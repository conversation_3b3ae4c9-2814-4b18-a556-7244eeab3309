CREATE TABLE `bill_asset` (
  `bill_id` BIGINT PRIMARY KEY COMMENT '账单ID',
  `uid` VARCHAR(20) NOT NULL COMMENT '用户ID',
  `symbol` VARCHAR(50) DEFAULT NULL COMMENT '合约代码',
  `currency` VARCHAR(25) NOT NULL COMMENT '资产币种',
  `bill_type` SMALLINT DEFAULT NULL COMMENT '账单类型',
  `flow_type` SMALLINT DEFAULT NULL COMMENT '流水方向（1-入账，2-出账）',
  `amount` DECIMAL(30,15) DEFAULT '0' COMMENT '数量',
  `balance` DECIMAL(30,15) DEFAULT '0' COMMENT '变动后余额',
  `funding_rate` DECIMAL(30,15) DEFAULT '0' COMMENT '资金费率',
  `mark_price` DECIMAL(30,15) DEFAULT '0' COMMENT '标记价格',
  `ref_id` VARCHAR(50) DEFAULT NULL COMMENT '关联业务单号',
  `from_pair` VARCHAR(20) DEFAULT NULL COMMENT '转出币对',
  `to_pair` VARCHAR(20) DEFAULT NULL COMMENT '转入币对',
  `from_wallet_type` SMALLINT DEFAULT NULL COMMENT '转出账户',
  `to_wallet_type` SMALLINT DEFAULT NULL COMMENT '转入账户',
  `recycle_id` VARCHAR(50) DEFAULT NULL COMMENT '体验金回收的唯一id',
  `create_time` BIGINT NOT NULL COMMENT '创建时间(时间戳)',
  INDEX `idx_user_id` (`uid`),
  INDEX `idx_currency` (`currency`),
  INDEX `idx_ref_id` (`ref_id`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT ='资产账单流水表' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
