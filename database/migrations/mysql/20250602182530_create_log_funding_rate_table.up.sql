CREATE TABLE `log_funding_rate` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    `symbol` VARCHAR(50) NOT NULL COMMENT '币对',
    `funding_rate` DECIMAL(30,15) DEFAULT '0' COMMENT '资金费率',
    `mark_price` DECIMAL(30,15) DEFAULT '0' COMMENT '标记价格',
    `index_price` DECIMAL(30,15) DEFAULT '0' COMMENT '指数价格',
    `funding_time` BIGINT NOT NULL DEFAULT '0' COMMENT '资金费时间',
    INDEX `idx_funding_time` (`funding_time`)
) ENGINE=InnoDB COMMENT ='资金费率记录' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;