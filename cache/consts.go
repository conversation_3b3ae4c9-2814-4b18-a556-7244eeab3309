package cache

import (
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type TaskType string

type (
	SwapBurstTask struct {
		Type     TaskType `json:"type"`
		Data     []byte   `json:"data"`
		FuncName string   `json:"func_name"`
	}
	LiquidationInfo struct {
		BurstId              string                 `json:"burstId"`
		UID                  string                 `json:"uid"`
		PosId                string                 `json:"posId"`
		MarginMode           domain.MarginMode      `json:"marginMode"`
		PosSide              int32                  `json:"posSide"`
		LiquidationType      domain.LiquidationType `json:"liquidationType"`
		CurrentLevel         int                    `json:"currentLevel"`
		TargetLevel          int                    `json:"targetLevel	"`
		TargetLimit          decimal.Decimal        `json:"targetLimit"`
		CancelType           domain.CancelType      `json:"cancelType"`
		CollapsePrice        decimal.Decimal        `json:"collapsePrice"`        // 破产价格
		CollapsePriceFormula string                 `json:"collapsePriceFormula"` // 破产价格公式
		BurstTime            int64                  `json:"burstTime"`
		IsTrialPos           bool                   `json:"isTrialPos"` // 是否体验金仓位
	}
)
