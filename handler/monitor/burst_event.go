package monitor

// import (
// 	"encoding/json"
// 	"fmt"
// 	"log"
// 	"strings"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachelock"
// 	"futures-asset/cache/price"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/service/coupling"
// 	"futures-asset/util"
// 	"futures-asset/util/modelutil"

// 	"github.com/sirupsen/logrus"

// 	"github.com/pkg/errors"
// 	"github.com/shopspring/decimal"
// )

// type BurstEvent struct {
// 	UID          string            `json:"uid"`          // 用户ID
// 	UserType     int32             `json:"userType"`     // 用户类型
// 	MarginMode   domain.MarginMode `json:"marginMode"`   // 仓位模式
// 	ContractCode string            `json:"contractCode"` // 合约代码
// 	PosId        string            `json:"posId"`        // 仓位ID
// 	PosSide      int32             `json:"posSide"`      // 仓位方向
// 	BurstId      string            `json:"burstId"`      // 爆仓ID
// 	BurstTime    int64             `json:"burstTime"`    // 爆仓时间
// 	IsTrialPos   bool              `json:"isTrialPos"`   // 是否体验金仓位

// 	ForceRivalAmount    decimal.Decimal     `json:"force_rival_amount"`  // 强制对手方数量(仅此文件中使用)
// 	LiquidationFee      decimal.Decimal     `json:"liquidationFee"`      // 强平费(仅此文件中使用)
// 	TrialLiquidationFee decimal.Decimal     `json:"trialLiquidationFee"` // 强平费(仅此文件中使用)
// 	Overflow            BurstOverflow       `json:"overflow"`            // 穿仓数量(仅此文件中使用)
// 	LiquidationFeeInfo  BurstLiquidationFee `json:"liquidationFeeInfo"`  // 强平费明细(仅此文件中使用)
// }

// type FeeInfo struct {
// 	Currency string          `json:"currency"`
// 	Price    decimal.Decimal `json:"price"`
// 	Amount   decimal.Decimal `json:"amount"`
// }
// type BurstOverflow []FeeInfo

// func (o BurstOverflow) TotalAmount() decimal.Decimal {
// 	total := decimal.Zero
// 	for _, info := range o {
// 		total = total.Add(info.Price.Mul(info.Amount))
// 	}
// 	return total
// }

// func (o BurstOverflow) String() string {
// 	temp, err := json.Marshal(o)
// 	if err != nil {
// 		logrus.Error(0, "BurstOverflow String Marshal error:", err, string(temp))
// 		return ""
// 	}
// 	return string(temp)
// }

// type BurstLiquidationFee []FeeInfo

// func (o BurstLiquidationFee) TotalAmount() decimal.Decimal {
// 	total := decimal.Zero
// 	for _, info := range o {
// 		total = total.Add(info.Price.Mul(info.Amount))
// 	}
// 	return total
// }

// func (o BurstLiquidationFee) String() string {
// 	temp, err := json.Marshal(o)
// 	if err != nil {
// 		logrus.Error(0, "BurstLiquidationFee String Marshal error:", err, string(temp))
// 		return ""
// 	}
// 	return string(temp)
// }

// type BurstReply struct {
// 	TrialLogs           []*entity.TrialAsset       // 体验金流水
// 	AssetLogs           []*repository.MqCmsAsset   // 财务流水
// 	BillAssets          []repository.BillAssetSync // 账单流水
// 	Overflow            BurstOverflow              // 穿仓补贴数量
// 	LiquidationFee      decimal.Decimal            // 强平费
// 	TrialLiquidationFee decimal.Decimal            // 强平费
// 	LiquidationFeeInfo  BurstLiquidationFee        // 强平费明细
// }

// func (slf *BurstEvent) Burst() error {
// 	// lock taker & maker
// 	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+slf.UID, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		return errors.New("lock err when burst user")
// 	}
// 	defer userMutex.Unlock()

// 	// 获取仓位
// 	base, quote := util.BaseQuote(slf.ContractCode)
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: repository.TradeCommon{
// 			Base:  base,
// 			Quote: quote,
// 		},
// 	}, slf.UID)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		return err
// 	}

// 	var reply BurstReply
// 	switch slf.MarginMode {
// 	case domain.MarginModeCross:
// 		reply, err = slf.burstCross(userCache, userAsset)
// 		log.Println("burstCross reply：", fmt.Sprintf("%+v", reply), err, fmt.Sprintf("%+v", slf))
// 		if err != nil {
// 			return err
// 		}
// 		// 解锁爆仓锁
// 		err = cachelock.UnlockBurstUser(repository.BurstLockParam{
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        slf.UID,
// 				MarginMode: domain.MarginModeCross,
// 			},
// 		}, true)
// 		if err != nil {
// 			log.Printf("Unlock Burst User err:%s", err.Error())
// 		}

// 	case domain.MarginModeIsolated:

// 		if slf.IsTrialPos {
// 			// 爆体验金仓位
// 			reply, err = slf.burstIsolatedTrial(userCache, userAsset)
// 		} else {
// 			// 爆真实仓位
// 			reply, err = slf.burstIsolated(userCache, userAsset)
// 		}

// 		log.Println("burstIsolated reply：", fmt.Sprintf("%+v", reply), err, fmt.Sprintf("%+v", slf))
// 		if err != nil {
// 			return err
// 		}
// 		// 解锁爆仓锁
// 		err = cachelock.UnlockBurstUser(repository.BurstLockParam{
// 			ContractCode: slf.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        slf.UID,
// 				MarginMode: domain.MarginModeIsolated,
// 				PosSide:    slf.PosSide,
// 				IsTrialPos: slf.IsTrialPos,
// 			},
// 		}, false)
// 		if err != nil {
// 			log.Printf("Unlock Burst User err:%s", err.Error())
// 		}
// 	default:
// 		log.Printf("margin mode error: %d", slf.MarginMode)
// 	}

// 	if reply.Overflow.TotalAmount().Sign() > 0 {
// 		// 如果穿仓数量大于0, 更新爆仓状态 & 是否穿仓 & 穿仓数量
// 		slf.Overflow = reply.Overflow
// 		// 序列化发送更新数据到队列, burst_time服务中处理更新逻辑(使用反射的调用方式)
// 		jsonBytes, _ := json.Marshal(slf)
// 		swapcache.SendSwapTask(slf.ContractCode, cache.SwapBurstTask{
// 			Type:     cache.TaskTypeBurstEvent,
// 			Data:     jsonBytes,
// 			FuncName: "UpdateBurstOverflow",
// 		})
// 	} else {
// 		slf.LiquidationFee = reply.LiquidationFee
// 		slf.TrialLiquidationFee = reply.TrialLiquidationFee
// 		slf.LiquidationFeeInfo = reply.LiquidationFeeInfo
// 		// 序列化发送更新数据到队列, burst_time服务中处理更新逻辑(使用反射的调用方式)
// 		jsonBytes, _ := json.Marshal(slf)
// 		swapcache.SendSwapTask(slf.ContractCode, cache.SwapBurstTask{
// 			Type:     cache.TaskTypeBurstEvent,
// 			Data:     jsonBytes,
// 			FuncName: "UpdateBurstStatus",
// 		})
// 	}

// 	// 开启协程进行推送
// 	go func() {
// 		redis := redislib.Redis()
// 		coupling.AddAssetLogs(redis, reply.AssetLogs...)
// 		coupling.AddBills(redis, reply.BillAssets...)
// 		coupling.AddTrialChange(redis, reply.TrialLogs)
// 	}()

// 	return nil
// }

// func (slf *BurstEvent) burstCross(userCache *swapcache.PosCache, asset *repository.AssetSwap) (BurstReply, error) {
// 	_, quote := util.BaseQuote(slf.ContractCode)

// 	// burstProfit := decimal.Zero     // 爆仓亏损数量(统计使用)
// 	// burstTotalClose := decimal.Zero // 爆仓平仓仓位价值(统计使用)

// 	reply := BurstReply{
// 		TrialLogs:          make([]*entity.TrialAsset, 0),
// 		AssetLogs:          make([]*repository.MqCmsAsset, 0),
// 		BillAssets:         make([]repository.BillAssetSync, 0),
// 		Overflow:           make([]FeeInfo, 0),
// 		LiquidationFeeInfo: make([]FeeInfo, 0),
// 	}

// 	// 机器人不处理爆仓强平和穿仓补贴
// 	if slf.UserType != domain.UserTypePlatformRobot {
// 		marginCurrencyList := []string{quote}
// 		isJoinMargin := asset.AssetMode == domain.AssetMode
// 		if isJoinMargin {
// 			marginCurrencyList = domain.CurrencyList
// 		}

// 		pCache := price.New()

// 		for _, currency := range marginCurrencyList {
// 			liquidationFee := &repository.MqCmsAsset{
// 				ContractCode: slf.ContractCode,
// 				Currency:     strings.ToUpper(currency),
// 				OrderId:      slf.BurstId,
// 				UID:          slf.UID,
// 				OperateTime:  time.Now().UnixNano(),
// 			}
// 			isolateTotalMargin := userCache.HoldCostTotalIsolated(liquidationFee.Currency, true)
// 			liquidationFee.Amount = asset.CBalance(liquidationFee.Currency).Sub(isolateTotalMargin)

// 			trialBalance := asset.TrialCBalance(liquidationFee.Currency)
// 			// 体验金填补穿仓保证金（不记流水）
// 			if trialBalance.GreaterThan(decimal.Zero) && liquidationFee.Amount.Sign() < 0 {
// 				if liquidationFee.Amount.Abs().GreaterThanOrEqual(trialBalance) {
// 					// 穿仓数量 >= 体验金数量，使用全部体验金进行填补
// 					liquidationFee.Amount = liquidationFee.Amount.Add(trialBalance)
// 					asset.AddBalance(liquidationFee.Currency, trialBalance.Neg())
// 				} else {
// 					// 穿仓数量 < 体验金数量，从体验金中填补穿仓数量
// 					liquidationFee.Amount = liquidationFee.Amount.Add(liquidationFee.Amount.Neg())
// 					asset.AddBalance(liquidationFee.Currency, liquidationFee.Amount)
// 				}
// 				// 体验金变更过，需要刷新
// 				trialBalance = asset.TrialCBalance(liquidationFee.Currency)
// 			}

// 			if liquidationFee.Amount.Sign() > 0 {
// 				// 账户余额中减去强平的费用
// 				asset.AddBalance(liquidationFee.Currency, liquidationFee.Amount.Neg())
// 				liquidationFee.OperateType = domain.BillTypePlatFee
// 				reply.LiquidationFee = liquidationFee.Amount

// 				reply.AssetLogs = append(reply.AssetLogs, liquidationFee)

// 				billAsset := repository.NewBillAssetSync(liquidationFee, domain.BillTypePlatFee, liquidationFee.Amount.Neg())
// 				reply.BillAssets = append(reply.BillAssets, *billAsset)

// 				// 获取保证金汇率
// 				rate := decimal.NewFromInt(1)
// 				if isJoinMargin {
// 					rate = pCache.SpotURate(liquidationFee.Currency)
// 				}
// 				reply.LiquidationFeeInfo = append(reply.LiquidationFeeInfo, FeeInfo{
// 					Currency: liquidationFee.Currency,
// 					Price:    rate,
// 					Amount:   liquidationFee.Amount,
// 				})

// 				log.Printf("user burst, risk received liquidation fee %s %s from user: %s", liquidationFee.Amount, liquidationFee.Currency, slf.UID)
// 			} else if liquidationFee.Amount.Sign() < 0 {
// 				// 发生穿仓, 风险准备金钱包补齐
// 				liquidationFee.Amount = liquidationFee.Amount.Abs()
// 				asset.AddBalance(liquidationFee.Currency, liquidationFee.Amount)
// 				liquidationFee.OperateType = domain.BillTypeSubsidy
// 				reply.AssetLogs = append(reply.AssetLogs, liquidationFee)

// 				// 获取保证金汇率
// 				rate := decimal.NewFromInt(1)
// 				if isJoinMargin {
// 					rate = pCache.SpotURate(liquidationFee.Currency)
// 				}

// 				billAsset := repository.NewBillAssetSync(liquidationFee, domain.BillTypeSubsidy, liquidationFee.Amount)
// 				reply.BillAssets = append(reply.BillAssets, *billAsset)
// 				reply.Overflow = append(reply.Overflow, FeeInfo{
// 					Currency: billAsset.Currency,
// 					Price:    rate,
// 					Amount:   liquidationFee.Amount, // 爆仓单穿仓数量
// 				})

// 				log.Printf("the balance of user drop below zero, value: %s %s, uid: %s", liquidationFee.Amount, liquidationFee.Currency, slf.UID)
// 			}

// 			if trialBalance.Sign() > 0 {
// 				trialLiquidationFee := &repository.MqCmsAsset{
// 					ContractCode: slf.ContractCode,
// 					Currency:     strings.ToUpper(currency),
// 					OrderId:      slf.BurstId,
// 					UID:          slf.UID,
// 					OperateTime:  time.Now().UnixNano(),
// 				}
// 				trialLiquidationFee.Amount = trialBalance

// 				// 体验金余额中减去强平的费用
// 				asset.AddTrialBalance(trialLiquidationFee.Currency, trialLiquidationFee.Amount.Neg())

// 				trialLiquidationFee.OperateType = domain.BillTypePlatFeeTrial
// 				reply.TrialLiquidationFee = trialLiquidationFee.Amount

// 				trialBillList := asset.ConsumeTrialBalance(trialLiquidationFee.Currency, trialLiquidationFee.Amount)
// 				reply.AssetLogs = append(reply.AssetLogs, trialLiquidationFee)
// 				reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

// 				// 获取保证金汇率
// 				rate := decimal.NewFromInt(1)
// 				if isJoinMargin {
// 					rate = pCache.SpotURate(trialLiquidationFee.Currency)
// 				}
// 				reply.LiquidationFeeInfo = append(reply.LiquidationFeeInfo, FeeInfo{
// 					Currency: trialLiquidationFee.Currency,
// 					Price:    rate,
// 					Amount:   trialLiquidationFee.Amount,
// 				})

// 				log.Printf("user burst, risk received trial liquidation fee %s %s from user: %s", trialLiquidationFee.Amount, trialLiquidationFee.Currency, slf.UID)
// 			}
// 		}
// 	}

// 	balance, _ := json.Marshal(asset.Balance)
// 	if asset.TrialDetail == nil {
// 		asset.TrialDetail = repository.TrialTimeList{}
// 	}
// 	trialDetail, _ := json.Marshal(asset.TrialDetail)
// 	// update asset cache
// 	fields := map[string]interface{}{
// 		userCache.BalanceKey:     string(balance),
// 		userCache.TrialDetailKey: string(trialDetail),
// 	}

// 	posChangeList := make([]*repository.LogPosSync, 0)
// 	timeNow := time.Now().UnixNano()
// 	for code, posList := range userCache.CrossList {
// 		for _, pos := range posList {
// 			if pos.Pos.Sign() <= 0 {
// 				continue
// 			}
// 			// 清空仓位数据
// 			tmpPos := pos.Pos
// 			pos.Clear()
// 			posStr, _ := json.Marshal(pos)
// 			fieldKey := fmt.Sprintf("%s%s", strings.ToUpper(code), cache.LongPosSuffix)
// 			var side int32 = domain.Sell
// 			if pos.PosSide == domain.ShortPos {
// 				fieldKey = fmt.Sprintf("%s%s", strings.ToUpper(code), cache.ShortPosSuffix)
// 				side = domain.Buy
// 			}
// 			fields[fieldKey] = posStr

// 			// 记录推送日志
// 			posChange := userCache.NewLogPosSync(pos, timeNow, "", slf.BurstId,
// 				side, pos.PosSide, tmpPos, decimal.Zero)
// 			posChangeList = append(posChangeList, posChange)
// 		}
// 	}
// 	err := redislib.Redis().HMSet(userCache.HashKey, fields)
// 	if err != nil {
// 		msg := fmt.Sprintf("hmset user %v err: %v, data: %+v", "slf.UID", err, fields)
// 		return reply, errors.New(msg)
// 	}
// 	assetChange := modelutil.NewLogAssetSync(asset, quote, timeNow)
// 	go func() {
// 		redis := redislib.Redis()
// 		assetChangeList := []*repository.LogAssetSync{assetChange}
// 		coupling.TradeAssetPosToMq(redis, assetChangeList, posChangeList)
// 	}()

// 	return reply, nil
// }

// func (slf *BurstEvent) burstIsolated(userCache *swapcache.PosCache, asset *repository.AssetSwap) (BurstReply, error) {
// 	_, quote := util.BaseQuote(slf.ContractCode)
// 	reply := BurstReply{
// 		TrialLogs:          make([]*entity.TrialAsset, 0),
// 		AssetLogs:          make([]*repository.MqCmsAsset, 0),
// 		BillAssets:         make([]repository.BillAssetSync, 0),
// 		Overflow:           make([]FeeInfo, 0),
// 		LiquidationFeeInfo: make([]FeeInfo, 0),
// 	}

// 	fee := &repository.MqCmsAsset{
// 		ContractCode: slf.ContractCode,
// 		Currency:     quote,
// 		OrderId:      slf.BurstId,
// 		UID:          slf.UID,
// 		OperateTime:  time.Now().UnixNano(),
// 	}

// 	pos := asset.LongPos
// 	fieldKey := fmt.Sprintf("%s%s", strings.ToUpper(slf.ContractCode), cache.LongPosSuffix)
// 	var side int32 = domain.Sell
// 	if slf.PosSide == domain.ShortPos {
// 		pos = asset.ShortPos
// 		fieldKey = fmt.Sprintf("%s%s", strings.ToUpper(slf.ContractCode), cache.ShortPosSuffix)
// 		side = domain.Buy
// 	}
// 	tmpPosStr, _ := json.Marshal(pos)
// 	logrus.Infof("user burst pos %s from user: %s", string(tmpPosStr), slf.UID)
// 	// 机器人不处理爆仓强平和穿仓补贴
// 	if slf.UserType != domain.UserTypePlatformRobot {
// 		fee.Amount = pos.IsolatedMargin

// 		if fee.Amount.IsPositive() {
// 			// 资产扣减
// 			asset.AddBalance(quote, fee.Amount.Neg())
// 			fee.OperateType = domain.BillTypePlatFee
// 			reply.LiquidationFee = fee.Amount
// 			reply.AssetLogs = append(reply.AssetLogs, fee)

// 			billAsset := repository.NewBillAssetSync(fee, domain.BillTypePlatFee, fee.Amount.Neg())
// 			reply.BillAssets = append(reply.BillAssets, *billAsset)

// 			reply.LiquidationFeeInfo = append(reply.LiquidationFeeInfo, FeeInfo{
// 				Currency: fee.Currency,
// 				Price:    decimal.NewFromInt(1),
// 				Amount:   fee.Amount,
// 			})
// 			logrus.Infof("user burst, risk received liquidation fee %s %s from user: %s", fee.Amount, fee.Currency, slf.UID)
// 		} else if fee.Amount.IsNegative() { // 发生穿仓, 风险准备金钱包补齐
// 			fee.Amount = fee.Amount.Abs()
// 			fee.OperateType = domain.BillTypeSubsidy
// 			reply.AssetLogs = append(reply.AssetLogs, fee)

// 			billAsset := repository.NewBillAssetSync(fee, domain.BillTypeSubsidy, fee.Amount)
// 			reply.BillAssets = append(reply.BillAssets, *billAsset)

// 			asset.AddBalance(quote, fee.Amount) // 账户余额需要加上穿仓补贴的费用
// 			reply.Overflow = append(reply.Overflow, FeeInfo{
// 				Currency: billAsset.Currency,
// 				Price:    decimal.NewFromInt(1),
// 				Amount:   billAsset.Amount,
// 			}) // 爆仓单穿仓数量
// 			logrus.Infof("the balance of user drop below zero, value: %s %s, uid: %s", fee.Amount, fee.Currency, slf.UID)
// 		}

// 		// 跳过旧体验金逻辑
// 		// if pos.TrialMargin.Sign() {
// 		if false {
// 			trialLiquidationFee := &repository.MqCmsAsset{
// 				ContractCode: slf.ContractCode,
// 				Currency:     strings.ToUpper(quote),
// 				OrderId:      slf.BurstId,
// 				UID:          slf.UID,
// 				OperateTime:  time.Now().UnixNano(),
// 			}
// 			trialLiquidationFee.Amount = pos.TrialMargin

// 			// 体验金余额中减去强平的费用
// 			pos.TrialMargin = pos.TrialMargin.Add(trialLiquidationFee.Amount.Neg())

// 			trialLiquidationFee.OperateType = domain.BillTypePlatFeeTrial
// 			reply.TrialLiquidationFee = trialLiquidationFee.Amount

// 			trialBillList := asset.ConsumeTrialBalance(trialLiquidationFee.Currency, trialLiquidationFee.Amount)
// 			reply.AssetLogs = append(reply.AssetLogs, trialLiquidationFee)
// 			reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

// 			// 获取保证金汇率
// 			rate := decimal.NewFromInt(1)

// 			reply.LiquidationFeeInfo = append(reply.LiquidationFeeInfo, FeeInfo{
// 				Currency: trialLiquidationFee.Currency,
// 				Price:    rate,
// 				Amount:   trialLiquidationFee.Amount,
// 			})
// 			log.Printf("user burst, risk received trial liquidation fee %s %s from user: %s", trialLiquidationFee.Amount, trialLiquidationFee.Currency, slf.UID)
// 		}
// 	}

// 	balance, _ := json.Marshal(asset.Balance)
// 	if asset.TrialDetail == nil {
// 		asset.TrialDetail = repository.TrialTimeList{}
// 	}
// 	trialDetail, _ := json.Marshal(asset.TrialDetail)
// 	// update asset cache
// 	fields := map[string]interface{}{
// 		userCache.BalanceKey:     balance,
// 		userCache.TrialDetailKey: trialDetail,
// 	}

// 	timeNow := time.Now().UnixNano()
// 	// 清空仓位数据
// 	tmpPos := pos.Pos
// 	pos.Clear()
// 	posStr, _ := json.Marshal(pos)
// 	fields[fieldKey] = posStr
// 	err := redislib.Redis().HMSet(userCache.HashKey, fields)
// 	if err != nil {
// 		msg := fmt.Sprintf("hmset user %v err: %v", "slf.UID", err)
// 		return reply, errors.New(msg)
// 	}

// 	assetChange := modelutil.NewLogAssetSync(asset, quote, timeNow)
// 	posChange := userCache.NewLogPosSync(pos, timeNow, "", slf.BurstId,
// 		side, pos.PosSide, tmpPos, decimal.Zero)
// 	go func() {
// 		redis := redislib.Redis()
// 		assetChangeList := []*repository.LogAssetSync{assetChange}
// 		posChangeList := []*repository.LogPosSync{posChange}
// 		coupling.TradeAssetPosToMq(redis, assetChangeList, posChangeList)
// 	}()
// 	return reply, nil
// }

// // burstIsolatedTrial 爆仓逐仓体验金仓位
// func (slf *BurstEvent) burstIsolatedTrial(userCache *swapcache.PosCache, asset *repository.AssetSwap) (BurstReply, error) {
// 	_, quote := util.BaseQuote(slf.ContractCode)
// 	reply := BurstReply{
// 		TrialLogs:          make([]*entity.TrialAsset, 0),       // 体验金资产日志记录
// 		AssetLogs:          make([]*repository.MqCmsAsset, 0),   // 财务日志记录
// 		BillAssets:         make([]repository.BillAssetSync, 0), // 账单日志记录
// 		Overflow:           make([]FeeInfo, 0),
// 		LiquidationFeeInfo: make([]FeeInfo, 0),
// 	}

// 	platFeeTrialAssetLog := &repository.MqCmsAsset{
// 		ContractCode: slf.ContractCode,
// 		Currency:     quote,
// 		OrderId:      slf.BurstId,
// 		UID:          slf.UID,
// 		OperateTime:  time.Now().UnixNano(),
// 	}

// 	pos := asset.TrialLongPos
// 	trialPosKey := userCache.TrialLongPosKey
// 	var side int32 = domain.Sell
// 	if slf.PosSide == domain.ShortPos {
// 		pos = asset.TrialShortPos
// 		trialPosKey = userCache.TrialShortPosKey
// 		side = domain.Buy
// 	}

// 	burstAmount := pos.IsolatedMargin
// 	tmpPosStr, _ := json.Marshal(pos)
// 	logrus.Infof("user burst trial pos %s from user: %s", string(tmpPosStr), slf.UID)
// 	// 机器人不处理爆仓强平和穿仓补贴
// 	if slf.UserType != domain.UserTypePlatformRobot {
// 		trialList := asset.TrialDetail.Gets(pos.AwardIds...)
// 		if burstAmount.IsPositive() {
// 			// 亏损保证金
// 			trialList.LossAmount(burstAmount)
// 			asset.AddTrialBalance(quote, burstAmount.Neg())
// 			asset.AddTrialConsume(quote, burstAmount)

// 			platFeeTrialAssetLog.Amount = burstAmount.Neg()
// 			platFeeTrialAssetLog.TAsset.TAmount = burstAmount.Neg()
// 			platFeeTrialAssetLog.TAsset.TDetail = trialList

// 			platFeeTrialAssetLog.OperateType = domain.BillTypePlatFeeTrial
// 			reply.LiquidationFee = burstAmount
// 			reply.AssetLogs = append(reply.AssetLogs, platFeeTrialAssetLog)

// 			billAsset := repository.NewBillAssetSync(platFeeTrialAssetLog, domain.BillTypePlatFeeTrial, burstAmount.Neg())
// 			reply.BillAssets = append(reply.BillAssets, *billAsset)

// 			reply.LiquidationFeeInfo = append(reply.LiquidationFeeInfo, FeeInfo{
// 				Currency: quote,
// 				Price:    decimal.NewFromInt(1),
// 				Amount:   burstAmount,
// 			})

// 			logrus.Infof("user burst, risk received liquidation fee %s %s from user: %s", burstAmount, quote, slf.UID)
// 		} else if burstAmount.IsNegative() {
// 			// todo::体验金仓位穿仓不需要补贴
// 			// 发生穿仓, 风险准备金钱包补齐
// 			// burstAmount = burstAmount.Abs()
// 			// platFeeTrialAssetLog.Amount = burstAmount
// 			// platFeeTrialAssetLog.OperateType = domain.BillTypePlatFeeTrial
// 			// reply.AssetLogs = append(reply.AssetLogs, platFeeTrialAssetLog)

// 			// billAsset := repository.NewBillAssetSync(platFeeTrialAssetLog, domain.BillTypeSubsidy, burstAmount)
// 			// reply.BillAssets = append(reply.BillAssets, *billAsset)

// 			// 账户余额需要加上穿仓补贴的费用
// 			// asset.AddBalance(quote, platFeeTrialAssetLog.Amount)
// 			// reply.Overflow = append(reply.Overflow, FeeInfo{
// 			// 	Currency: billAsset.Currency,
// 			// 	Price:    decimal.NewFromInt(1),
// 			// 	Amount:   billAsset.Amount,
// 			// }) // 爆仓单穿仓数量
// 			logrus.Infof("the balance of user drop below zero, value: %s %s, uid: %s", burstAmount, quote, slf.UID)
// 		}

// 		// 如果还有剩余保证金,退回到剩余体验金中
// 		if trialList.GetOpenAmount().IsPositive() {
// 			trialList.SubOpenAmount(trialList.GetOpenAmount())
// 		}

// 		// 回收一次性体验金流水
// 		recoveryAmount := trialList.Recovery()
// 		// 如果回收金额存在,回收体验金
// 		if recoveryAmount.IsPositive() {
// 			asset.AddTrialBalance(quote, recoveryAmount.Neg())
// 			asset.AddTrialConsume(quote, recoveryAmount) // 回收体验金
// 			recoveryAssetLogs := &repository.MqCmsAsset{
// 				ContractCode: slf.ContractCode,
// 				Currency:     quote,
// 				OrderId:      slf.BurstId,
// 				UID:          slf.UID,
// 				Amount:       recoveryAmount.Neg(),
// 				OperateTime:  time.Now().UnixNano(),
// 			}
// 			recoveryAssetLogs.TAsset.TAmount = recoveryAmount.Neg()
// 			recoveryAssetLogs.TAsset.TDetail = trialList
// 			recoveryAssetLogs.OperateType = domain.BillTrialAssetRecycle
// 			reply.AssetLogs = append(reply.AssetLogs, recoveryAssetLogs)

// 			billAsset := repository.NewBillAssetSync(recoveryAssetLogs, domain.BillTrialAssetRecycle, recoveryAmount.Neg())
// 			reply.BillAssets = append(reply.BillAssets, *billAsset)
// 		}

// 		reply.TrialLogs = append(reply.TrialLogs, trialList...)
// 	}

// 	// 备份当前仓位
// 	tmpPos := pos.Pos

// 	// 清空仓位数据
// 	pos.Clear()
// 	posStr, _ := json.Marshal(pos)

// 	// 用户体验金数据
// 	if asset.TrialDetail == nil {
// 		asset.TrialDetail = repository.TrialTimeList{}
// 	}
// 	realBalance, _ := json.Marshal(asset.Balance)
// 	trialDetail, _ := json.Marshal(asset.TrialDetail)
// 	trialBalance, _ := json.Marshal(asset.TrialBalance)
// 	trialConsume, _ := json.Marshal(asset.TrialConsume)

// 	// 更新缓存中用户仓位与资产信息
// 	fields := map[string]interface{}{
// 		userCache.BalanceKey:      realBalance,
// 		userCache.TrialDetailKey:  trialDetail,
// 		userCache.TrialBalanceKey: trialBalance,
// 		userCache.TrialConsumeKey: trialConsume,
// 		trialPosKey:               posStr,
// 	}
// 	err := redislib.Redis().HMSet(userCache.HashKey, fields)
// 	if err != nil {
// 		msg := fmt.Sprintf("hmset user %v err: %v", "slf.UID", err)
// 		return reply, errors.New(msg)
// 	}

// 	timeNow := time.Now().UnixNano()
// 	assetChange := modelutil.NewLogAssetSync(asset, quote, timeNow)
// 	posChange := userCache.NewLogPosSync(pos, timeNow, "", slf.BurstId, side, pos.PosSide, tmpPos, decimal.Zero)
// 	go func() {
// 		redis := redislib.Redis()
// 		assetChangeList := []*repository.LogAssetSync{assetChange}
// 		posChangeList := []*repository.LogPosSync{posChange}
// 		coupling.TradeAssetPosToMq(redis, assetChangeList, posChangeList)
// 	}()
// 	return reply, nil
// }

// // UpdateBurstStatus 更新爆仓单状态
// //
// //	此函数以反射方式被调用，搜索引用时请使用函数名称全文搜索  (FuncName: "UpdateBurstStatus")
// func (slf *BurstEvent) UpdateBurstStatus() {
// 	// update db status
// 	burstSwap := &entity.BurstSwap{
// 		UID:                slf.UID,
// 		BurstId:            slf.BurstId,
// 		BurstTime:          slf.BurstTime,
// 		LiquidationFee:     slf.LiquidationFee,
// 		LiquidationFeeInfo: slf.LiquidationFeeInfo.String(),
// 		Status:             domain.BurstFinished,
// 	}
// 	if err := burstSwap.UpdateStatus(nil); err != nil {
// 		logrus.Error("Update burst_swap status in mysql err:" + err.Error())
// 	}
// 	log.Printf("successed update user burst status. burst: %+v", *slf)
// }

// // UpdateBurstOverflow 更新爆仓单是否穿仓
// //
// //	此函数以反射方式被调用，搜索引用时请使用函数名称全文搜索  (FuncName: "UpdateBurstOverflow")
// func (slf *BurstEvent) UpdateBurstOverflow() {
// 	// update db status
// 	burstSwap := &entity.BurstSwap{
// 		UID:           slf.UID,
// 		BurstId:       slf.BurstId,
// 		BurstTime:     slf.BurstTime,
// 		Status:        domain.BurstFinished,
// 		IsOverflow:    domain.Overflowed,
// 		OverflowInfo:  slf.Overflow.String(),
// 		OverflowValue: slf.Overflow.TotalAmount(),
// 		OverflowTime:  time.Now().UnixNano(),
// 	}
// 	if err := burstSwap.UpdateOverflow(nil); err != nil {
// 		logrus.Error("Update burst_swap is_overflow in mysql err:" + err.Error())
// 	}
// 	log.Printf("successed update user burst is_overflow. burst: %+v", *slf)
// 	if len(slf.PosId) != 0 {
// 		posSwap := &entity.Position{
// 			Id:      slf.PosId,
// 			UID:     slf.UID,
// 			Subsidy: slf.Overflow.TotalAmount(),
// 		}
// 		if err := posSwap.UpdateSubsidy(nil); err != nil {
// 			log.Printf("ERROR update pos_swap subsidy [err:%s] [posId:%s uid:%s subsidy:%s]", err.Error(), posSwap.Id, posSwap.UID, posSwap.Subsidy)
// 			return
// 		}
// 		log.Printf("successed update pos_swap subsidy.[posId:%s uid:%s subsidy:%s]", posSwap.Id, posSwap.UID, posSwap.Subsidy)
// 	}
// }

// // UpdateBurstIsForceRival 更新爆仓单是否强制减仓
// //
// //	此函数以反射方式被调用，搜索引用时请使用函数名称全文搜索  (FuncName: "UpdateBurstIsForceRival")
// func (slf *BurstEvent) UpdateBurstIsForceRival() {
// 	// update db status
// 	burstSwap := &entity.BurstSwap{
// 		UID:              slf.UID,
// 		BurstId:          slf.BurstId,
// 		BurstTime:        slf.BurstTime,
// 		IsForceRival:     domain.ForceRivaled,
// 		ForceRivalAmount: slf.ForceRivalAmount,
// 	}
// 	if err := burstSwap.UpdateIsForceRival(nil); err != nil {
// 		logrus.Error("Update burst_swap is_force_rival in mysql err:" + err.Error())
// 	}
// 	log.Printf("successed update user burst is_force_rival. burst: %+v", *slf)
// }
