###########
# BUILDER #
###########

FROM golang:1.23 as builder

ARG VERSION
ARG BUILT
ARG GIT_COMMIT

RUN apt-get update && \
    apt-get -y install git

WORKDIR /app

COPY . .

# Download public key for yt.com
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan yt.com >> ~/.ssh/known_hosts

# Forces the usage of git and ssh key fwded by ssh-agent for yt.com git repos
RUN git config --global url."**********:".insteadOf "https://yt.com"

# private go packages
ENV GOPRIVATE=yt.com

RUN --mount=type=ssh go mod download

ENV FLAG="-s -w -X main.Version=${VERSION} -X main.Built=${BUILT} -X main.GitCommit=${GIT_COMMIT}"

RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build \
    -ldflags "$FLAG" \
    -o /server ./cmd/futures-asset

#########
# FINAL #
#########

FROM oraclelinux:9-slim AS final

WORKDIR /app

COPY --from=builder /server /app/server
COPY --from=builder /app/configs/config-example.yaml /app/config.yaml

CMD ["/app/server"]
