package coupling

// import (
// 	"encoding/json"
// 	"fmt"
// 	"net/http"
// 	"strconv"
// 	"time"

// 	"futures-asset/cache/price"
// 	"futures-asset/cache/swapcache"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/service/coupling/liquidation"
// 	"futures-asset/service/persist"
// 	"futures-asset/util"
// 	"futures-asset/util/modelutil"

// 	"fameex.dev/YT/fameex_common/riskclient"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// type Trade struct{}

// func (slf *Trade) Trade(_req *payload.TradeParam) (domain.Code, payload.TradeReply, error) {
// 	var (
// 		takerCache  *swapcache.PosCache
// 		makerCache  *swapcache.PosCache
// 		takerAsset  *repository.AssetSwap
// 		makerAsset  *repository.AssetSwap
// 		takerReply  payload.PosReply
// 		makerReply  payload.PosReply
// 		assetLogs   []*repository.MqCmsAsset
// 		changeTrial []*entity.TrialAsset // 有变动的 体验金，同步数据库用
// 		billAsset   []repository.BillAssetSync
// 		err         error
// 		code        domain.Code
// 	)
// 	replyDto := payload.TradeReply{
// 		TradeId:      _req.TradeId,
// 		TradeType:    _req.TradeType,
// 		ContractCode: _req.ContractCode(),
// 	}
// 	pCache := price.New()
// 	if _req.Taker.UID == _req.Maker.UID {
// 		selfMutex := redislib.NewMutex(domain.MutexSwapPosLock+_req.Taker.UID, 30*time.Second)
// 		if selfMutex.Lock() != nil {
// 			return domain.Code251101, replyDto, domain.ErrLockPos
// 		}
// 		defer selfMutex.Unlock()

// 		takerCache = swapcache.NewPosCache(swapcache.CacheParam{
// 			TradeCommon: repository.TradeCommon{
// 				Base:  _req.TradeCommon.Base,
// 				Quote: _req.TradeCommon.Quote,
// 			},
// 		}, _req.Taker.UID)
// 		makerCache = takerCache
// 		takerAsset, err = takerCache.Load()
// 		if err != nil {
// 			return domain.Code252404, replyDto, err
// 		}
// 		makerAsset = takerAsset
// 		// 机器人单向持仓自成交判断
// 		if _req.Taker.UserType == domain.UserTypePlatformRobot {
// 			_req.Taker.IsRobotSelfTrade = true
// 			_req.Maker.IsRobotSelfTrade = true
// 		}
// 	} else {
// 		// lock taker & maker
// 		takerMutex := redislib.NewMutex(domain.MutexSwapPosLock+_req.Taker.UID, 30*time.Second)
// 		if takerMutex.Lock() != nil {
// 			return domain.Code251101, replyDto, domain.ErrLockPos
// 		}
// 		defer takerMutex.Unlock()
// 		makerMutex := redislib.NewMutex(domain.MutexSwapPosLock+_req.Maker.UID, 30*time.Second)
// 		if makerMutex.Lock() != nil {
// 			return domain.Code251101, replyDto, domain.ErrLockPos
// 		}
// 		defer makerMutex.Unlock()
// 		takerCache = swapcache.NewPosCache(swapcache.CacheParam{
// 			TradeCommon: repository.TradeCommon{
// 				Base:  _req.TradeCommon.Base,
// 				Quote: _req.TradeCommon.Quote,
// 			},
// 		}, _req.Taker.UID)
// 		makerCache = swapcache.NewPosCache(swapcache.CacheParam{
// 			TradeCommon: repository.TradeCommon{
// 				Base:  _req.TradeCommon.Base,
// 				Quote: _req.TradeCommon.Quote,
// 			},
// 		}, _req.Maker.UID)
// 		// taker平仓资产变动(变动已实现盈亏,可用,账户余额)
// 		takerAsset, err = takerCache.Load()
// 		if err != nil {
// 			return domain.Code252404, replyDto, err
// 		}
// 		// maker平仓资产变动(变动已实现盈亏,可用,账户余额)
// 		makerAsset, err = makerCache.Load()
// 		if err != nil {
// 			return domain.Code252404, replyDto, err
// 		}
// 	}
// 	revertTakerAsset := new(repository.AssetSwap) // 用于回滚操作

// 	tempBytes, _ := json.Marshal(takerAsset)
// 	_ = json.Unmarshal(tempBytes, revertTakerAsset)

// 	// taker Process
// 	takerReply, code, err = slf.Process(*_req, _req.Taker, takerAsset, takerCache, pCache)
// 	if err != nil {
// 		return code, replyDto, err
// 	}
// 	// maker Process
// 	// 如果是单向持仓自成交, 处理Maker时需要重新加载仓位数据后再进行成交处理, 否则机器人仓位一直变化
// 	if _req.Taker.UID == _req.Maker.UID && makerAsset.PositionMode == domain.HoldModeBoth {
// 		makerAsset, err = makerCache.Load()
// 		if err != nil {
// 			logrus.Errorln(fmt.Sprintf("self trade makerCache.Load error: %s", err))
// 			makerAsset = takerAsset
// 		}
// 	}
// 	makerReply, code, err = slf.Process(*_req, _req.Maker, makerAsset, makerCache, pCache)
// 	if err != nil {
// 		revertErr := takerCache.UpdatePosAndAsset(*revertTakerAsset)
// 		if revertErr != nil {
// 			logrus.Errorf("revert hmset hash: %s with err: %v", takerCache.HashKey, err)
// 		}
// 		return code, replyDto, err
// 	}

// 	assetLogs = append(assetLogs, takerReply.AssetLogs...)
// 	billAsset = append(billAsset, takerReply.BillAssetLogs...)
// 	changeTrial = append(changeTrial, takerReply.TrialLogs...)
// 	assetLogs = append(assetLogs, makerReply.AssetLogs...)
// 	billAsset = append(billAsset, makerReply.BillAssetLogs...)
// 	changeTrial = append(changeTrial, makerReply.TrialLogs...)

// 	go func() {
// 		if _req.Taker.UID == _req.Maker.UID {
// 			if err = takerAsset.UpdateUserHoldPos(takerAsset.LongPos, takerAsset.ShortPos, takerAsset.BothPos, _req.Taker.UserType, false); err != nil {
// 				logrus.Errorf("self trade update hold pos err:%v, hash key:%s", err, takerCache.HashKey)
// 			}
// 			if err = takerAsset.UpdateUserHoldPos(takerAsset.TrialLongPos, takerAsset.TrialShortPos, takerAsset.TrialBothPos, _req.Taker.UserType, true); err != nil {
// 				logrus.Errorf("self trade update trial hold pos err:%v, hash key:%s", err, takerCache.HashKey)
// 			}
// 		} else {
// 			// 更新用户持仓信息
// 			if err = takerAsset.UpdateUserHoldPos(takerAsset.LongPos, takerAsset.ShortPos, takerAsset.BothPos, _req.Taker.UserType, false); err != nil {
// 				logrus.Errorf("taker trade update hold pos err:%v, hash key:%s", err, takerCache.HashKey)
// 			}
// 			if err = takerAsset.UpdateUserHoldPos(takerAsset.TrialLongPos, takerAsset.TrialShortPos, takerAsset.TrialBothPos, _req.Taker.UserType, true); err != nil {
// 				logrus.Errorf("taker trade update trial hold pos err:%v, hash key:%s", err, takerCache.HashKey)
// 			}
// 			// 更新用户持仓信息
// 			if err = makerAsset.UpdateUserHoldPos(makerAsset.LongPos, makerAsset.ShortPos, makerAsset.BothPos, _req.Maker.UserType, false); err != nil {
// 				logrus.Errorf("maker trade update hold pos err:%v, hash key:%s", err, makerCache.HashKey)
// 			}
// 			// 更新用户持仓信息
// 			if err = makerAsset.UpdateUserHoldPos(makerAsset.TrialLongPos, makerAsset.TrialShortPos, makerAsset.TrialBothPos, _req.Maker.UserType, true); err != nil {
// 				logrus.Errorf("maker trade update trial hold pos err:%v, hash key:%s", err, makerCache.HashKey)
// 			}
// 		}
// 	}()

// 	takerAssetSwap := modelutil.NewLogAssetSync(takerAsset, takerCache.Quote, _req.OperateTime)
// 	makerAssetSwap := modelutil.NewLogAssetSync(makerAsset, makerCache.Quote, _req.OperateTime)
// 	go func() {
// 		redis := redislib.Redis()
// 		if len(takerReply.ClearPos.PosId) > 0 {
// 			go redis.LPush(domain.PosClear, takerReply.ClearPos.PosId)
// 		}
// 		if len(makerReply.ClearPos.PosId) > 0 {
// 			go redis.LPush(domain.PosClear, makerReply.ClearPos.PosId)
// 		}

// 		assetChangeList := []*repository.LogAssetSync{takerAssetSwap, makerAssetSwap}
// 		posChangeList := []*repository.LogPosSync{takerReply.LogPos, makerReply.LogPos}
// 		TradeAssetPosToMq(redis, assetChangeList, posChangeList)
// 		AddAssetLogs(redis, assetLogs...)
// 		AddBills(redis, billAsset...)
// 		AddTrialChange(redis, changeTrial)

// 		// 插入Taker强平数据(taker和maker都有平仓操作)
// 		if takerAsset.PositionMode == domain.HoldModeBoth {
// 			liquidation.NewOperator(_req.Taker.LiquidationType).LiquidationData(_req.Taker, repository.TradeCommon{
// 				Base:  _req.TradeCommon.Base,
// 				Quote: _req.TradeCommon.Quote,
// 			}, takerAsset.BothPos)
// 		} else {
// 			if _req.Maker.PosSide == domain.Long {
// 				liquidation.NewOperator(_req.Taker.LiquidationType).LiquidationData(_req.Taker, repository.TradeCommon{
// 					Base:  _req.TradeCommon.Base,
// 					Quote: _req.TradeCommon.Quote,
// 				}, takerAsset.LongPos)
// 			} else if _req.Maker.PosSide == domain.Short {
// 				liquidation.NewOperator(_req.Taker.LiquidationType).LiquidationData(_req.Taker, repository.TradeCommon{
// 					Base:  _req.TradeCommon.Base,
// 					Quote: _req.TradeCommon.Quote,
// 				}, takerAsset.ShortPos)
// 			}
// 		}
// 		// 插入Maker强平数据(taker和maker都有平仓操作)
// 		if makerAsset.PositionMode == domain.HoldModeBoth {
// 			liquidation.NewOperator(_req.Maker.LiquidationType).LiquidationData(_req.Maker, repository.TradeCommon{
// 				Base:  _req.TradeCommon.Base,
// 				Quote: _req.TradeCommon.Quote,
// 			}, makerAsset.BothPos)
// 		} else {
// 			if _req.Maker.PosSide == domain.Long {
// 				liquidation.NewOperator(_req.Maker.LiquidationType).LiquidationData(_req.Maker, repository.TradeCommon{
// 					Base:  _req.TradeCommon.Base,
// 					Quote: _req.TradeCommon.Quote,
// 				}, makerAsset.LongPos)
// 			} else if _req.Maker.PosSide == domain.Short {
// 				liquidation.NewOperator(_req.Maker.LiquidationType).LiquidationData(_req.Maker, repository.TradeCommon{
// 					Base:  _req.TradeCommon.Base,
// 					Quote: _req.TradeCommon.Quote,
// 				}, makerAsset.ShortPos)
// 			}
// 		}
// 	}()

// 	replyDto.Taker = takerReply.Reply
// 	replyDto.Maker = makerReply.Reply
// 	return http.StatusOK, replyDto, nil
// }

// func (slf *Trade) Process(req payload.TradeParam, trade payload.Trade, cacheAsset *repository.AssetSwap,
// 	cache *swapcache.PosCache, pCache *price.PCache,
// ) (payload.PosReply, domain.Code, error) {
// 	var (
// 		reply payload.PosReply
// 		code  domain.Code
// 		err   error
// 	)

// 	isTrial := len(trade.AwardIds) > 0
// 	openBothPos := cache.OpenBothPos
// 	closeBothPos := cache.CloseBothPos
// 	bothPos := cacheAsset.BothPos

// 	if cacheAsset.PositionMode == domain.HoldModeBoth {
// 		if isTrial {
// 			// 体验金不支持单向持仓
// 			err = fmt.Errorf("trial not support both pos")
// 			return reply, domain.Code251026, err
// 		}

// 		tradeAmount := req.Amount
// 		if trade.Side == domain.Sell {
// 			tradeAmount = tradeAmount.Neg() // 空仓置为负数
// 		}

// 		if (trade.Side == domain.Sell && trade.IsOpen() && bothPos.Pos.Sign() <= 0) ||
// 			(trade.Side == domain.Buy && trade.IsOpen() && bothPos.Pos.Sign() >= 0) {
// 			reply, err = openBothPos(cacheAsset, trade, pCache, tradeAmount, trade.Fee, trade.UnfrozenMargin)
// 		} else {
// 			closeAmount, openAmount, closeFee, cUnfrozenMargin := tradeAmount, decimal.Zero, trade.Fee, trade.UnfrozenMargin
// 			if bothPos.Pos.Abs().LessThan(tradeAmount.Abs()) {
// 				closeAmount = bothPos.Pos.Neg()
// 				closeFee = trade.Fee.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)

// 				cUnfrozenMargin = trade.UnfrozenMargin.Mul(closeAmount.Abs().Div(req.Amount)).Abs().Truncate(domain.CurrencyPrecision)
// 				openAmount = bothPos.Pos.Add(tradeAmount)
// 			} else {
// 				if cUnfrozenMargin.GreaterThanOrEqual(decimal.Zero) {
// 					cUnfrozenMargin, err = util.RoundCeil(req.Amount.Mul(trade.OrderPrice).Div(decimal.NewFromInt32(int32(trade.Leverage))), domain.CurrencyPrecision)
// 					if err != nil {
// 						return reply, domain.Code252201, err
// 					}
// 				}
// 			}

// 			logrus.Infof("-both pos self trade- call CloseBothPos closeAmount:%s openAmount:%s BothPos: %+v", closeAmount.String(), openAmount.String(), bothPos)
// 			reply, code, err = closeBothPos(cacheAsset, trade, pCache, closeAmount, closeFee, cUnfrozenMargin)
// 			if code == http.StatusOK {
// 				// 推送仓位平仓状态
// 				pos := cache.NewLogPosSync(cacheAsset.BothPos, req.OperateTime, req.TradeId, trade.OrderId,
// 					trade.Side, trade.PosSide, closeAmount, reply.ProfitReal)
// 				if err := persist.SyncPos(redislib.Redis(), req.ContractCode(), pos); err != nil {
// 					logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
// 				}

// 				// 反向开仓处理
// 				if trade.IsOpen() && !openAmount.IsZero() {
// 					closedAssetLogs := reply.AssetLogs
// 					closedBillAssetLogs := reply.BillAssetLogs
// 					closedTrialLogs := reply.TrialLogs
// 					closeProfitReal := reply.ProfitReal
// 					reply, err = openBothPos(cacheAsset, trade, pCache, openAmount, trade.Fee.Sub(closeFee),
// 						trade.UnfrozenMargin.Sub(cUnfrozenMargin))

// 					reply.ProfitReal = closeProfitReal // 部分平仓的已实现盈亏
// 					// reply.IsReverse = true
// 					// reply.ReverseClosePosAmount = closeAmount
// 					reply.AssetLogs = append(reply.AssetLogs, closedAssetLogs...)
// 					reply.BillAssetLogs = append(reply.BillAssetLogs, closedBillAssetLogs...)
// 					reply.TrialLogs = append(reply.TrialLogs, closedTrialLogs...)
// 				}
// 			}
// 		}
// 	} else {
// 		if trade.Side == domain.Sell && trade.IsOpen() {
// 			if isTrial {
// 				reply, err = cache.OpenTrialShortPos(cacheAsset, pCache, trade)
// 			} else {
// 				reply, err = cache.OpenShortPos(cacheAsset, pCache, trade)
// 			}
// 		} else if trade.Side == domain.Sell && trade.IsClose() {
// 			if isTrial {
// 				reply, code, err = cache.CloseTrialLongPos(cacheAsset, pCache, trade)
// 			} else {
// 				reply, code, err = cache.CloseLongPos(cacheAsset, pCache, trade)
// 			}
// 		} else if trade.Side == domain.Buy && trade.IsOpen() {
// 			if isTrial {
// 				reply, err = cache.OpenTrialLongPos(cacheAsset, pCache, trade)
// 			} else {
// 				reply, err = cache.OpenLongPos(cacheAsset, pCache, trade)
// 			}
// 		} else if trade.Side == domain.Buy && trade.IsClose() {
// 			if isTrial {
// 				reply, code, err = cache.CloseTrialShortPos(cacheAsset, pCache, trade)
// 			} else {
// 				reply, code, err = cache.CloseShortPos(cacheAsset, pCache, trade)
// 			}
// 		}
// 	}

// 	if trade.IsClose() && trade.UserType != domain.UserTypePlatformRobot {
// 		riskHandler(reply)
// 	}
// 	return reply, code, err
// }

// func riskHandler(data payload.PosReply) {
// 	profit, err := setting.GetProfitFromRedis(data.UID)
// 	if err != nil {
// 		logrus.Errorf("riskHandler GetProfitFromRedis error: %v", err)
// 		return
// 	}
// 	profit.CurrentFund = profit.CurrentFund.Add(data.ProfitReal)
// 	err = profit.Calculate()
// 	if err != nil {
// 		logrus.Errorf("riskHandler Calculate error: %v", err)
// 		return
// 	}
// 	logrus.Info("user[%v] current profit rate is: %v", profit.ProfitRate)
// 	err = profit.Flush(data.UID)
// 	if err != nil {
// 		logrus.Errorf("riskHandler Flush error: %v", err)
// 		return
// 	}

// 	// 触发风控检查，直接检查盈利率
// 	riskcli := riskclient.New(redislib.Redis().Client(), riskclient.ProjectTypeSwap)
// 	riskcli.Factor(riskclient.FactorProfit).Trigger(riskclient.TriggerTypeUserId, data.UID).Value(strconv.FormatFloat(profit.ProfitRate, 'f', -1, 64)).Fire()

// 	return
// }

// // TradeErr 错误单处理，单边：Taker 或 Maker
// func (slf *Trade) TradeErr(req *payload.TradeParam, isTaker bool) (domain.Code, payload.TradeReply, error) {
// 	replyDto := payload.TradeReply{
// 		TradeId:      req.TradeId,
// 		TradeType:    req.TradeType,
// 		ContractCode: req.ContractCode(),
// 	}
// 	singleTrade := req.Maker
// 	if isTaker {
// 		singleTrade = req.Taker
// 	}
// 	pCache := price.New()
// 	// lock taker
// 	userMutex := redislib.NewMutex(domain.MutexSwapPosLock+singleTrade.UID, 30*time.Second)
// 	if userMutex.Lock() != nil {
// 		return domain.Code251101, replyDto, domain.ErrLockPos
// 	}
// 	defer userMutex.Unlock()

// 	// 获取仓位
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: repository.TradeCommon{
// 			Base:  req.TradeCommon.Base,
// 			Quote: req.TradeCommon.Quote,
// 		},
// 	}, singleTrade.UID)

// 	// 平仓资产变动(变动已实现盈亏,可用,账户余额)
// 	userAsset, err := userCache.Load()
// 	if err != nil {
// 		return domain.Code252404, replyDto, err
// 	}

// 	var assetLogs []*repository.MqCmsAsset
// 	var billAsset []repository.BillAssetSync

// 	reply, _, err := slf.Process(*req, singleTrade, userAsset, userCache, pCache)
// 	if err != nil {
// 		return domain.Code251106, replyDto, err
// 	}
// 	assetLogs = append(assetLogs, reply.AssetLogs...)
// 	billAsset = append(billAsset, reply.BillAssetLogs...)

// 	// 更新用户持仓信息
// 	if err := userAsset.UpdateUserHoldPos(userAsset.LongPos, userAsset.ShortPos, userAsset.BothPos, singleTrade.UserType, false); err != nil {
// 		logrus.Errorf("[SellOpenBuyOpen.Trade UpdateUserHoldPos] err:%v, hash key:%s", err, userCache.HashKey)
// 	}
// 	if err := userAsset.UpdateUserHoldPos(userAsset.TrialLongPos, userAsset.TrialShortPos, userAsset.TrialBothPos, singleTrade.UserType, true); err != nil {
// 		logrus.Errorf("[SellOpenBuyOpen.Trade UpdateUserHoldPos] trial err:%v, hash key:%s", err, userCache.HashKey)
// 	}

// 	assetSwap := modelutil.NewLogAssetSync(userAsset, userCache.Quote, req.OperateTime)
// 	go func() {
// 		redis := redislib.Redis()
// 		assetChangeList := []*repository.LogAssetSync{assetSwap}
// 		posChangeList := []*repository.LogPosSync{reply.LogPos}
// 		TradeAssetPosToMq(redis, assetChangeList, posChangeList)
// 		AddAssetLogs(redis)
// 		AddBills(redis, billAsset...)
// 	}()

// 	if isTaker {
// 		replyDto.Taker = reply.Reply
// 	} else {
// 		replyDto.Maker = reply.Reply
// 	}

// 	return http.StatusOK, replyDto, nil
// }
