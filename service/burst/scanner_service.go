package burst

// import (
// 	"context"
// 	"encoding/json"
// 	"fmt"
// 	"log"
// 	"runtime"
// 	"runtime/debug"
// 	"sort"
// 	"strconv"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/cache/cachelock"
// 	"futures-asset/cache/price"
// 	"futures-asset/cache/sharedcache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/db/swap"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/internal/message"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/service/isolation"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// func ScannerStart(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "ScannerStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second * 5)
// 			ScannerStart(_ctx, _wg, true)
// 		}
// 	}()

// 	if !_isRecover {
// 		_wg.Add(1)
// 	}
// 	second30Ticker := time.NewTicker(time.Second * 30)
// Loop:
// 	for {
// 		select {
// 		case <-second30Ticker.C:
// 			// 检查是否新增币对
// 			burstContracts := sharedcache.GetBurstServerContracts()
// 			if len(burstContracts) < 1 {
// 				log.Fatalf("burst contracts is empty")
// 			}
// 			sort.Strings(burstContracts)
// 			// 快照币对列表
// 			snapScannerContracts.RLock()
// 			memSnapContracts := snapScannerContracts.List
// 			snapScannerContracts.RUnlock()
// 			sort.Strings(memSnapContracts)

// 			burstContractsStr := strings.Join(burstContracts, "|")
// 			snapContractsStr := strings.Join(memSnapContracts, "|")

// 			// 配置币对对比内存快照币对
// 			if burstContractsStr != snapContractsStr {
// 				// 配置币对map
// 				burstContractMap := map[string]string{}
// 				for _, symbol := range burstContracts {
// 					burstContractMap[symbol] = symbol
// 				}
// 				// 快照币对map
// 				memContractMap := map[string]string{}
// 				for _, symbol := range memSnapContracts {
// 					memContractMap[symbol] = symbol
// 				}
// 				// 确定移除币对
// 				var removeSymbolList []string
// 				for _, symbol := range memSnapContracts {
// 					if _, ok := burstContractMap[symbol]; !ok {
// 						removeSymbolList = append(removeSymbolList, symbol)
// 					}
// 				}
// 				// 确定添加币对
// 				var addSymbolList []string
// 				for _, symbol := range burstContracts {
// 					if _, ok := memContractMap[symbol]; !ok {
// 						addSymbolList = append(addSymbolList, symbol)
// 					}
// 				}
// 				// 移除币对
// 				scannerStop(removeSymbolList)
// 				snapScannerContracts.Lock()
// 				snapScannerContracts.List = burstContracts
// 				snapScannerContracts.Unlock()
// 				// 增加币对
// 				for _, symbol := range addSymbolList {
// 					hasContext := false
// 					snapScannerContext.Lock()
// 					_, hasContext = snapScannerContext.Map[symbol]
// 					snapScannerContext.Unlock()
// 					if !hasContext {
// 						go _scannerStart(_ctx, _wg, symbol, false)
// 					}
// 				}
// 			}

// 		case <-_ctx.Done():
// 			break Loop
// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// func _scannerStart(_ctx context.Context, _wg *sync.WaitGroup, _symbol string, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, strings.ToUpper(_symbol), "scannerStart recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			RunningServices.Lock()
// 			if runningService, ok := RunningServices.Map[strings.ToUpper(_symbol)]; ok {
// 				runningService.Close()
// 			}
// 			RunningServices.Map[strings.ToUpper(_symbol)] = nil
// 			RunningServices.Unlock()
// 			time.Sleep(time.Second * 5)
// 			_scannerStart(_ctx, _wg, _symbol, true)
// 		}
// 	}()

// 	var burstWorker *workingBurstService = nil
// 	if !_isRecover {
// 		burstWorker = buildService(_symbol)
// 	} else {
// 		RunningServices.RLock()
// 		burstWorker = RunningServices.Map[strings.ToUpper(_symbol)]
// 		RunningServices.RUnlock()
// 	}
// 	if burstWorker == nil {
// 		return
// 	}

// 	err := burstWorker.updateServiceCoinPairInfo()
// 	if err != nil {
// 		logrus.Error(0, strings.ToUpper(_symbol), "scannerStart updateServiceCoinPairInfo error:", err)
// 	}

// 	workerGroup := 10
// 	burstWorker.InitScannerGroup(workerGroup)
// 	wg := new(sync.WaitGroup)

// 	// 启动爆仓服务
// 	burstScannerCtx, burstScannerCtxCancel := context.WithCancel(_ctx)
// 	go burstWorker.BurstScannerStart(burstScannerCtx, wg, false)

// 	time.Sleep(time.Second * 2)

// 	// 监听标记价格频率
// 	go burstWorker.MarkPriceListener(_ctx)

// 	// 检查币对禁用状态
// 	checkContractStatusCtx, checkContractStatusCtxCancel := context.WithCancel(_ctx)
// 	go burstWorker.CheckContractStatus(checkContractStatusCtx, wg, false)

// 	// 追加内存快照
// 	RunningServices.Lock()
// 	RunningServices.Map[strings.ToUpper(_symbol)] = burstWorker
// 	RunningServices.Unlock()
// 	if !_isRecover {
// 		_wg.Add(1)
// 		snapScannerContracts.Lock()
// 		snapScannerContracts.List = append(snapScannerContracts.List, _symbol)
// 		snapScannerContracts.Unlock()
// 	}

// 	snapScannerContext.Lock()
// 	if funcList, ok := snapScannerContext.Map[_symbol]; ok {
// 		for _, cancelFunc := range funcList {
// 			cancelFunc()
// 		}
// 	}
// 	snapScannerContext.Map[_symbol] = []context.CancelFunc{burstScannerCtxCancel, checkContractStatusCtxCancel}
// 	snapScannerContext.Unlock()

// 	go burstWorker.CheckTickTime(_ctx)

// 	logrus.Info(0, _symbol, "burst scanner started")

// 	<-burstScannerCtx.Done()
// 	<-checkContractStatusCtx.Done()
// 	burstWorker.TickerListenerStop()
// 	burstWorker.MarkPriceListenerStop()
// 	wg.Wait()
// 	burstWorker = nil
// 	logrus.Info(0, _symbol, "burst scanner stopped")
// 	_wg.Done()
// }

// // CheckContractStatus 检查是否强制平仓后禁用币对
// func (bs *workingBurstService) CheckContractStatus(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "CheckContractStatus recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			bs.CheckContractStatus(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}

// 	logrus.Info(0, bs.contractCode, "burst check contract status started")
// 	defer logrus.Info(0, bs.contractCode, "burst check contract status stopped")

// 	targetTime := time.Now()
// 	targetTime = targetTime.Add(-time.Second * time.Duration(targetTime.Second()-1)).
// 		Add(-time.Nanosecond * time.Duration(targetTime.Nanosecond())).
// 		Add(time.Second * 10)

// 	ticker := time.NewTicker(time.Second * 10)
// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		case <-ticker.C:
// 			// 检查是否币对预下线
// 			if bs._serviceIsExpired() {
// 				// 强制清理用户仓位准备
// 				bs.closeAllUserPosReady()
// 			}

// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// }

// // MarkPriceListener 监听最新标记价格
// func (bs *workingBurstService) MarkPriceListener(ctx context.Context) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "MarkPriceListener recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			if bs.markPriceSubStop {
// 				return
// 			}
// 			time.Sleep(time.Second)
// 			bs.MarkPriceListener(ctx)
// 		}
// 	}()
// 	markPriceChannel := cache.GetContractMarkPriceRedisChannel(bs.base, bs.quote)
// 	bs.markPriceSubCli = redisCli.Subscribe(markPriceChannel)
// 	fmt.Println("subscribe", markPriceChannel)
// 	logrus.Info(0, bs.contractCode, "scanner subscribe", markPriceChannel)
// 	_ctx := context.WithValue(ctx, "name", "mark price listener")
// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			_, err := bs.markPriceSubCli.ReceiveMessage(context.Background())
// 			if err != nil {
// 				logrus.Error(0, fmt.Sprintln(bs.contractCode, "MarkPriceListener ReceiveMessage error:", err))
// 				goto Next
// 			}

// 			if !bs.working && !bs.closeFlag {
// 				bs.ticker <- 1
// 			}

// 		}
// 	Next:
// 		runtime.Gosched()
// 		time.Sleep(time.Millisecond * 100)
// 	}
// }

// // TickerListenerStop 监听最新成交价
// func (bs *workingBurstService) TickerListenerStop() {
// 	bs.tickerSubStop = true
// 	if bs.tickerSubCli != nil {
// 		err := bs.tickerSubCli.Close()
// 		if err != nil {
// 			logrus.Error(0, "TickerListenerStop Close error:", err)
// 			return
// 		}
// 	}
// 	bs.tickerSubCli = nil
// }

// // MarkPriceListenerStop 监听最新成交价
// func (bs *workingBurstService) MarkPriceListenerStop() {
// 	bs.markPriceSubStop = true
// 	err := bs.markPriceSubCli.Close()
// 	if err != nil {
// 		logrus.Error(0, "MarkPriceListenerStop Close error:", err)
// 		return
// 	}
// 	bs.markPriceSubCli = nil
// }

// // BurstScannerStart 爆仓服务启动  功能：订阅最新成交价触发
// func (bs *workingBurstService) BurstScannerStart(_ctx context.Context, _wg *sync.WaitGroup, _isRecover bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "%s BurstServiceStart recover error:", err)
// 			bs.BurstScannerStart(_ctx, _wg, true)
// 		}
// 	}()
// 	if !_isRecover {
// 		_wg.Add(1)
// 	}
// 	fmt.Println(bs.contractCode, "burst scanner started")
// 	if len(bs.userGroup) < 1 {
// 		logrus.Error(0, bs.contractCode, "user group not init")
// 		return
// 	}
// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		case <-bs.ticker:
// 			bs.lastTickTime = time.Now()
// 			if !bs.working {
// 				bs.burstReady()
// 			}

// 		}
// 		runtime.Gosched()
// 	}

// 	_wg.Done()
// 	fmt.Println(bs.contractCode, "burst scanner stopped")
// }

// func (bs *workingBurstService) CheckTickTime(ctx context.Context) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(1, bs.contractCode, "CheckTickTime recover error: %s", err)
// 			time.Sleep(time.Second * 3)
// 			bs.CheckTickTime(ctx)
// 		}
// 	}()

// 	time.Sleep(time.Second * 3)

// 	_ctx := context.WithValue(ctx, "name", "ticker")

// Loop:
// 	for {
// 		select {
// 		case <-_ctx.Done():
// 			break Loop

// 		default:
// 			if bs.lastTickTime.IsZero() || bs.lastTickTime.Before(time.Now().Add(time.Second*-10)) {
// 				bs.ticker <- 1
// 			}

// 		}

// 		runtime.Gosched()
// 		time.Sleep(time.Second)
// 	}
// }

// // burstReady 爆仓准备
// // 功能：将用户拉出投放到指定分组
// func (bs *workingBurstService) burstReady() {
// 	// 获取当前持有仓位用户
// 	platformUserPosKey := domain.GetAllUserPosKey(bs.contractCode)
// 	swapUsers, err := redisCli.HKeys(platformUserPosKey)
// 	if err != nil {
// 		logrus.Error(0, "burstService HKeys", platformUserPosKey, "error:", err)
// 		return
// 	}

// 	// 获取当前持有体验金仓位用户
// 	platformTrialUserPosKey := domain.GetAllUserTrialPosKey(bs.contractCode)
// 	trialSwapUsers, err := redisCli.HKeys(platformTrialUserPosKey)
// 	if err != nil {
// 		logrus.Error(0, "burstService HKeys", platformTrialUserPosKey, "error:", err)
// 		return
// 	}

// 	// 合并用户去重
// 	swapUsers = append(swapUsers, trialSwapUsers...)
// 	swapUsers = util.SliceUnique(swapUsers)

// 	ids := make(map[int][]string)
// 	for _, uid := range swapUsers {
// 		idNum, err := strconv.ParseInt(uid, 10, 64)
// 		if err != nil {
// 			logrus.Error(0, "uid:", uid)
// 			continue
// 		}
// 		groupId := int(idNum) % len(bs.userGroup)
// 		v, ok := ids[groupId]
// 		if !ok {
// 			ids[groupId] = make([]string, 0, len(swapUsers))
// 			ids[groupId] = append(ids[groupId], uid)
// 		} else {
// 			v = append(v, uid)
// 			ids[groupId] = v
// 		}
// 	}
// 	for k, v := range ids {
// 		if len(v) == 0 {
// 			continue
// 		}
// 		bs.userGroup[k] <- v
// 	}
// }

// // 爆仓扫描  功能：从分组中拉取待检查用户
// func (bs *workingBurstService) burstScanner(_group int) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "burstScanner recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second)
// 			bs.burstScanner(_group)
// 		}
// 	}()

// 	logrus.Info(0, bs.contractCode, "burstScanner", _group, "start")
// 	defer logrus.Info(0, bs.contractCode, "burstScanner", _group, "stop")

// 	for {
// 		select {
// 		case userIds := <-bs.userGroup[_group]:
// 			logrus.Info(0, bs.contractCode, "burstScanner", _group, "begin", time.Now().UnixMilli())
// 			// 检查是否币对预下线
// 			if bs._serviceIsExpired() {
// 				// 强制清理用户仓位准备
// 				go bs.closeAllUserPosReady()
// 			} else {
// 				for _, uid := range userIds {
// 					bs.checkUserBurst(uid)
// 				}
// 				logrus.Info(0, bs.contractCode, "burstScanner", _group, "end", time.Now().UnixMilli())
// 			}
// 		}
// 		runtime.Gosched()
// 		time.Sleep(time.Millisecond * 100)
// 	}
// }

// // closeAllUserPosReady 清理所有用户仓位准备
// //
// //	功能：将用户拉出投放到指定分组(禁用币对时候专用)
// func (bs *workingBurstService) closeAllUserPosReady() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "closeAllUserPosReady recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			return
// 		}
// 	}()

// 	// 获取当前持有(实钱)仓位用户
// 	platformUserPosKey := domain.GetAllUserPosKey(bs.contractCode)
// 	swapUserIds, err := redisCli.HKeys(platformUserPosKey)
// 	if err != nil {
// 		logrus.Error(0, "closeAllUserPosReady HKeys", platformUserPosKey, " error:", err)
// 		return
// 	}

// 	// 获取当前持有体验金仓位用户
// 	platformTrialUserPosKey := domain.GetAllUserTrialPosKey(bs.contractCode)
// 	trialSwapUserIds, err := redisCli.HKeys(platformTrialUserPosKey)
// 	if err != nil {
// 		logrus.Error(0, "burstService HKeys", platformTrialUserPosKey, "error:", err)
// 		return
// 	}

// 	// 实际仓位用户
// 	for _, uid := range swapUserIds {
// 		temp := repository.UserHoldPos{}
// 		posJsonStr, err := redisCli.HGet(platformUserPosKey, uid)
// 		if err != nil {
// 			logrus.Error(0, "closeAllUserPosReady HGet", platformUserPosKey, uid, " error:", err)
// 			continue
// 		}
// 		err = json.Unmarshal([]byte(posJsonStr), &temp)
// 		if err != nil {
// 			logrus.Error(0, "closeAllUserPosReady", uid, "Unmarshal", posJsonStr, " error:", err)
// 			continue
// 		}
// 		// todo both pos
// 		if temp.LongPos.GreaterThan(decimal.Zero) || temp.BothPos.GreaterThan(decimal.Zero) {
// 			longRivalKey := cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)
// 			err = redisCli.ZAdd(longRivalKey, uid, float64(time.Now().UnixNano()))
// 			if err != nil {
// 				logrus.Error(0, "closeAllUserPosReady LongPos", uid, "ZAdd", uid, " error:", err)
// 			}
// 		}
// 		if temp.ShortPos.GreaterThan(decimal.Zero) || temp.BothPos.LessThan(decimal.Zero) {
// 			shortRivalKey := cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)
// 			err = redisCli.ZAdd(shortRivalKey, uid, float64(time.Now().UnixNano()))
// 			if err != nil {
// 				logrus.Error(0, "closeAllUserPosReady ShortPos", uid, "ZAdd", uid, " error:", err)
// 			}
// 		}
// 	}

// 	// 体验金仓位用户
// 	for _, uid := range trialSwapUserIds {
// 		temp := repository.UserHoldPos{}
// 		posJsonStr, err := redisCli.HGet(platformTrialUserPosKey, uid)
// 		if err != nil {
// 			logrus.Error(0, "closeAllUserPosReady Trial HGet", platformTrialUserPosKey, uid, " error:", err)
// 			continue
// 		}
// 		err = json.Unmarshal([]byte(posJsonStr), &temp)
// 		if err != nil {
// 			logrus.Error(0, "closeAllUserPosReady Trial", uid, "Unmarshal", posJsonStr, " error:", err)
// 			continue
// 		}
// 		// todo both pos
// 		if temp.LongPos.GreaterThan(decimal.Zero) || temp.BothPos.GreaterThan(decimal.Zero) {
// 			longRivalKey := cachekey.GetBurstRivalLongRankRedisKey(bs.contractCode)
// 			err = redisCli.ZAdd(longRivalKey, swapcache.TrialUserId(uid), float64(time.Now().UnixNano()))
// 			if err != nil {
// 				logrus.Error(0, "closeAllUserPosReady Trial LongPos", uid, "ZAdd", uid, " error:", err)
// 			}
// 		}
// 		if temp.ShortPos.GreaterThan(decimal.Zero) || temp.BothPos.LessThan(decimal.Zero) {
// 			shortRivalKey := cachekey.GetBurstRivalShortRankRedisKey(bs.contractCode)
// 			err = redisCli.ZAdd(shortRivalKey, swapcache.TrialUserId(uid), float64(time.Now().UnixNano()))
// 			if err != nil {
// 				logrus.Error(0, "closeAllUserPosReady Trial ShortPos", uid, "ZAdd", uid, " error:", err)
// 			}
// 		}
// 	}

// 	// 合并用户去重
// 	swapUserIds = append(swapUserIds, trialSwapUserIds...)
// 	swapUserIds = util.SliceUnique(swapUserIds)

// 	for _, uid := range swapUserIds {
// 		redisKey := cache.GetCloseAllUserPosListRedisKey(bs.base, bs.quote)
// 		err = redisCli.LPush(redisKey, uid)
// 		if err != nil {
// 			logrus.Error(0, uid, "closeAllUserPosReady LPush", redisKey, "error:", err)
// 			continue
// 		}
// 	}
// 	return
// }

// // checkBurst 检查用户爆仓
// func (bs *workingBurstService) checkUserBurst(_userId string) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, bs.contractCode, "checkBurst recover error:", err)
// 			logrus.Error(0, string(debug.Stack()))
// 		}
// 	}()

// 	// 获取用户资产信息
// 	userCache, assetInfo, err := swapcache.GetUserCacheData(bs.base, bs.quote, _userId)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "burstService checkBurst GetUserCacheData error:", err, _userId)
// 		return
// 	}
// 	// 获取用户仓位
// 	crossPos := make([]repository.PosSwap, 0)
// 	pCache := price.New()
// 	if assetInfo.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
// 		crossPos, _, err = userCache.UserPos(false, bs.quote, pCache)
// 	} else {
// 		crossPos, _, err = userCache.UserPos(true, "", pCache)
// 	}
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "checkUserBurst userCache.UserPos error:", err, _userId)
// 		return
// 	}

// 	// 更新强制对手方指数
// 	bs.UpdateRivalScore(_userId, assetInfo, userCache)

// 	// 获取所有总仓位 = 真实总仓位 + 体验金总仓位
// 	totalPos := assetInfo.LongPos.Pos.Add(assetInfo.ShortPos.Pos.Add(assetInfo.BothPos.Pos.Abs()))
// 	totalPos = totalPos.Add(assetInfo.TrialLongPos.Pos.Add(assetInfo.TrialShortPos.Pos.Add(assetInfo.TrialBothPos.Pos.Abs())))
// 	if totalPos.LessThanOrEqual(decimal.Zero) {
// 		return
// 	}

// 	// 获取用户当前比对仓位类型
// 	marginMode := assetInfo.GetLeverage(bs.contractCode).MarginMode
// 	switch marginMode {
// 	case domain.MarginModeCross:
// 		hasPos := false
// 		for _, pos := range crossPos {
// 			if pos.Pos.Abs().GreaterThan(decimal.Zero) {
// 				hasPos = true
// 				break
// 			}
// 		}
// 		if hasPos {
// 			// 全仓爆仓检查
// 			bs.checkCrossBurst(userCache, assetInfo, crossPos)
// 		}

// 	case domain.MarginModeIsolated:
// 		isolatedPosList := []repository.PosSwap{
// 			assetInfo.ShortPos,
// 			assetInfo.LongPos,
// 			assetInfo.BothPos,
// 			assetInfo.TrialLongPos,
// 			assetInfo.TrialShortPos,
// 			assetInfo.TrialBothPos,
// 		}
// 		for _, posInfo := range isolatedPosList {
// 			if posInfo.Pos.Abs().IsPositive() {
// 				// 判断是否非体验金仓位
// 				if !posInfo.IsTrial() {
// 					// 非体验金仓位爆仓检查
// 					bs.checkIsolateBurst(userCache, assetInfo, posInfo)
// 				} else {
// 					// 体验金仓位爆仓检查
// 					bs.checkTrialIsolateBurst(userCache, assetInfo, posInfo)
// 				}
// 			}
// 		}

// 	default:
// 		// 缓存中没有用户持仓模式信息
// 		contractLeverage := &repository.Leverage{
// 			ContractCode: strings.ToUpper(bs.contractCode),
// 		}
// 		// 使用已有仓位进行赋值
// 		currentContractCodePosList := []repository.PosSwap{
// 			assetInfo.ShortPos,
// 			assetInfo.LongPos,
// 			assetInfo.BothPos,
// 		}
// 		for _, posInfo := range currentContractCodePosList {
// 			if !posInfo.Pos.IsZero() {
// 				contractLeverage.MarginMode = domain.MarginMode(posInfo.MarginMode)
// 				switch domain.MarginMode(posInfo.MarginMode) {
// 				case domain.MarginModeCross:
// 					contractLeverage.Leverage = posInfo.Leverage
// 					contractLeverage.LLeverage = 10
// 					contractLeverage.SLeverage = 10
// 					contractLeverage.BLeverage = 10
// 					break

// 				case domain.MarginModeIsolated:
// 					switch posInfo.PosSide {
// 					case domain.LongPos:
// 						contractLeverage.LLeverage = posInfo.Leverage

// 					case domain.ShortPos:
// 						contractLeverage.SLeverage = posInfo.Leverage

// 					case domain.BothPos:
// 						contractLeverage.BLeverage = posInfo.Leverage
// 					default:
// 					}
// 				}
// 			}
// 		}
// 		// 填充已有仓位意外事件的默认值
// 		if contractLeverage.Leverage <= 0 {
// 			contractLeverage.Leverage = 10
// 		}
// 		if contractLeverage.LLeverage <= 0 {
// 			contractLeverage.LLeverage = 10
// 		}
// 		if contractLeverage.SLeverage <= 0 {
// 			contractLeverage.SLeverage = 10
// 		}
// 		if contractLeverage.BLeverage <= 0 {
// 			contractLeverage.BLeverage = 10
// 		}
// 		assetInfo.Leverage = append(assetInfo.Leverage, contractLeverage)
// 		leverageBytes, _ := json.Marshal(assetInfo.Leverage)
// 		if err = redislib.Redis().HSet(userCache.HashKey, userCache.LeverageKey, string(leverageBytes)); err != nil {
// 			logrus.Error(0, bs.contractCode, _userId, "burstService assetInfo MarginMode:", fmt.Sprintf("%+v", marginMode), "HSet", userCache.HashKey, userCache.LeverageKey, "error:", err)
// 			return
// 		}
// 		logrus.Error(0, bs.contractCode, _userId, "burstService assetInfo MarginMode:", fmt.Sprintf("%+v", marginMode))

// 	}

// 	return
// }

// // checkCrossBurst 检查用户爆仓
// func (bs *workingBurstService) checkCrossBurst(userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, crossPos []repository.PosSwap) {
// 	crossLocking := true

// 	feeRateInfo, err := GetContractUserLevelRate(userCache.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "Cross checkCrossBurst", userCache.UID, "error:", err)
// 		return
// 	}
// 	if feeRateInfo.UserType == userTypePlatformRobot {
// 		return
// 	}

// 	// 检查是否有扫描锁
// 	getCrossLock, crossLockErr := cachelock.UserScanTryLock(userCache.UID, domain.MarginModeCross, "")
// 	if crossLockErr != nil {
// 		logrus.Error(0, bs.contractCode, "Cross UserScanTryLock", userCache.UID, "error:", crossLockErr)
// 	}

// 	// 如果抢到全仓扫描锁
// 	if getCrossLock {
// 		crossLocking = false
// 		defer cachelock.UserScanUnlock(userCache.UID, domain.MarginModeCross, "")
// 	}

// 	// 扫描被其他线程锁定
// 	if crossLocking {
// 		return
// 	}

// 	// 获取全仓爆仓锁状态
// 	locked, err := cachelock.BurstUserIsLocked(repository.BurstLockParam{
// 		Liquidation: cache.LiquidationInfo{
// 			UID:        userCache.UID,
// 			MarginMode: domain.MarginModeCross,
// 		},
// 	})
// 	if err != nil {
// 		logrus.Error(0, userCache.UID, bs.contractCode, "checkCrossBurst BurstUserIsLocked error:", err)
// 		return
// 	}

// 	// 全仓已爆仓锁
// 	if locked {
// 		checkRecordKey := cachekey.GetBurstWorkingUsersRedisKey(domain.MarginModeCross)
// 		err = redisCli.Client().SAdd(context.Background(), checkRecordKey, assetInfo.UID).Err()
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst MarginModeCross SAdd error:", err)
// 		}
// 		return
// 	}

// 	pCache := price.New()
// 	holdMargin, warnMargin, isLastLevelMap, levelFilterMap, err := userCache.TotalCrossMaintainMargin(pCache, bs.contractCode)
// 	if err != nil {
// 		logrus.Error(0, "checkCrossBurst TotalCrossMaintainMargin", userCache.UID, "error:", err)
// 		return
// 	}
// 	crossMarkMarginBalance, err := userCache.CrossMarginBalance(assetInfo, pCache)
// 	if err != nil {
// 		logrus.Error(0, "checkCrossBurst CrossMarginBalance", userCache.UID, "error:", err)
// 		return
// 	}

// 	posTotalTrial := decimal.Zero
// 	for _, posInfo := range crossPos {
// 		posTotalTrial = posTotalTrial.Add(posInfo.TrialMargin)
// 	}
// 	logrus.Info(0, userCache.UID, bs.contractCode, "crossMarkMarginBalance:", crossMarkMarginBalance, "posTotalTrial:", posTotalTrial)

// 	crossMarkMarginBalance = crossMarkMarginBalance.Add(posTotalTrial)

// 	logrus.Info(0, userCache.UID, bs.contractCode, "holdMargin:", holdMargin, "crossMarkMarginBalance:", crossMarkMarginBalance)

// 	contractCodeIsLastLevel := isLastLevelMap[bs.contractCode]

// 	// 所有全仓维持保证金， 所有持仓仓位级别及维持保证金率 或 需要强制清仓
// 	if holdMargin.GreaterThan(decimal.Zero) && crossMarkMarginBalance.LessThanOrEqual(holdMargin) {
// 		// 生成爆仓id
// 		burstId := util.GenerateId()

// 		{
// 			// 检查是否触发重生卡
// 			activatingRebornCard, err := cache.GetActivatingRebornCard(userCache.UID)
// 			if err != nil && err != redis.Nil {
// 				logrus.Error(0, userCache.UID, bs.contractCode, "checkCrossBurst GetActivatingRebornCard error:", err)
// 			}
// 			// 判断重生卡是否生效中
// 			if len(activatingRebornCard.Id) > 0 && time.Now().Unix()-activatingRebornCard.TriggerTime < activatingRebornCard.Period {
// 				return
// 			}

// 			// 重生卡获取
// 			rebornCard, err := cache.GetAvailableRebornCard(userCache.UID, bs.contractCode)
// 			if err != nil && err != redis.Nil {
// 				logrus.Error(0, userCache.UID, bs.contractCode, "checkCrossBurst GetAvailableRebornCard error:", err)
// 			}
// 			// 如果重生卡有效就进行激活
// 			if len(rebornCard.Id) > 0 {
// 				var posIds []string
// 				for _, posInfo := range crossPos {
// 					posIds = append(posIds, posInfo.PosId)
// 					posInfo.RebornId = rebornCard.Id
// 					updateErr := isolation.SyncPos(posInfo)
// 					if updateErr != nil {
// 						logrus.Error(0, "update reborn pos info error:", err)
// 					}
// 				}
// 				activateOk, err := cache.ActivateRebornCard(posIds, rebornCard)
// 				if err != nil {
// 					logrus.Error(0, userCache.UID, bs.contractCode, "checkCrossBurst ActivateRebornCard error:", err)
// 				}
// 				// 激活成功直接免爆仓
// 				if activateOk {
// 					return
// 				}
// 			}
// 		}

// 		// 增加全仓爆仓锁
// 		err = cachelock.LockBurstUser(repository.BurstLockParam{
// 			Liquidation: cache.LiquidationInfo{
// 				BurstId:    burstId,
// 				UID:        userCache.UID,
// 				MarginMode: domain.MarginModeCross,
// 				BurstTime:  time.Now().Unix(),
// 			},
// 		})
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst LockBurstUser", userCache.UID, "error:", err)
// 			return
// 		}

// 		isBurst := false
// 		for _, isLastLevel := range isLastLevelMap {
// 			isBurst = isLastLevel
// 			if !isLastLevel {
// 				break
// 			}
// 		}

// 		logrus.Info(0, bs.contractCode, userCache.UID, "cross position burst, isBurst:", isBurst)
// 		bs.crossBurstTrigger(burstId, userCache, assetInfo, crossPos, levelFilterMap, holdMargin, isBurst, isLastLevelMap, pCache)
// 		return
// 	}

// 	// 触发警告
// 	if warnMargin.GreaterThan(decimal.Zero) && crossMarkMarginBalance.LessThanOrEqual(warnMargin) {
// 		// 最后一级按风险率发送预警
// 		if contractCodeIsLastLevel {
// 			riskRate := holdMargin.Div(crossMarkMarginBalance)
// 			if crossMarkMarginBalance.GreaterThan(decimal.Zero) && riskRate.GreaterThanOrEqual(decimal.NewFromFloat(0.8)) {
// 				// 发送爆仓预警通知
// 				go util.SendMailAndSmsToNoticeBurst(userCache.UID, bs.contractCode, domain.MSBurstWarn, "80", decimal.Zero)
// 			}
// 		} else {
// 			if crossMarkMarginBalance.LessThanOrEqual(warnMargin) {
// 				// 发送减仓预警通知
// 				go util.SendMailAndSmsToNoticeBurst(userCache.UID, bs.contractCode, domain.MSReduceWarn, "", decimal.Zero)
// 			}
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) crossBurstTrigger(burstId string, userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, crossPos []repository.PosSwap,
// 	_levelFilterMap map[string]repository.LevelFilter, holdMargin decimal.Decimal, isBurst bool, isLastLevelMap map[string]bool, pCache *price.PCache,
// ) {
// 	liquidationType := domain.LiquidationTypeBurst
// 	cancelType := domain.CancelTypeBurst

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			return
// 		}
// 	}

// 	for _, posInfo := range crossPos {
// 		// 如果不是最后一档触发，当前币对要是最后一档就跳过，继续看下一个是不是高档位
// 		if !isBurst {
// 			if isLastLevelMap[posInfo.ContractCode] {
// 				continue
// 			}

// 			liquidationType = domain.LiquidationTypeReduce
// 			cancelType = domain.CancelTypeReduce
// 		}

// 		if posInfo.Pos.Abs().Equal(decimal.Zero) {
// 			logrus.Info(0, "crossBurstTrigger skip pos", fmt.Sprintf("%+v", posInfo))
// 			continue
// 		}

// 		logrus.Info(0, "crossBurstTrigger pos", fmt.Sprintf("%+v", posInfo))

// 		tradePosSide := posInfo.PosSide
// 		if tradePosSide == domain.BothPos {
// 			if posInfo.Pos.GreaterThan(decimal.Zero) {
// 				tradePosSide = domain.LongPos
// 			} else {
// 				if posInfo.Pos.LessThan(decimal.Zero) {
// 					tradePosSide = domain.ShortPos
// 				}
// 			}
// 		}

// 		go _forcePushRivalScoreZero(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial())

// 		collapsePrice, collapsePriceFormula, err := posInfo.CrossCollapsePrice(assetInfo, userCache.HoldCostTotalIsolated(posInfo.Currency, true),
// 			userCache.OtherCrossUnreal(pCache), userCache.OtherCrossMaintainMargin(pCache), userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker, pCache)
// 		if err != nil {
// 			logrus.Error(0, posInfo.ContractCode, posInfo.UID, "crossBurstTrigger MarginModeCross CrossCollapsePrice error:", err)
// 			continue
// 		}
// 		if collapsePrice.LessThanOrEqual(decimal.Zero) {
// 			logrus.Error(0, posInfo.ContractCode, posInfo.UID, "crossBurstTrigger MarginModeCross LessThanOrEqual zero replace to last price")
// 			collapsePrice, err = sharedcache.GetContractLastPrice(util.BaseQuote(strings.ToUpper(posInfo.ContractCode)))
// 			if err != nil {
// 				logrus.Error(0, posInfo.ContractCode, posInfo.UID, "crossBurstTrigger MarginModeCross GetContractLastPrice error:", err)
// 				continue
// 			}
// 		}

// 		posLevelFilter, posLevelFilterOk := _levelFilterMap[posInfo.ContractCode]
// 		if !posLevelFilterOk {
// 			logrus.Error(0, "no", posInfo.ContractCode, "level filter:", fmt.Sprintf("%+v", _levelFilterMap))
// 			continue
// 		}

// 		targetLevel := 0
// 		targetLimit := decimal.Zero
// 		if !isBurst {
// 			if posLevelFilter.Level == 1 {
// 				targetLevel = 1
// 				targetLimit = posLevelFilter.HighLimit
// 			} else if posLevelFilter.Level > 1 {
// 				nextLevel, err := setting.NextMarginLevel(bs.base, bs.quote, posLevelFilter.Level)
// 				if err != nil {
// 					logrus.Error(0, posInfo.ContractCode, posInfo.UID, "updatePos MarginModeIsolated NextMarginLevel error:", err)
// 					targetLevel = 1
// 					targetLimit = posLevelFilter.HighLimit
// 				} else {
// 					targetLevel = nextLevel.Level
// 					targetLimit = nextLevel.HighLimit
// 				}
// 			}
// 		}

// 		// 增加全仓币对爆仓锁
// 		err = cachelock.LockBurstSymbol(repository.BurstLockParam{
// 			ContractCode: posInfo.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				BurstId:              burstId,
// 				UID:                  userCache.UID,
// 				PosId:                posInfo.PosId,
// 				MarginMode:           domain.MarginModeCross,
// 				PosSide:              posInfo.PosSide,
// 				LiquidationType:      liquidationType,
// 				CurrentLevel:         posLevelFilter.Level,
// 				TargetLevel:          targetLevel,
// 				TargetLimit:          targetLimit,
// 				CancelType:           cancelType,
// 				CollapsePrice:        collapsePrice,
// 				CollapsePriceFormula: collapsePriceFormula,
// 				BurstTime:            time.Now().UnixNano(),
// 			},
// 		})
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst LockBurstUser", userCache.UID, "error:", err)
// 			return
// 		}

// 		// 获取币对合约配置
// 		ContractSettings.Lock()
// 		settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 		ContractSettings.Unlock()
// 		if !settingOk {
// 			logrus.Error(0, "crossBurstTrigger cross get", posInfo.ContractCode, "BurstSettings is empty")
// 			continue
// 		}

// 		// 制作爆仓信息
// 		_burstPosInfo := new(burstPosInfo)
// 		err = _burstPosInfo._updatePos(burstId, assetInfo, userCache, settingInfo, posInfo, crossPos, posLevelFilter,
// 			time.Now().UnixNano(), pCache, userLevelRateInfo)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, "checkCrossBurst updatePos", userCache.UID, "error:", err)
// 			return
// 		}

// 		if _burstPosInfo.UserLevelRate.UserType == userTypePlatformRobot {
// 			err := cachelock.UnlockBurstUser(repository.BurstLockParam{
// 				ContractCode: posInfo.ContractCode,
// 				Liquidation: cache.LiquidationInfo{
// 					UID:        userCache.UID,
// 					MarginMode: domain.MarginModeCross,
// 				},
// 			}, true)
// 			if err != nil {
// 				logrus.Error(0, "crossBurstTrigger cross", posInfo.ContractCode, "UnlockBurstUser robot user err:", err)
// 			}
// 			continue
// 		}

// 		// 仓位强制清空标记
// 		_burstPosInfo.IsBurstToZero = isBurst

// 		// 仓位爆仓类型
// 		if isBurst {
// 			_burstPosInfo.LiquidationType = domain.LiquidationTypeBurst
// 			// 发送爆仓通知
// 			go util.SendMailAndSmsToNoticeBurst(posInfo.UID, posInfo.ContractCode, domain.MSBurst, "",
// 				pCache.GetMarkPrice(posInfo.ContractCode))
// 		} else {
// 			_burstPosInfo.LiquidationType = domain.LiquidationTypeReduce
// 			// 发送减仓通知
// 			go util.SendMailAndSmsToNoticeBurst(posInfo.UID, posInfo.ContractCode, domain.MSReduce, "", decimal.Zero)
// 		}

// 		_, _burstPosInfo.PNL = bs._posPNL(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial(), _burstPosInfo.CollapsePrice,
// 			posInfo.OpenPriceAvg, posInfo.ProfitUnreal, "makeBurstData", pCache)

// 		// 发送异步爆仓记录
// 		bs._sendBurstLog(*_burstPosInfo, posInfo, posLevelFilter.HoldingMarginRate, holdMargin, true)

// 		// 如果不是最后一档爆仓，发送一个降级之后跳出
// 		if !isBurst {
// 			break
// 		}
// 	}

// 	// 不是最后一档只锁单币对
// 	if !isBurst {
// 		// 删除用户爆仓锁
// 		err := cachelock.UnlockBurstUser(repository.BurstLockParam{
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        userCache.UID,
// 				MarginMode: domain.MarginModeCross,
// 			},
// 		}, false)
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst LockBurstUser", userCache.UID, "error:", err)
// 			return
// 		}
// 	}

// 	return
// }

// // checkIsolateBurst 检查用户爆仓
// func (bs *workingBurstService) checkIsolateBurst(userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, posInfo repository.PosSwap) {
// 	isolatedLocking := true

// 	feeRateInfo, err := GetContractUserLevelRate(userCache.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "Isolate checkCrossBurst", userCache.UID, "error:", err)
// 		return
// 	}
// 	if feeRateInfo.UserType == userTypePlatformRobot {
// 		return
// 	}

// 	// 检查是否有扫描锁
// 	getIsolatedLock, isolatedLockErr := cachelock.UserScanTryLock(userCache.UID, domain.MarginModeIsolated, bs.contractCode)
// 	if isolatedLockErr != nil {
// 		logrus.Error(0, bs.contractCode, "Isolated UserScanTryLock", userCache.UID, "error:", isolatedLockErr)
// 	}

// 	// 如果抢到全仓扫描锁
// 	if getIsolatedLock {
// 		isolatedLocking = false
// 		defer cachelock.UserScanUnlock(userCache.UID, domain.MarginModeIsolated, bs.contractCode)
// 	}

// 	// 扫描被其他线程锁定
// 	if isolatedLocking {
// 		return
// 	}

// 	// 获取逐仓爆仓锁状态
// 	locked, err := cachelock.BurstUserIsLocked(repository.BurstLockParam{
// 		ContractCode: bs.contractCode,
// 		Liquidation: cache.LiquidationInfo{
// 			UID:        userCache.UID,
// 			MarginMode: domain.MarginModeIsolated,
// 			PosSide:    posInfo.PosSide,
// 			IsTrialPos: posInfo.IsTrial(),
// 		},
// 	})
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, userCache.UID, "checkIsolateBurst BurstUserIsLocked error:", err)
// 		return
// 	}

// 	// 逐仓锁
// 	if locked {
// 		checkRecordKey := cachekey.GetBurstWorkingUsersRedisKey(domain.MarginModeIsolated)
// 		err = redisCli.Client().SAdd(context.Background(), checkRecordKey, assetInfo.UID).Err()
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst MarginModeCross SAdd error:", err)
// 		}
// 		return
// 	}

// 	pCache := price.New()
// 	posBase, posQuote := util.BaseQuote(posInfo.ContractCode)
// 	posValue := posInfo.CalcPosHoldValue()

// 	// 仓位级别及维持保证金率
// 	marginLevel, isLastLevel, err := setting.FetchMarginLevel(posBase, posQuote, posValue)
// 	if err != nil {
// 		logrus.Error(0, posInfo.ContractCode, posInfo.UID, "checkIsolateBurst FetchMarginLevel error:", err)
// 		return
// 	}

// 	holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 	warnMargin := marginLevel.WarnMarginRate.Mul(posValue)
// 	isolatedMarkMarginBalance := posInfo.IsolatedMarginBalance(pCache)

// 	logrus.Info(0, bs.contractCode, userCache.UID, "holdMargin:", holdMargin, "isolatedMarkMarginBalance:", isolatedMarkMarginBalance)

// 	// 所有全仓维持保证金， 所有持仓仓位级别及维持保证金率 或 需要强制清仓
// 	if holdMargin.GreaterThan(decimal.Zero) && isolatedMarkMarginBalance.LessThanOrEqual(holdMargin) {
// 		// 生成爆仓id
// 		burstId := util.GenerateId()

// 		{
// 			// 检查是否触发重生卡
// 			activatingRebornCard, err := cache.GetActivatingRebornCard(userCache.UID)
// 			if err != nil {
// 				logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst GetActivatingRebornCard error:", err)
// 			}
// 			// 判断重生卡是否生效中
// 			if len(activatingRebornCard.Id) > 0 && time.Now().Unix()-activatingRebornCard.TriggerTime < activatingRebornCard.Period {
// 				return
// 			}

// 			// 重生卡获取
// 			rebornCard, err := cache.GetAvailableRebornCard(userCache.UID, posInfo.ContractCode)
// 			if err != nil {
// 				logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst GetAvailableRebornCard error:", err)
// 			}
// 			// 如果重生卡有效就进行激活
// 			if len(rebornCard.Id) > 0 {
// 				posIds := []string{
// 					posInfo.PosId,
// 				}

// 				activateOk, err := cache.ActivateRebornCard(posIds, rebornCard)
// 				if err != nil {
// 					logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst ActivateRebornCard error:", err)
// 				}
// 				// 激活成功直接免爆仓
// 				if activateOk {
// 					return
// 				}
// 			}
// 		}

// 		// 获取user费率
// 		userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, assetInfo.UID, "checkIsolateBurst GetContractUserLevelRate error:", err)
// 			// 获取默认setting等级
// 			if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 				logrus.Info(0, bs.contractCode, assetInfo.UID, "checkIsolateBurst use default taker rate")
// 				userLevelRateInfo.UID = assetInfo.UID
// 				userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 			} else {
// 				logrus.Error(0, bs.contractCode, "checkIsolateBurst no level 0 taker rate:", err)
// 				return
// 			}
// 		}

// 		collapsePrice, collapsePriceFormula := posInfo.IsolatedCollapsePrice(userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker)
// 		if collapsePrice.LessThanOrEqual(decimal.Zero) {
// 			base, quote := util.BaseQuote(strings.ToUpper(posInfo.ContractCode))
// 			collapsePrice, err = sharedcache.GetContractLastPrice(base, quote)
// 			if err != nil {
// 				logrus.Error(0, posInfo.ContractCode, posInfo.UID, "updatePos MarginModeIsolated GetContractLastPrice error:", err)
// 			}
// 		}

// 		liquidationType := domain.LiquidationTypeBurst
// 		cancelType := domain.CancelTypeBurst
// 		targetLimit := decimal.Zero
// 		targetLevel := 0
// 		if !isLastLevel {
// 			liquidationType = domain.LiquidationTypeReduce
// 			cancelType = domain.CancelTypeReduce

// 			if marginLevel.Level == 1 {
// 				targetLevel = 1
// 				targetLimit = marginLevel.HighLimit
// 			} else if marginLevel.Level > 1 {
// 				nextLevel, err := setting.NextMarginLevel(bs.base, bs.quote, marginLevel.Level)
// 				if err != nil {
// 					logrus.Error(0, posInfo.ContractCode, posInfo.UID, "updatePos MarginModeIsolated NextMarginLevel error:", err)
// 					targetLevel = 1
// 					targetLimit = marginLevel.HighLimit
// 				} else {
// 					targetLevel = nextLevel.Level
// 					targetLimit = nextLevel.HighLimit
// 				}
// 			}

// 		}

// 		// 增加爆仓锁
// 		err = cachelock.LockBurstSymbol(repository.BurstLockParam{
// 			ContractCode: posInfo.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				BurstId:              burstId,
// 				UID:                  userCache.UID,
// 				PosId:                posInfo.PosId,
// 				MarginMode:           domain.MarginModeIsolated,
// 				PosSide:              posInfo.PosSide,
// 				LiquidationType:      liquidationType,
// 				CurrentLevel:         marginLevel.Level,
// 				TargetLevel:          targetLevel,
// 				TargetLimit:          targetLimit,
// 				CancelType:           cancelType,
// 				CollapsePrice:        collapsePrice,
// 				CollapsePriceFormula: collapsePriceFormula,
// 				BurstTime:            time.Now().Unix(),
// 				IsTrialPos:           posInfo.IsTrial(),
// 			},
// 		})
// 		if err != nil {
// 			logrus.Error(0, posInfo.ContractCode, "crossBurstTrigger LockBurstUser", userCache.UID, "error:", err)
// 		}
// 		logrus.Info(0, bs.contractCode, userCache.UID, "isolated position burst")
// 		bs.isolatedBurstTrigger(burstId, userCache, assetInfo, posInfo, marginLevel, holdMargin, isLastLevel, pCache)
// 		return
// 	}

// 	// 触发警告
// 	if warnMargin.GreaterThan(decimal.Zero) && isolatedMarkMarginBalance.LessThanOrEqual(warnMargin) {
// 		// 最后一级按风险率发送预警
// 		if isLastLevel {
// 			riskRate := holdMargin.Div(isolatedMarkMarginBalance)
// 			if isolatedMarkMarginBalance.GreaterThan(decimal.Zero) && riskRate.GreaterThanOrEqual(decimal.NewFromFloat(0.8)) {
// 				// 发送爆仓预警通知
// 				go util.SendMailAndSmsToNoticeBurst(bs.contractCode, userCache.UID, domain.MSBurstWarn, "80", decimal.Zero)
// 			}
// 		} else {
// 			if isolatedMarkMarginBalance.LessThanOrEqual(warnMargin) {
// 				// 发送减仓预警通知
// 				go util.SendMailAndSmsToNoticeBurst(bs.contractCode, userCache.UID, domain.MSReduceWarn, "", decimal.Zero)
// 			}
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) isolatedBurstTrigger(burstId string, userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, posInfo repository.PosSwap,
// 	levelFilter repository.LevelFilter, holdMargin decimal.Decimal, isLastLevel bool, pCache *price.PCache,
// ) {
// 	logrus.Info(0, "isolatedBurstTrigger pos", fmt.Sprintf("%+v", posInfo))

// 	tradePosSide := posInfo.PosSide
// 	if tradePosSide == domain.BothPos {
// 		if posInfo.Pos.GreaterThan(decimal.Zero) {
// 			tradePosSide = domain.LongPos
// 		} else {
// 			if posInfo.Pos.LessThan(decimal.Zero) {
// 				tradePosSide = domain.ShortPos
// 			}
// 		}
// 	}

// 	go _forcePushRivalScoreZero(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial())

// 	// 获取合约币对配置
// 	ContractSettings.Lock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.Unlock()
// 	if !settingOk {
// 		logrus.Error(0, "isolatedBurstTrigger cross", posInfo.ContractCode, "get BurstSettings is empty")
// 		return
// 	}

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			return
// 		}
// 	}

// 	// 制作爆仓信息
// 	_burstPosInfo := new(burstPosInfo)
// 	_burstPosInfo._updatePos(burstId, assetInfo, userCache, settingInfo, posInfo, []repository.PosSwap{},
// 		levelFilter, time.Now().UnixNano(), pCache, userLevelRateInfo)

// 	if _burstPosInfo.UserLevelRate.UserType == userTypePlatformRobot {
// 		err := cachelock.UnlockBurstUser(repository.BurstLockParam{
// 			ContractCode: posInfo.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        userCache.UID,
// 				MarginMode: domain.MarginModeIsolated,
// 				PosSide:    posInfo.PosSide,
// 				BurstTime:  time.Now().UnixNano(),
// 			},
// 		}, true)
// 		if err != nil {
// 			logrus.Error(0, "isolatedBurstTrigger isolated", posInfo.ContractCode, "UnlockBurstUser robot user err:", err)
// 		}
// 		return
// 	}

// 	// 仓位强制清空标记
// 	_burstPosInfo.IsBurstToZero = isLastLevel

// 	// 仓位爆仓类型
// 	if isLastLevel {
// 		_burstPosInfo.LiquidationType = domain.LiquidationTypeBurst
// 		// 发送爆仓通知
// 		go util.SendMailAndSmsToNoticeBurst(posInfo.UID, posInfo.ContractCode, domain.MSBurst,
// 			"", pCache.GetMarkPrice(posInfo.ContractCode))
// 	} else {
// 		_burstPosInfo.LiquidationType = domain.LiquidationTypeReduce
// 		// 发送减仓通知
// 		go util.SendMailAndSmsToNoticeBurst(posInfo.UID, posInfo.ContractCode, domain.MSReduce, "", decimal.Zero)
// 	}

// 	_, _burstPosInfo.PNL = bs._posPNL(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial(), _burstPosInfo.CollapsePrice,
// 		posInfo.OpenPriceAvg, posInfo.ProfitUnreal, "makeBurstData", pCache)

// 	// 发送异步爆仓记录
// 	bs._sendBurstLog(*_burstPosInfo, posInfo, levelFilter.HoldingMarginRate, holdMargin, true)
// 	return
// }

// // checkTrialIsolateBurst 检查体验金逐仓爆仓
// func (bs *workingBurstService) checkTrialIsolateBurst(userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, posInfo repository.PosSwap) {
// 	isolatedLocking := true

// 	feeRateInfo, err := GetContractUserLevelRate(userCache.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, "TrialIsolate checkTrialIsolateBurst", userCache.UID, "error:", err)
// 		return
// 	}
// 	if feeRateInfo.UserType == userTypePlatformRobot {
// 		return
// 	}

// 	// 检查是否有扫描锁
// 	getIsolatedLock, isolatedLockErr := cachelock.UserTrialScanTryLock(userCache.UID, domain.MarginModeIsolated, bs.contractCode)
// 	if isolatedLockErr != nil {
// 		logrus.Error(0, bs.contractCode, "TrialIsolated UserTrialScanTryLock", userCache.UID, "error:", isolatedLockErr)
// 	}

// 	// 如果抢到全仓扫描锁
// 	if getIsolatedLock {
// 		isolatedLocking = false
// 		defer cachelock.UserTrialScanUnlock(userCache.UID, domain.MarginModeIsolated, bs.contractCode)
// 	}

// 	// 扫描被其他线程锁定
// 	if isolatedLocking {
// 		return
// 	}

// 	// 获取逐仓爆仓锁状态
// 	locked, err := cachelock.TrialBurstUserIsLocked(repository.BurstLockParam{
// 		ContractCode: bs.contractCode,
// 		Liquidation: cache.LiquidationInfo{
// 			UID:        userCache.UID,
// 			MarginMode: domain.MarginModeIsolated,
// 			PosSide:    posInfo.PosSide,
// 			IsTrialPos: posInfo.IsTrial(),
// 		},
// 	})
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, userCache.UID, "checkTrialIsolateBurst BurstUserIsLocked error:", err)
// 		return
// 	}

// 	// 逐仓锁
// 	if locked {
// 		checkRecordKey := cachekey.GetTrialBurstWorkingUsersRedisKey(domain.MarginModeIsolated)
// 		err = redisCli.Client().SAdd(context.Background(), checkRecordKey, assetInfo.UID).Err()
// 		if err != nil {
// 			logrus.Error(0, "checkCrossBurst MarginModeCross SAdd error:", err)
// 		}
// 		return
// 	}

// 	pCache := price.New()
// 	posBase, posQuote := util.BaseQuote(posInfo.ContractCode)
// 	posValue := posInfo.CalcPosHoldValue()

// 	// 仓位级别及维持保证金率
// 	marginLevel, isLastLevel, err := setting.FetchMarginLevel(posBase, posQuote, posValue)
// 	if err != nil {
// 		logrus.Error(0, posInfo.ContractCode, posInfo.UID, "checkIsolateBurst FetchMarginLevel error:", err)
// 		return
// 	}

// 	holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
// 	warnMargin := marginLevel.WarnMarginRate.Mul(posValue)
// 	isolatedMarkMarginBalance := posInfo.IsolatedMarginBalance(pCache)

// 	logrus.Info(0, bs.contractCode, userCache.UID, "holdMargin:", holdMargin, "isolatedMarkMarginBalance:", isolatedMarkMarginBalance)

// 	// 所有全仓维持保证金， 所有持仓仓位级别及维持保证金率 或 需要强制清仓
// 	if holdMargin.GreaterThan(decimal.Zero) && isolatedMarkMarginBalance.LessThanOrEqual(holdMargin) {
// 		// 生成爆仓id
// 		burstId := util.GenerateId()

// 		{
// 			// 检查是否触发重生卡
// 			activatingRebornCard, err := cache.GetActivatingRebornCard(userCache.UID)
// 			if err != nil {
// 				logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst GetActivatingRebornCard error:", err)
// 			}
// 			// 判断重生卡是否生效中
// 			if len(activatingRebornCard.Id) > 0 && time.Now().Unix()-activatingRebornCard.TriggerTime < activatingRebornCard.Period {
// 				return
// 			}

// 			// 重生卡获取
// 			rebornCard, err := cache.GetAvailableRebornCard(userCache.UID, posInfo.ContractCode)
// 			if err != nil {
// 				logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst GetAvailableRebornCard error:", err)
// 			}
// 			// 如果重生卡有效就进行激活
// 			if len(rebornCard.Id) > 0 {
// 				posIds := []string{
// 					posInfo.PosId,
// 				}

// 				activateOk, err := cache.ActivateRebornCard(posIds, rebornCard)
// 				if err != nil {
// 					logrus.Error(0, userCache.UID, posInfo.ContractCode, "checkCrossBurst ActivateRebornCard error:", err)
// 				}
// 				// 激活成功直接免爆仓
// 				if activateOk {
// 					return
// 				}
// 			}
// 		}

// 		// 获取user费率
// 		userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 		if err != nil {
// 			logrus.Error(0, bs.contractCode, assetInfo.UID, "checkIsolateBurst GetContractUserLevelRate error:", err)
// 			// 获取默认setting等级
// 			if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 				logrus.Info(0, bs.contractCode, assetInfo.UID, "checkIsolateBurst use default taker rate")
// 				userLevelRateInfo.UID = assetInfo.UID
// 				userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 			} else {
// 				logrus.Error(0, bs.contractCode, "checkIsolateBurst no level 0 taker rate:", err)
// 				return
// 			}
// 		}

// 		collapsePrice, collapsePriceFormula := posInfo.IsolatedCollapsePrice(userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker)
// 		if collapsePrice.LessThanOrEqual(decimal.Zero) {
// 			base, quote := util.BaseQuote(strings.ToUpper(posInfo.ContractCode))
// 			collapsePrice, err = sharedcache.GetContractLastPrice(base, quote)
// 			if err != nil {
// 				logrus.Error(0, posInfo.ContractCode, posInfo.UID, "updatePos MarginModeIsolated GetContractLastPrice error:", err)
// 			}
// 		}

// 		liquidationType := domain.LiquidationTypeBurst
// 		cancelType := domain.CancelTypeBurst
// 		targetLimit := decimal.Zero
// 		targetLevel := 0
// 		// 保证金仓位直接爆仓,无需减仓
// 		isLastLevel = true

// 		// 增加爆仓锁
// 		err = cachelock.LockBurstSymbol(repository.BurstLockParam{
// 			ContractCode: posInfo.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				BurstId:              burstId,
// 				UID:                  userCache.UID,
// 				PosId:                posInfo.PosId,
// 				MarginMode:           domain.MarginModeIsolated,
// 				PosSide:              posInfo.PosSide,
// 				LiquidationType:      liquidationType,
// 				CurrentLevel:         marginLevel.Level,
// 				TargetLevel:          targetLevel,
// 				TargetLimit:          targetLimit,
// 				CancelType:           cancelType,
// 				CollapsePrice:        collapsePrice,
// 				CollapsePriceFormula: collapsePriceFormula,
// 				BurstTime:            time.Now().Unix(),
// 				IsTrialPos:           posInfo.IsTrial(),
// 			},
// 		})
// 		if err != nil {
// 			logrus.Error(0, posInfo.ContractCode, "crossBurstTrigger LockBurstUser", userCache.UID, "error:", err)
// 		}
// 		logrus.Info(0, bs.contractCode, userCache.UID, "isolated position burst")
// 		bs.trialIsolatedBurstTrigger(burstId, userCache, assetInfo, posInfo, marginLevel, holdMargin, isLastLevel, pCache)
// 		return
// 	}

// 	// 触发警告
// 	if warnMargin.GreaterThan(decimal.Zero) && isolatedMarkMarginBalance.LessThanOrEqual(warnMargin) {
// 		// 最后一级按风险率发送预警
// 		riskRate := holdMargin.Div(isolatedMarkMarginBalance)
// 		if isolatedMarkMarginBalance.GreaterThan(decimal.Zero) && riskRate.GreaterThanOrEqual(decimal.NewFromFloat(0.8)) {
// 			// 发送爆仓预警通知
// 			go util.SendMailAndSmsToNoticeBurst(bs.contractCode, userCache.UID, domain.MSBurstWarn, "80", decimal.Zero)
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) trialIsolatedBurstTrigger(burstId string, userCache *swapcache.PosCache, assetInfo *repository.AssetSwap, posInfo repository.PosSwap,
// 	levelFilter repository.LevelFilter, holdMargin decimal.Decimal, isLastLevel bool, pCache *price.PCache,
// ) {
// 	logrus.Info(0, "trialIsolatedBurstTrigger pos", fmt.Sprintf("%+v", posInfo))

// 	tradePosSide := posInfo.PosSide
// 	if tradePosSide == domain.BothPos {
// 		if posInfo.Pos.GreaterThan(decimal.Zero) {
// 			tradePosSide = domain.LongPos
// 		} else {
// 			if posInfo.Pos.LessThan(decimal.Zero) {
// 				tradePosSide = domain.ShortPos
// 			}
// 		}
// 	}

// 	go _forcePushRivalScoreZero(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial())

// 	// 获取合约币对配置
// 	ContractSettings.Lock()
// 	settingInfo, settingOk := ContractSettings.Map[posInfo.ContractCode]
// 	ContractSettings.Unlock()
// 	if !settingOk {
// 		logrus.Error(0, "isolatedBurstTrigger cross", posInfo.ContractCode, "get BurstSettings is empty")
// 		return
// 	}

// 	// 获取user费率
// 	userLevelRateInfo, err := GetContractUserLevelRate(assetInfo.UID)
// 	if err != nil {
// 		logrus.Error(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger GetContractUserLevelRate error:", err)
// 		// 获取默认setting等级
// 		if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 			logrus.Info(0, bs.contractCode, assetInfo.UID, "crossBurstTrigger use default taker rate")
// 			userLevelRateInfo.UID = assetInfo.UID
// 			userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 		} else {
// 			logrus.Error(0, bs.contractCode, "crossBurstTrigger no level 0 taker rate:", err)
// 			return
// 		}
// 	}

// 	// 制作爆仓信息
// 	_burstPosInfo := new(burstPosInfo)
// 	_burstPosInfo._updatePos(burstId, assetInfo, userCache, settingInfo, posInfo, []repository.PosSwap{},
// 		levelFilter, time.Now().UnixNano(), pCache, userLevelRateInfo)

// 	if _burstPosInfo.UserLevelRate.UserType == userTypePlatformRobot {
// 		err := cachelock.UnlockBurstUser(repository.BurstLockParam{
// 			ContractCode: posInfo.ContractCode,
// 			Liquidation: cache.LiquidationInfo{
// 				UID:        userCache.UID,
// 				MarginMode: domain.MarginModeIsolated,
// 				PosSide:    posInfo.PosSide,
// 				IsTrialPos: true,
// 				BurstTime:  time.Now().UnixNano(),
// 			},
// 		}, true)
// 		if err != nil {
// 			logrus.Error(0, "isolatedBurstTrigger isolated", posInfo.ContractCode, "UnlockBurstUser robot user err:", err)
// 		}
// 		return
// 	}

// 	// 仓位强制清空标记
// 	_burstPosInfo.IsBurstToZero = isLastLevel

// 	// 仓位爆仓类型
// 	_burstPosInfo.LiquidationType = domain.LiquidationTypeBurst
// 	// 发送爆仓通知
// 	go util.SendMailAndSmsToNoticeBurst(posInfo.UID, posInfo.ContractCode, domain.MSBurst,
// 		"", pCache.GetMarkPrice(posInfo.ContractCode))

// 	_, _burstPosInfo.PNL = bs._posPNL(userCache.UID, posInfo.ContractCode, tradePosSide, posInfo.IsTrial(), _burstPosInfo.CollapsePrice,
// 		posInfo.OpenPriceAvg, posInfo.ProfitUnreal, "makeBurstData", pCache)

// 	// 发送异步爆仓记录
// 	bs._sendBurstLog(*_burstPosInfo, posInfo, levelFilter.HoldingMarginRate, holdMargin, true)
// 	return
// }

// // UpdateRivalScore 更新对手方强制建仓指数  对手方强制减仓指数=未实现盈亏/保证金*(保证金率=仓位价值*维持保证金率/保证金余额)
// // 排序 = 盈利百分比 * 有效杠杆  (如果盈利)
// //
// //	= 盈利百分比 / 有效杠杆  (如果亏损)
// func (bs *workingBurstService) UpdateRivalScore(uid string, _assetInfo *repository.AssetSwap, _userCache *swapcache.PosCache) {
// 	posList := []repository.PosSwap{
// 		_assetInfo.LongPos,
// 		_assetInfo.ShortPos,
// 	}

// 	trialLongPos := _assetInfo.TrialLongPos
// 	trialShortPos := _assetInfo.TrialShortPos
// 	// 因为没有任何仓位的时候AwardOpIds是空的，无法区分是否是体验金仓位，所以给一个随意值，来区分是体验金仓位
// 	trialLongPos.AwardIds = append(trialLongPos.AwardIds, "12345")
// 	trialShortPos.AwardIds = append(trialShortPos.AwardIds, "12345")
// 	posList = append(posList, trialLongPos)
// 	posList = append(posList, trialShortPos)

// 	if _assetInfo.PositionMode == domain.HoldModeBoth {
// 		posList = []repository.PosSwap{
// 			_assetInfo.BothPos,
// 		}
// 		trialBothPos := _assetInfo.TrialBothPos
// 		trialBothPos.AwardIds = append(trialLongPos.AwardIds, "12345")
// 		posList = append(posList, trialBothPos)
// 	}

// 	pCache := price.New()
// 	for _, pos := range posList {
// 		posSide := pos.PosSide
// 		// 是否保证金仓位
// 		isTrialPos := pos.IsTrial()
// 		if posSide == domain.BothPos {
// 			if pos.Pos.GreaterThan(decimal.Zero) {
// 				posSide = domain.LongPos
// 				logrus.Info(0, "RemoveRivalScore from zset", _userId, pos.ContractCode, posSide, isTrialPos)
// 				swapcache.RemoveRivalScore(_userId, pos.ContractCode, posSide, isTrialPos)
// 			} else if pos.Pos.LessThan(decimal.Zero) {
// 				posSide = domain.ShortPos
// 				logrus.Info(0, "RemoveRivalScore from zset", _userId, pos.ContractCode, posSide, isTrialPos)
// 				swapcache.RemoveRivalScore(_userId, pos.ContractCode, posSide, isTrialPos)
// 			}
// 		}
// 		// 标记价值
// 		markPrice := pCache.GetMarkPrice(pos.ContractCode)

// 		// 获取taker费率
// 		userLevelRateInfo, err := GetContractUserLevelRate(_userId)
// 		if err != nil {
// 			logrus.Error(0, "UpdateRivalScore", _userId, "GetContractUserLevelRate error:", err)
// 			// 获取默认setting等级
// 			if levelInfo, ok := DefaultUserLevelRate.Map[0]; ok {
// 				logrus.Error(0, _userId, bs.contractCode, "UpdateRivalScore use default taker rate")
// 				userLevelRateInfo.UID = _userId
// 				userLevelRateInfo.ContractTaker = levelInfo.ContractTaker
// 			} else {
// 				logrus.Error(0, bs.contractCode, "UpdateRivalScore no level 0 taker rate error:", err, _userId)
// 				continue
// 			}
// 		}

// 		collapsePrice := decimal.Zero
// 		if pos.Isolated() {
// 			// 破产价值
// 			collapsePrice, _ = pos.IsolatedCollapsePrice(userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker)
// 		} else {
// 			// 破产价值
// 			collapsePrice, _, err = pos.CrossCollapsePrice(_assetInfo, _userCache.HoldCostTotalIsolated(pos.Currency, true),
// 				_userCache.OtherCrossUnreal(pCache), _userCache.OtherCrossMaintainMargin(pCache),
// 				userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker, pCache)
// 			if err != nil {
// 				logrus.Error(0, bs.contractCode, "UpdateRivalScore HoldCostTotalIsolated error:", err, _userId)
// 				continue
// 			}
// 		}

// 		// 有效杠杆 = abs(标记价值) / (标记价值 - 破产价值)
// 		if markPrice.Sub(collapsePrice).IsZero() {
// 			logrus.Info(0, "RemoveRivalScore from zset", _userId, pos.ContractCode, posSide, isTrialPos)
// 			swapcache.RemoveRivalScore(_userId, pos.ContractCode, posSide, isTrialPos)
// 			continue
// 		}
// 		// 盈利百分比 = (标记价值 - 平均开仓价值) / abs(平均开仓价值)
// 		if pos.OpenPriceAvg.IsZero() {
// 			// logrus.Info(0, "RemoveRivalScore from zset", _userId, pos.ContractCode, posSide, isTrialPos)
// 			swapcache.RemoveRivalScore(_userId, pos.ContractCode, posSide, isTrialPos)
// 			continue
// 		}
// 		// 未实现盈亏
// 		posUnreal := pos.CalcProfitUnreal(pCache)
// 		if posUnreal.IsZero() && pos.Pos.IsZero() {
// 			logrus.Info(0, "RemoveRivalScore from zset", _userId, pos.ContractCode, posSide, isTrialPos)
// 			swapcache.RemoveRivalScore(_userId, pos.ContractCode, posSide, isTrialPos)
// 			continue
// 		}
// 		rivalScoreSuccess, rivalScore := bs._posPNL(_userId, pos.ContractCode, posSide, isTrialPos, collapsePrice, pos.OpenPriceAvg,
// 			posUnreal, "CrossCollapsePrice", pCache)

// 		if rivalScoreSuccess {
// 			bs.SaveRivalScore(rivalScore, _userId, pos.ContractCode, posSide, isTrialPos)
// 		}
// 	}

// 	return
// }

// func (bs *workingBurstService) _posPNL(_userId, symbol string, _posSide int32, _isTrialPos bool, _collapsePrice, _openPriceAvg,
// 	_profitUnreal decimal.Decimal, debug string, pCache *price.PCache,
// ) (bool, decimal.Decimal) {
// 	logrus.Info(0, "posPNL", _userId, _contractCode, _posSide, _isTrialPos, _collapsePrice, _openPriceAvg, _profitUnreal)
// 	if _openPriceAvg.IsZero() {
// 		return false, decimal.Zero
// 	}
// 	if _collapsePrice.IsZero() {
// 		logrus.Error(0, _userId, _contractCode, _posSide, "pos pnl collapsePrice is zero ===================================================", debug)
// 		logrus.Info(0, "RemoveRivalScore from zset", _userId, _contractCode, _posSide, _isTrialPos)
// 		swapcache.RemoveRivalScore(_userId, _contractCode, _posSide, _isTrialPos)
// 		return false, decimal.Zero
// 	}
// 	// 标记价值
// 	markPrice := pCache.GetMarkPrice(_contractCode)
// 	// 有效杠杆 = abs(标记价值) / (标记价值 - 破产价值)
// 	if markPrice.Sub(_collapsePrice).IsZero() {
// 		return false, decimal.Zero
// 	}
// 	if markPrice.IsZero() {
// 		return false, decimal.Zero
// 	}
// 	leverageRate := markPrice.Abs().Div(markPrice.Sub(_collapsePrice))
// 	// 盈利百分比 = (标记价值 - 平均开仓价值) / abs(平均开仓价值)
// 	profitPercent := markPrice.Sub(_openPriceAvg).Div(_openPriceAvg.Abs())

// 	// 排序 = 盈利百分比 * 有效杠杆  (如果盈利)
// 	if _profitUnreal.GreaterThanOrEqual(decimal.Zero) {
// 		if leverageRate.IsZero() {
// 			logrus.Info(0, "RemoveRivalScore from zset", _userId, _contractCode, _posSide, _isTrialPos)
// 			swapcache.RemoveRivalScore(_userId, _contractCode, _posSide, _isTrialPos)
// 			return false, decimal.Zero
// 		}
// 		rivalScore := profitPercent.Mul(leverageRate)
// 		bs.SaveRivalScore(rivalScore, _userId, _contractCode, _posSide, _isTrialPos)
// 		return true, rivalScore
// 	}
// 	// 排序 = 盈利百分比 / 有效杠杆  (如果亏损)
// 	if _profitUnreal.LessThan(decimal.Zero) {
// 		if leverageRate.IsZero() {
// 			logrus.Info(0, "RemoveRivalScore from zset", _userId, _contractCode, _posSide, _isTrialPos)
// 			swapcache.RemoveRivalScore(_userId, _contractCode, _posSide, _isTrialPos)
// 			return false, decimal.Zero
// 		}
// 		rivalScore := profitPercent.Div(leverageRate)
// 		bs.SaveRivalScore(rivalScore, _userId, _contractCode, _posSide, _isTrialPos)
// 		return true, rivalScore
// 	}
// 	return false, decimal.Zero
// }

// func (bs *workingBurstService) SaveRivalScore(_rivalScore decimal.Decimal, uid string, symbol string, _posSide int32, _isTrialPos bool) {
// 	if len(_contractCode) < 1 {
// 		return
// 	}
// 	rivalScoreFloat64, _ := _rivalScore.Float64()
// 	switch _posSide {
// 	case domain.LongPos:
// 		rivalLongRankListKey := cachekey.GetBurstRivalLongRankRedisKey(_contractCode)
// 		if _isTrialPos {
// 			_userId = swapcache.TrialUserId(_userId)
// 		}
// 		err := redisCli.ZAdd(rivalLongRankListKey, _userId, rivalScoreFloat64)
// 		logrus.Info(0, "SaveRivalScore to zset", rivalLongRankListKey, _userId, rivalScoreFloat64, err)
// 		if err != nil && err != redis.Nil {
// 			logrus.Error(0, fmt.Sprintf("SaveRivalScore %s ZAdd error: %s", rivalLongRankListKey, err))
// 			return
// 		}
// 		go _pushRivalScore(_userId, _contractCode, domain.LongPos, _isTrialPos)
// 	case domain.ShortPos:
// 		rivalShortRankListKey := cachekey.GetBurstRivalShortRankRedisKey(_contractCode)
// 		if _isTrialPos {
// 			_userId = swapcache.TrialUserId(_userId)
// 		}
// 		err := redisCli.ZAdd(rivalShortRankListKey, _userId, rivalScoreFloat64)
// 		logrus.Info(0, "SaveRivalScore to zset", rivalShortRankListKey, _userId, rivalScoreFloat64, err)
// 		if err != nil && err != redis.Nil {
// 			logrus.Error(0, "SaveRivalScore", rivalShortRankListKey, "ZAdd error:", err)
// 			return
// 		}
// 		go _pushRivalScore(_userId, _contractCode, domain.ShortPos, _isTrialPos)
// 	}
// 	return
// }

// func _pushRivalScore(_userId, symbol string, _posSide int32, _isTrialPos bool) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(0, "pushRivalScore error:", err)
// 		}
// 	}()
// 	base, quote := util.BaseQuote(_contractCode)
// 	uid := swapcache.RemoveUserIdTrial(_userId)
// 	contractRivalRatePushLock := cache.GetContractRivalRatePushLockRedisKey(base, quote, uid, _posSide)
// 	if _isTrialPos {
// 		contractRivalRatePushLock = cache.GetContractTrialRivalRatePushLockRedisKey(base, quote, uid, _posSide)
// 	}
// 	lockExist := redislib.Redis().Exist(contractRivalRatePushLock)
// 	if lockExist {
// 		return
// 	}

// 	err := redislib.Redis().SetString(contractRivalRatePushLock, fmt.Sprintf("%d", time.Now().Unix()))
// 	if err != nil {
// 		logrus.Error(0, "pushRivalScore SetString", contractRivalRatePushLock, err)
// 	}
// 	err = redislib.Redis().SetExpire(contractRivalRatePushLock, "30s")
// 	if err != nil {
// 		logrus.Error(0, "pushRivalScore SetExpire", contractRivalRatePushLock, err)
// 	}

// 	// 计算排名占比
// 	rankPercent := swapcache.RivalScoreRate(_userId, _contractCode, _posSide, _isTrialPos)

// 	// 推送需要对手方比率
// 	logrus.Info(0, "pushRivalScore to mq", uid, _contractCode, _posSide, _isTrialPos, rankPercent.String())
// 	message.New(uid, message.PosQueueIndex, mqlib.CommonAmqp).
// 		TopicRivalRatePrice(message.SwapAccount).
// 		Push(map[string]interface{}{
// 			"uid":            uid,
// 			"isTrialPos":     _isTrialPos,
// 			"contractCode":   _contractCode,
// 			"posSide":        _posSide,
// 			"rivalScoreRate": rankPercent.String(),
// 		})

// 	return
// }

// func (bs *workingBurstService) initGroup(_size int) {
// 	for i := 0; i < _size; i++ {
// 		bs.userGroup = append(bs.userGroup, make(chan []string))
// 		go bs.burstScanner(i)
// 	}
// }

// func (bs *workingBurstService) InitScannerGroup(_group int) {
// 	bs.initGroup(_group)
// }

// func scannerStop(_symbolList []string) {
// 	for _, symbol := range _symbolList {
// 		snapScannerContext.Lock()
// 		ctxCancelList, ok := snapScannerContext.Map[symbol]
// 		snapScannerContext.Unlock()
// 		if ok {
// 			for _, ctxCancel := range ctxCancelList {
// 				ctxCancel()
// 			}
// 			snapScannerContext.Lock()
// 			delete(snapScannerContext.Map, symbol)
// 			snapScannerContext.Unlock()
// 		}

// 		RunningServices.RLock()
// 		_, hasBurstWorker := RunningServices.Map[strings.ToUpper(symbol)]
// 		RunningServices.RUnlock()
// 		if hasBurstWorker {
// 			RunningServices.Lock()
// 			delete(RunningServices.Map, symbol)
// 			RunningServices.Unlock()
// 		}
// 	}
// }

// // 发送爆仓记录
// func (bs *workingBurstService) _sendBurstLog(burstPosInfo burstPosInfo, posInfo repository.PosSwap, holdingMarginRate, holdingMargin decimal.Decimal, isNormalBurst bool) {
// 	if burstPosInfo.LongPosAmount.IsZero() && burstPosInfo.ShortPosAmount.IsZero() {
// 		logrus.Error(0, posInfo.ContractCode, "burstService sendBurstLog zero pos amount.", fmt.Sprintf("%+v", burstPosInfo))
// 		return
// 	}

// 	jsonBytes, _ := json.Marshal(burstPosInfo)
// 	redisKey := cachekey.GetBurstListRedisKey(posInfo.ContractCode, burstPosInfo.MarginMode)
// 	// 常规爆仓判断
// 	if isNormalBurst {
// 		// 记录爆仓信息
// 		bs._logBurstData(posInfo.ContractCode, holdingMarginRate, holdingMargin, redisKey, jsonBytes)
// 	}

// 	openTimeStr := fmt.Sprintf("%d", posInfo.OpenTime)
// 	if len(openTimeStr) < 11 {
// 		posInfo.OpenTime = posInfo.OpenTime * 1e9
// 	}

// 	logData := swap.LogBurstSwap{
// 		BurstId:              burstPosInfo.BurstId,
// 		PosId:                posInfo.PosId,
// 		PosSide:              posInfo.PosSide,
// 		UID:                  posInfo.UID,
// 		UserType:             burstPosInfo.UserType,
// 		AccountType:          posInfo.AccountType,
// 		Base:                 burstPosInfo.Base,
// 		Quote:                burstPosInfo.Quote,
// 		Currency:             posInfo.Currency,
// 		Leverage:             posInfo.Leverage,
// 		MarginMode:           burstPosInfo.MarginMode,
// 		BurstPrice:           burstPosInfo.BurstPrice,
// 		MarkPrice:            burstPosInfo.MarkPrice,
// 		CollapsePrice:        burstPosInfo.CollapsePrice,
// 		CollapsePriceFormula: burstPosInfo.CollapsePriceFormula,
// 		LiquidationPrice:     burstPosInfo.LiquidationPrice,
// 		LiquidationType:      int(burstPosInfo.LiquidationType),
// 		FrozenMargin:         burstPosInfo.FrozenMargin,
// 		MarginBalance:        burstPosInfo.MarginBalance,
// 		TotalMargin:          burstPosInfo.TotalMargin,
// 		PosMargin:            burstPosInfo.PosMargin,
// 		HoldingMargin:        holdingMargin,
// 		PNL:                  burstPosInfo.PNL,
// 		HoldingMarginRate:    holdingMarginRate,
// 		Level:                burstPosInfo.BurstLevel,
// 		OpenTime:             posInfo.OpenTime,
// 		CreateTime:           burstPosInfo.BurstTime,
// 		IsTrialPos:           0,
// 	}
// 	if burstPosInfo.IsTrialPos {
// 		logData.IsTrialPos = 1
// 	}

// 	switch posInfo.PosSide {
// 	case domain.LongPos:
// 		logData.LongPos = posInfo.Pos.Abs()
// 		logData.LongPosValue = posInfo.PosValue
// 		logData.LongOpenPrice = posInfo.OpenPriceAvg
// 		logData.LongPosUnreal = posInfo.ProfitUnreal

// 	case domain.ShortPos:
// 		logData.ShortPos = posInfo.Pos.Abs()
// 		logData.ShortPosValue = posInfo.PosValue
// 		logData.ShortOpenPrice = posInfo.OpenPriceAvg
// 		logData.ShortPosUnreal = posInfo.ProfitUnreal

// 	case domain.BothPos:
// 		if posInfo.Pos.GreaterThan(decimal.Zero) {
// 			logData.LongPos = posInfo.Pos.Abs()
// 			logData.LongPosValue = posInfo.PosValue
// 			logData.LongOpenPrice = posInfo.OpenPriceAvg
// 			logData.LongPosUnreal = posInfo.ProfitUnreal
// 		} else if posInfo.Pos.LessThan(decimal.Zero) {
// 			logData.ShortPos = posInfo.Pos.Abs()
// 			logData.ShortPosValue = posInfo.PosValue
// 			logData.ShortOpenPrice = posInfo.OpenPriceAvg
// 			logData.ShortPosUnreal = posInfo.ProfitUnreal
// 		}

// 	}

// 	// 常规爆仓判断
// 	if isNormalBurst { // push爆仓任务
// 		logData.BurstPosData = string(jsonBytes)
// 	}
// 	logDataBytes, err := logData.MarshalBinary()
// 	if err != nil {
// 		logrus.Error(0, "burstService sendBurstLog Marshal burstInfo error: %s", err, fmt.Sprintf("%+v", burstPosInfo))
// 		return
// 	}
// 	swapcache.SendSwapTask(posInfo.ContractCode, cache.SwapBurstTask{
// 		Type:     cache.TaskTypeNewBurst,
// 		Data:     logDataBytes,
// 		FuncName: "",
// 	})

// 	return
// }
