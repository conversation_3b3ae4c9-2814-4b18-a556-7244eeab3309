package utils

import (
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

type AccountSettleParam struct {
	AccountSettle *futuresEnginePB.AccountSettleEngine
}

// GetUID 从AccountSettleEngine中提取TradeID
func (p *AccountSettleParam) GetTradeId() string {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetTradeId: accountSettle is nil")
		return ""
	}

	tradeId := p.AccountSettle.GetTradeId()
	return tradeId
}

// GetAmount 从AccountSettleEngine中提取交易数量
func (p *AccountSettleParam) GetAmount() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetAmount: accountSettle is nil")
		return decimal.Zero
	}

	amount, err := decimal.NewFromString(p.AccountSettle.GetAmount())
	if err != nil {
		logrus.Warnf("tp0803 GetAmount: failed to parse amount %s: %v", p.AccountSettle.GetAmount(), err)
		return decimal.Zero
	}
	return amount
}

// GetPrice 从AccountSettleEngine中提取价格
func (p *AccountSettleParam) GetPrice() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetPrice: accountSettle is nil")
		return decimal.Zero
	}

	price, err := decimal.NewFromString(p.AccountSettle.GetPrice())
	if err != nil {
		logrus.Warnf("tp0803 GetPrice: failed to parse price %s: %v", p.AccountSettle.GetPrice(), err)
		return decimal.Zero
	}
	return price
}

// GetAccountSettleType 从AccountSettleEngine中提取Type
func (p *AccountSettleParam) GetAccountSettleType() string {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetAccountSettleType: accountSettle is nil")
		return ""
	}

	accountSettleType := p.AccountSettle.GetType()
	return accountSettleType
}

// GetFee 从AccountSettleEngine中提取FeedRate
func (p *AccountSettleParam) GetFeedRate() decimal.Decimal {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetFeedRate: accountSettle is nil")
		return decimal.Zero
	}

	feeRate, err := decimal.NewFromString(p.AccountSettle.GetFeedRate())
	if err != nil {
		logrus.Warnf("tp0803 GetFeedRate: failed to parse feedRate %s: %v", p.AccountSettle.GetFeedRate(), err)
		return decimal.Zero
	}
	return feeRate
}

// GetLiquidationType 从AccountSettleEngine中提取清算类型
func (p *AccountSettleParam) GetLiquidationType() domain.LiquidationType {
	if p.AccountSettle == nil {
		logrus.Warn("tp0803 GetLiquidationType: accountSettle is nil")
		return domain.LiquidationTypeNone
	}

	liquidationType := domain.LiquidationType(p.AccountSettle.GetLiquidationType())
	return liquidationType
}

// GetUID 从AccountSettleEngine中提取订单ID
func (p *AccountSettleParam) GetOrderID() string {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetOrderID: accountSettle or accountSettle.Order is nil")
		return ""
	}

	uid := p.AccountSettle.GetOrder().GetOrderId()
	return uid
}

// GetUID 从AccountSettleEngine中提取用户ID
func (p *AccountSettleParam) GetUID() string {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetUID: accountSettle or accountSettle.Order is nil")
		return ""
	}

	uid := p.AccountSettle.GetOrder().GetUserId()
	return uid
}

// GetBaseQuote 从AccountSettleEngine中提取交易对信息
func (p *AccountSettleParam) GetBaseQuote() (string, string) {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetBaseQuote: accountSettle or accountSettle.Order is nil")
		return "", ""
	}

	base, quote := p.AccountSettle.GetOrder().GetSymbol().GetBase(), p.AccountSettle.GetOrder().GetSymbol().GetQuote()
	return base, quote
}

// IsOpenPosition 判断是否为开仓操作
func (p *AccountSettleParam) IsOpenPosition() bool {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 IsOpenPosition: accountSettle or accountSettle.Order is nil")
		return false
	}

	side := p.AccountSettle.GetOrder().GetSide()
	posSide := p.AccountSettle.GetOrder().GetPosSide()

	// 买入多仓或卖出空仓为开仓
	return (side == domain.Buy && posSide == domain.Long) ||
		(side == domain.Sell && posSide == domain.Short)
}

// GetSide 从AccountSettleEngine中提取交易方向
func (p *AccountSettleParam) GetSide() int {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetSide: accountSettle or accountSettle.Order is nil")
		return 0
	}

	side := int(p.AccountSettle.GetOrder().GetSide())
	return side
}

// GetUserType 从AccountSettleEngine中提取用户类型
func (p *AccountSettleParam) GetUserType() int32 {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetUserType: accountSettle or accountSettle.Order is nil")
		return 0
	}

	userType := int32(p.AccountSettle.GetOrder().GetUserType())
	return userType
}

// GetMarginMode 从AccountSettleEngine中提取保证金模式
func (p *AccountSettleParam) GetMarginMode() domain.MarginMode {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetMarginMode: accountSettle or accountSettle.Order is nil")
		return domain.MarginModeNone
	}

	marginMode := domain.MarginMode(p.AccountSettle.GetOrder().GetMarginMode())
	return marginMode
}

// GetLeverage 从AccountSettleEngine中提取杠杆倍数
func (p *AccountSettleParam) GetLeverage() int {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetLeverage: accountSettle or accountSettle.Order is nil")
		return 1
	}

	leverage := int(p.AccountSettle.GetOrder().GetLeverage())
	return leverage
}

// GetFeeRateTaker 从AccountSettleEngine中提取FeeRateTaker
func (p *AccountSettleParam) GetFeeRateTaker() decimal.Decimal {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetFeeRateTaker: accountSettle or accountSettle.Order is nil")
		return decimal.Zero
	}

	feeRateTaker, err := decimal.NewFromString(p.AccountSettle.GetOrder().GetFeeRateTaker())
	if err != nil {
		logrus.Warnf("tp0803 GetFeeRateTaker: failed to parse feeRateTaker %s: %v", p.AccountSettle.GetOrder().GetFeeRateTaker(), err)
		return decimal.Zero
	}
	return feeRateTaker
}

// GetFeeRateMaker 从AccountSettleEngine中提取FeeRateMaker
func (p *AccountSettleParam) GetFeeRateMaker() decimal.Decimal {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetFeeRateMaker: accountSettle or accountSettle.Order is nil")
		return decimal.Zero
	}

	feeRateMaker, err := decimal.NewFromString(p.AccountSettle.GetOrder().GetFeeRateMaker())
	if err != nil {
		logrus.Warnf("tp0803 GetFeeRateMaker: failed to parse feeRateMaker %s: %v", p.AccountSettle.GetOrder().GetFeeRateMaker(), err)
		return decimal.Zero
	}
	return feeRateMaker
}

// GetOrderType 从AccountSettleEngine中提取订单类型
func (p *AccountSettleParam) GetOrderType() int {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetOrderType: accountSettle or accountSettle.Order is nil")
		return 0
	}

	orderType := int(p.AccountSettle.GetOrder().GetType())
	return orderType
}

// GetOrderFronzen 从AccountSettleEngine中提取订单冻结
func (p *AccountSettleParam) GetOrderFronzen() decimal.Decimal {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetOrderFronzen: accountSettle or accountSettle.Order is nil")
		return decimal.Zero
	}

	orderFronzen, err := decimal.NewFromString(p.AccountSettle.GetOrder().GetFrozen())
	if err != nil {
		logrus.Warnf("tp0803 GetOrderFronzen: failed to parse frozen %s: %v", p.AccountSettle.GetOrder().GetFrozen(), err)
		return decimal.Zero
	}
	return orderFronzen
}

// GetUnfilledVolume 从AccountSettleEngine中提取未成交数量
func (p *AccountSettleParam) GetUnfilledVolume() decimal.Decimal {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetUnfilledVolume: accountSettle or accountSettle.Order is nil")
		return decimal.Zero
	}

	unfilledVolume, err := decimal.NewFromString(p.AccountSettle.GetOrder().GetUnfilledVolume())
	if err != nil {
		logrus.Warnf("tp0803 GetUnfilledVolume: failed to parse unfilledVolume %s: %v", p.AccountSettle.GetOrder().GetUnfilledVolume(), err)
		return decimal.Zero
	}
	return unfilledVolume
}

// GetUnfilledAmount 从AccountSettleEngine中提取未成交金额
func (p *AccountSettleParam) GetUnfilledAmount() decimal.Decimal {
	if p.AccountSettle == nil || p.AccountSettle.GetOrder() == nil {
		logrus.Warn("tp0803 GetUnfilledAmount: accountSettle or accountSettle.Order is nil")
		return decimal.Zero
	}

	unfilledAmount, err := decimal.NewFromString(p.AccountSettle.GetOrder().GetUnfilledAmount())
	if err != nil {
		logrus.Warnf("tp0803 GetUnfilledAmount: failed to parse unfilledAmount %s: %v", p.AccountSettle.GetOrder().GetUnfilledAmount(), err)
		return decimal.Zero
	}
	return unfilledAmount
}
