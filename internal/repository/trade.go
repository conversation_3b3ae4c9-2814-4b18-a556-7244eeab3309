package repository

import (
	"context"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"go.uber.org/dig"
	"gorm.io/gorm"
)

type TradeRepositoryParam struct {
	dig.In

	DB *gorm.DB `name:"db"`
}

type TradeRepository struct {
	db *gorm.DB
}

func NewTradeRepository(param TradeRepositoryParam) repository.TradeRepository {
	return &TradeRepository{
		db: param.DB,
	}
}

// Create 建立委託單
func (repo *TradeRepository) Create(ctx context.Context, order *entity.Trade) error {
	err := repo.db.WithContext(ctx).Create(order).Error

	if err != nil {
		return repository.InsertRecordError{DBErr: err}
	}

	return nil
}

// Update 更新
func (repo *TradeRepository) Update(ctx context.Context, trade *entity.Trade) error {
	err := repo.db.WithContext(ctx).Save(trade).Error
	if err != nil {
		return repository.UpdateRecordError{DBErr: err}
	}

	return nil
}

func (repo *TradeRepository) Page(ctx context.Context, param repository.TradePageParam) ([]entity.Trade, int64, error) {
	sql := repo.db.WithContext(ctx)

	var data []entity.Trade
	var count int64

	if param.UID != "" {
		sql = sql.Where("uid = ?", param.UID)
	}

	if param.ContractType != 0 {
		sql = sql.Where("contract_type = ?", param.ContractType)
	}

	if param.Symbol != "" {
		sql = sql.Where("symbol = ?", param.Symbol)
	}

	if param.OrderID != "" {
		sql = sql.Where("order_id = ?", param.OrderID)
	}

	if param.StartTime != 0 && param.EndTime != 0 {
		sql = sql.Where("create_time between ? and ?", param.StartTime, param.EndTime)
	}

	if param.Side != 0 {
		sql = sql.Where("side = ?", param.Side)
	}

	if param.PageIndex != 0 && param.PageSize != 0 {
		sql = sql.Offset((param.PageIndex - 1) * param.PageSize).Limit(param.PageSize)
	}

	if err := sql.Model(&entity.Trade{}).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	err := sql.
		Order("create_time DESC").
		Find(&data).Error
	if err != nil {
		// sql Take 才需要用到這個
		//if errors.Is(err, gorm.ErrRecordNotFound) {
		//	return []entity.Trade{}, 0, repository.ErrRecordNotFound
		//}

		return []entity.Trade{}, 0, err
	}

	return data, count, nil
}
