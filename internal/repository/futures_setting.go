package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
	futuresSetting "yt.com/backend/common.git/business/futures/setting"
)

type SettingRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	MDB *mongo.Database      `name:"mongodb"`
	RDB *redis.ClusterClient `name:"redis-cluster"`
}

type settingRepository struct {
	db  *gorm.DB
	mdb *mongo.Database
	rdb *redis.ClusterClient
}

func NewSettingRepository(param SettingRepositoryParam) repository.SettingRepository {
	return &settingRepository{
		db:  param.DB,
		mdb: param.MDB,
		rdb: param.RDB,
	}
}

// AllContractPair implements repository.SettingRepository.
func (repo *settingRepository) AllContractPair() (repository.AllContractPairReply, error) {
	pairs := repository.AllContractPairReply{}

	return pairs, nil
}

// FetchMarginLevel implements repository.SettingRepository.
func (repo *settingRepository) FetchMarginLevel(ctx context.Context, symbol string, posTotalValue decimal.Decimal) (repository.LevelFilter, bool, error) {
	retryTimes := 0
	lowLimitSortList := repository.LevelLowSortList{}
Retry:
	tempHoldSortList, err := repo.GetMarginRateLevel(ctx, symbol)
	if err != nil {
		err = repo.UpdateAllPairInfo(ctx)
		if err != nil {
			return repository.LevelFilter{}, false, errors.New(fmt.Sprintln(fmt.Sprintf("%s", symbol), "FetchMarginLevel UpdateAllPairInfo error:", err))
		}
		retryTimes += 1
		if retryTimes > 3 {
			return repository.LevelFilter{}, false, errors.New(fmt.Sprintln("FetchMarginLevel retry times >= 3"))
		}
		goto Retry
	}

	lowLimitSortList = repository.LevelLowSortList(tempHoldSortList)

	sort.Sort(sort.Reverse(lowLimitSortList))

	tempLevel := repository.LevelFilter{}
	tempIsLast := false
	for k, lData := range lowLimitSortList {
		// 如果是最后一个直接跳出
		if k == lowLimitSortList.Len()-1 {
			tempLevel, tempIsLast = lData, true
			break
		}
		// 在等级范围内的就是当前等级
		if posTotalValue.GreaterThanOrEqual(lData.LowLimit) && (lData.HighLimit.Equal(decimal.NewFromInt(-1)) || posTotalValue.LessThanOrEqual(lData.HighLimit)) {
			tempLevel = lData
			break
		}
	}

	if tempLevel.Lever == 0 {
		return repository.LevelFilter{}, false, errors.New("HoldingMarginRate leverage is zero")
	}

	return tempLevel, tempIsLast, nil
}

// NextMarginLevel 获取下一个级维持保证金率信息
//
//	Params:
//	  base int: 交易币
//	  quote int: 计价币
//	  level int: 当前仓位等级
//
//	Return:
//	  0 LevelFilter: 仓位等级信息
//	  1 error: 错误信息
func (repo *settingRepository) NextMarginLevel(ctx context.Context, symbol string, level int) (repository.LevelFilter, error) {
	tempLevel := repository.LevelFilter{}
	contractSettings, err := repo.GetMarginRateLevel(ctx, symbol)
	if err != nil {
		return tempLevel, err
	}
	tempLowSortList := repository.LevelLowSortList(contractSettings)
	sort.Sort(sort.Reverse(tempLowSortList))

	nextBreak := false

	for _, filterInfo := range tempLowSortList {
		if level == filterInfo.Level {
			nextBreak = true
			continue
		}
		if nextBreak {
			tempLevel = filterInfo
			break
		}
	}

	if !nextBreak {
		return tempLevel, errors.New(fmt.Sprintf("%s no match level %d next", symbol, level))
	}

	return tempLevel, nil
}

// GetAllPairSettingInfo implements repository.SettingRepository.
func (repo *settingRepository) GetAllPairSettingInfo(ctx context.Context) (map[string]repository.ContractPair, error) {
	coinPairSettings := make(map[string]repository.ContractPair)
	coinPairSettingMap, err := repo.rdb.HGetAll(ctx, domain.ContractSetting).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return coinPairSettings, errors.Wrap(err, "get all pair setting from redis")
		}
	}
	if len(coinPairSettingMap) > 0 {
		for coinPairName, coinPairSettingStr := range coinPairSettingMap {
			coinPairSetting := new(repository.ContractPair)
			err := json.Unmarshal([]byte(coinPairSettingStr), &coinPairSetting)
			if err != nil {
				logrus.Errorln(fmt.Sprintf("setting GetAllPairInfo error: %s", err.Error()))
				continue
			}
			coinPairSettings[coinPairName] = *coinPairSetting
		}
	}

	return coinPairSettings, err
}

// GetCachePair implements repository.SettingRepository.
func (repo *settingRepository) GetCachePair(ctx context.Context, symbol string) (*repository.ContractPair, error) {
	pairInfo := new(repository.ContractPair)
	b := repo.rdb.HGet(ctx, domain.ContractSetting, symbol).Val()
	if b != "" {
		err := json.Unmarshal([]byte(b), &pairInfo)
		if err != nil {
			return pairInfo, err
		}
		return pairInfo, nil
	}

	return repo.GetPairSettingInfo(ctx, symbol)
}

// GetCoinWhiteListConfigMap implements repository.SettingRepository.
func (repo *settingRepository) GetCoinWhiteListConfigMap(ctx context.Context) (map[string]repository.CurrencyWhiteListConfig, error) {
	cacheHash, err := repo.rdb.HGetAll(ctx, "Wallet_Coin").Result()
	if err != nil {
		logrus.Error(fmt.Sprintln("GetCoinWhiteListConfigMap from redis error:", err))
		return nil, err
	}
	configMap := make(map[string]repository.CurrencyWhiteListConfig)
	for currency, currencyConfig := range cacheHash {
		r := repository.CurrencyWhiteListConfig{}
		err := json.Unmarshal([]byte(currencyConfig), &r)
		if err != nil {
			logrus.Error(fmt.Sprintln("GetCoinWhiteListConfigMap Unmarshal error:", err))
			continue
		}
		configMap[strings.ToUpper(currency)] = r
	}
	return configMap, nil
}

// GetMarginRateLevel implements repository.SettingRepository.
func (repo *settingRepository) GetMarginRateLevel(ctx context.Context, symbol string) (repository.LevelHoldSortList, error) {
	var levelFilters repository.LevelHoldSortList

	var pairSettings futuresSetting.ContractSetting
	pairSettingStr, err := repo.rdb.HGet(ctx, domain.ContractSetting, symbol).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logrus.WithFields(logrus.Fields{
			"err":    err,
			"symbol": symbol,
			"key":    domain.ContractSetting,
		}).Error(fmt.Sprintln(symbol, "GetMarginRateLevel HGet redis error:", err))

		return levelFilters, err
	}
	if len(pairSettingStr) < 1 {
		return levelFilters, errors.New("no margin rate level data")
	}

	err = json.Unmarshal([]byte(pairSettingStr), &pairSettings)
	if err != nil {
		return levelFilters, err
	}

	for _, item := range pairSettings.MarginBrackets {
		levelFilters = append(levelFilters, repository.LevelFilter{
			Level:             item.Level,
			Lever:             item.LeverageMax,
			HighLimit:         item.LimitUpper,
			LowLimit:          item.LimitLower,
			InitMarginRate:    item.MarginRateInit,
			WarnMarginRate:    item.MarginRateWarn,
			HoldingMarginRate: item.MarginRateHold,
		})
	}

	return levelFilters, nil
}

// GetPairSettingInfo implements repository.SettingRepository.
func (repo *settingRepository) GetPairSettingInfo(ctx context.Context, symbol string) (*repository.ContractPair, error) {
	coinPairSetting := new(repository.ContractPair)
	jsonStr, err := repo.rdb.HGet(ctx, domain.ContractSetting, strings.ToUpper(symbol)).Result()

	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logrus.Error("GetPairSettingInfo redis HGet error:", err)
			return nil, err
		}
	} else {
		err = json.Unmarshal([]byte(jsonStr), &coinPairSetting)
		if err != nil {
			logrus.Error(fmt.Sprintln("GetPairSettingInfo Unmarshal error:", err, jsonStr))
			return nil, err
		}
	}

	return coinPairSetting, nil
}

// GetUserLevelsRate implements repository.SettingRepository.
func (repo *settingRepository) GetUserLevelsRate() (repository.UserLevelsData, error) {
	userLevelsReply := repository.UserLevelsData{}
	// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["setting"], urlLevelConfig)
	// 	params := map[string]interface{}{}
	// 	reply, err := httplib.Post(url, params)
	// 	if err != nil {
	// 		return userLevelsReply.Data, err
	// 	}
	// 	if err := json.Unmarshal(reply, &userLevelsReply); err != nil {
	// 		return userLevelsReply.Data, err
	// 	}
	return userLevelsReply, nil
}

// UpdateAllPairInfo implements repository.SettingRepository.
func (repo *settingRepository) UpdateAllPairInfo(ctx context.Context) error {
	// resObj := new(repository.CoinPairsReply)
	// params := map[string]interface{}{
	// 	"viewType": 1,
	// }
	// paramsBytes, _ := json.Marshal(params)
	// url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["setting"], urlCoinPair)
	// response, err := http.Post(url, "application/json", bytes.NewBuffer(paramsBytes))
	// if err != nil {
	// 	logrus.Error(fmt.Sprintln("UpdatePairInfo Post error:", err))
	// 	return err
	// }
	// resBytes, err := ioutil.ReadAll(response.Body)
	// if err != nil {
	// 	logrus.Error(fmt.Sprintln("UpdatePairInfo ReadAll error:", err))
	// 	return err
	// }

	// err = json.Unmarshal(resBytes, resObj)
	// if err != nil {
	// 	logrus.Error(fmt.Sprintln("UpdatePairInfo Unmarshal error:", err, string(resBytes)))
	// 	return err
	// }
	// if resObj.Code != 200 {
	// 	return fmt.Errorf("response code error: %d", resObj.Code)
	// }
	// if len(resObj.Data) > 0 {
	// 	for _, pairInfo := range resObj.Data {
	// 		// 保存setting数据
	// 		contractCode := strings.ToUpper(fmt.Sprintf("%s-%s", pairInfo.Base, pairInfo.Quote))
	// 		pairInfoBytes, _ := json.Marshal(pairInfo)
	// 		err := redislib.Redis().HSet(getContractSettingRedisKey(), contractCode, string(pairInfoBytes))
	// 		if err != nil {
	// 			logrus.Error("UpdatePairInfo redis HSet error:", err, string(pairInfoBytes))
	// 			continue
	// 		}
	// 		// 保存level数据
	// 		var levelList LevelHoldSortList
	// 		for _, marginRateInfo := range pairInfo.MarginRateGear {
	// 			levelList = append(levelList, LevelFilter{
	// 				Level:             marginRateInfo.Level,
	// 				Lever:             marginRateInfo.LeverMultiple,
	// 				HighLimit:         marginRateInfo.End.Truncate(domain.CurrencyPrecision),
	// 				LowLimit:          marginRateInfo.Start.Truncate(domain.CurrencyPrecision),
	// 				InitMarginRate:    marginRateInfo.InitRate.Truncate(domain.RatePrecision),
	// 				WarnMarginRate:    marginRateInfo.WarnRate.Truncate(domain.RatePrecision),
	// 				HoldingMarginRate: marginRateInfo.MaintenanceRate.Truncate(domain.RatePrecision),
	// 			})
	// 		}

	// 		if len(levelList) > 0 {
	// 			sort.Sort(sort.Reverse(levelList))
	// 			levelBytes, _ := json.Marshal(levelList)
	// 			err := redislib.Redis().HSet(getContractSettingLevelRedisKey(), contractCode, string(levelBytes))
	// 			if err != nil {
	// 				logrus.Error("UpdateAllPairInfo redis error:", err)
	// 				continue
	// 			}
	// 		}
	// 	}
	// }

	return nil
}
