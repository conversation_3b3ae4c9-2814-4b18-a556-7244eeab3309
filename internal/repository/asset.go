package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type AssetRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	MDB *mongo.Database      `name:"mongodb"`
	RDB *redis.ClusterClient `name:"redis-cluster"`

	PriceRepo   repository.PriceRepository
	SettingRepo repository.SettingRepository
}

type assetRepository struct {
	db  *gorm.DB
	mdb *mongo.Database
	rdb *redis.ClusterClient

	priceRepo   repository.PriceRepository
	settingRepo repository.SettingRepository
}

func NewAssetRepository(param AssetRepositoryParam) repository.AssetRepository {
	return &assetRepository{
		db:  param.DB,
		mdb: param.MDB,
		rdb: param.RDB,

		priceRepo:   param.PriceRepo,
		settingRepo: param.SettingRepo,
	}
}

// GetBtachUserAsset implements repository.AssetRepository.
func (repo *assetRepository) GetBatchUserAsset(ctx context.Context, req repository.BalanceReq) ([]entity.Wallet, error) {
	var data []entity.Wallet
	query := repo.db.WithContext(ctx).
		Select("uid, currency, balance, frozen, update_time")

	if len(req.Currencies) > 0 {
		query = query.Where("currency IN (?)", req.Currencies)
	}
	if len(req.UIDs) > 0 {
		query = query.Where("uid IN (?)", req.UIDs)
	}

	err := query.Find(&data).Error
	if err != nil {
		return nil, err
	}

	return data, nil
}

// SumUserTotalAsset implements repository.AssetRepository
func (repo *assetRepository) SumUserTotalAsset(ctx context.Context, req repository.TotalAssetReq) ([]entity.Wallet, error) {
	var data []entity.Wallet
	query := repo.db.WithContext(ctx).Select("currency, sum(balance+frozen) as balance")

	if len(req.UIDs) > 0 {
		query = query.Where("uid in (?)", req.UIDs)
	}
	if len(req.UIDsNotIn) > 0 {
		query = query.Where("uid not in (?)", req.UIDsNotIn)
	}

	if err := query.Group("currency").Find(&data).Error; err != nil && err != gorm.ErrRecordNotFound {
		return data, err
	}

	return data, nil
}

// UpsertAsset implements repository.AssetRepository.
func (repo *assetRepository) UpsertAsset(ctx context.Context, data entity.Wallet) error {
	query := repo.db.WithContext(ctx)

	insertColumns := "`id`, `uid`, `currency`, `balance`, `frozen`, `create_time`"
	updateColumns := "balance=?, frozen=?, update_time=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		data.TableName(), insertColumns, updateColumns)
	if err := query.Exec(sql,
		data.GetId(), data.UID, data.Currency, data.Balance,
		data.Frozen, data.CreateTime,
		data.Balance, data.Frozen, time.Now().UnixNano(),
	).Error; err != nil {
		return err
	}

	return nil
}

// UpdateAsset 更新数据
func (repo *assetRepository) UpdateAsset(ctx context.Context, data entity.Wallet, changeType commonpb.FundingChangeType) error {
	query := repo.db.WithContext(ctx)
	if data.UID == "" || data.Currency == "" {
		return fmt.Errorf("assetRepository.UpdateAsset wallet: %+v", data)
	}

	values := map[string]interface{}{"update_time": time.Now().UnixNano()}
	switch changeType {
	case commonpb.FundingChangeType_FundingChangeType_Balance:
		values["balance"] = data.Balance
	case commonpb.FundingChangeType_FundingChangeType_Frozen:
		values["frozen"] = data.Frozen
	default:
		return fmt.Errorf("assetRepository.UpdateAsset wrong changetype: %+v", data)
	}

	where := "uid = ? and currency = ?"
	return query.Model(&entity.Wallet{}).Where(where, data.UID, data.Currency).Updates(values).Error
}

func (repo *assetRepository) CanTransfer(ctx context.Context, asset *repository.AssetSwap, currency string) (decimal.Decimal, error) {
	currency = strings.ToUpper(currency)
	canTrans := decimal.Zero
	switch asset.AssetMode {
	case futuresassetpb.AssetMode_ASSET_MODE_MULTI:
		usdValBalance, err := repo.TotalJoinBalance(ctx, asset)
		if err != nil {
			logrus.Error("CanTransfer TotalJoinBalance error:", err)
			return decimal.Zero, err
		}

		usdValHoldCostTotal := repo.HoldCostTotal(ctx, asset, currency)
		usdValFrozenTotal := repo.FrozenTotal(ctx, asset, currency)
		usdValTrialHoldCostTotal := repo.HoldCostTrialTotal(ctx)

		canTrans = usdValBalance.Sub(usdValHoldCostTotal.Sub(usdValTrialHoldCostTotal)).Sub(usdValFrozenTotal)

		usdValTotalUnreal := repo.CrossUnrealTotal(ctx, asset, currency)
		usdValTrialUnreal := repo.CrossTrialUnrealTotal(ctx)
		usdValRealTotalUnreal := decimal.Min(decimal.NewFromInt(0), usdValTotalUnreal.Sub(usdValTrialUnreal))

		logrus.Infoln("==============", asset.UID, currency, "usdValBalance", usdValBalance)
		logrus.Infoln("==============", asset.UID, currency, "usdValHoldCostTotal", usdValHoldCostTotal)
		logrus.Infoln("==============", asset.UID, currency, "usdValFrozenTotal", usdValFrozenTotal)
		logrus.Infoln("==============", asset.UID, currency, "usdValTrialHoldCostTotal", usdValTrialHoldCostTotal)
		logrus.Infoln("==============", asset.UID, currency, "usdValTotalUnreal", usdValTotalUnreal)
		logrus.Infoln("==============", asset.UID, currency, "usdValTrialUnreal", usdValTrialUnreal)
		logrus.Infoln("==============", asset.UID, currency, "usdValRealTotalUnreal", usdValRealTotalUnreal)

		canTrans = canTrans.Add(usdValRealTotalUnreal)
		logrus.Infoln("==============", asset.UID, currency, "canTrans", canTrans)

		cBalance := asset.CBalance(currency)
		logrus.Infoln("==============", asset.UID, currency, "cBalance", cBalance)

		rate := repo.priceRepo.SpotRate(ctx, currency, domain.CurrencyUSDT)
		logrus.Infoln("==============", asset.UID, currency, "rate", rate)

		if rate.IsPositive() {
			if usdValHoldCostTotal.Add(usdValFrozenTotal).IsZero() {
				canTrans = cBalance
			} else {
				canTrans = decimal.Min(cBalance, canTrans.Div(rate).Truncate(domain.CoinPrecision))
			}
		} else {
			return decimal.Zero, nil
		}

	default:
		balance := asset.CBalance(currency)
		holdCostTotal := repo.HoldCostTotal(ctx, asset, currency)
		frozenTotal := repo.FrozenTotal(ctx, asset, currency)
		holdCostTrialTotal := repo.HoldCostTrialTotal(ctx)

		canTrans = balance.Sub(holdCostTotal.Sub(holdCostTrialTotal)).Sub(frozenTotal)

		totalUnreal := repo.CrossUnrealTotal(ctx, asset, currency)
		totalTrialUnreal := repo.CrossTrialUnrealTotal(ctx)
		realTotalUnreal := decimal.Min(decimal.NewFromInt(0), totalUnreal.Sub(totalTrialUnreal))

		logrus.Infoln("==============", asset.UID, currency, "balance", balance)
		logrus.Infoln("==============", asset.UID, currency, "holdCostTotal", holdCostTotal)
		logrus.Infoln("==============", asset.UID, currency, "frozenTotal", frozenTotal)
		logrus.Infoln("==============", asset.UID, currency, "holdCostTrialTotal", holdCostTrialTotal)
		logrus.Infoln("==============", asset.UID, currency, "totalUnreal", totalUnreal)
		logrus.Infoln("==============", asset.UID, currency, "totalTrialUnreal", totalTrialUnreal)
		logrus.Infoln("==============", asset.UID, currency, "realTotalUnreal", realTotalUnreal)

		canTrans = canTrans.Add(realTotalUnreal)

		logrus.Infoln("==============", asset.UID, currency, "canTrans", canTrans)
	}

	return canTrans.Truncate(domain.CoinPrecision), nil
}

// GetAvailableBase implements repository.CacheRepository.
func (repo *assetRepository) GetAvailableBase(ctx context.Context, asset *repository.AssetSwap, marginMode futuresassetpb.MarginMode, currency string) (decimal.Decimal, error) {
	available := decimal.Zero
	var err error = nil

	totalBalance := asset.CBalance(currency)
	totalTrialBalance := asset.TrialCBalance(currency)

	holdCostTotal := repo.HoldCostTotal(ctx, asset, currency)
	logrus.Info(1, asset.UID, "---------- holdCostTotal", holdCostTotal)
	holdCostTrialTotal := repo.HoldCostTrialTotal(ctx)
	logrus.Info(1, asset.UID, "---------- holdCostTrialTotal", holdCostTrialTotal)
	frozenTotal := repo.FrozenTotal(ctx, asset, currency)
	logrus.Info(1, asset.UID, "---------- frozenTotal", frozenTotal)

	// 总余额 = 账户余额 + 体验金
	totalBalance = totalBalance.Add(totalTrialBalance)

	switch marginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		logrus.Info(1, asset.UID, "---------- totalBalance + totalTrialBalance", totalBalance)
		logrus.Info(1, asset.UID, "---------- totalTrialBalance", totalTrialBalance)

		// 账户余额 - ∑n{逐仓持仓成本}  - ∑m{全仓持仓成本} - ∑{全部标的委托冻结}
		available = totalBalance.Sub(holdCostTotal).Add(holdCostTrialTotal).Sub(frozenTotal)

	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
		switch asset.AssetMode {
		case futuresassetpb.AssetMode_ASSET_MODE_MULTI:
			totalBalance, err = repo.TotalJoinBalance(ctx, asset)
			if err != nil {
				return decimal.Zero, err
			}
			totalTrialBalance, err = asset.TotalJoinTrialBalance()
			if err != nil {
				return decimal.Zero, err
			}

			// 总余额 = 账户余额 + 体验金
			totalBalance = totalBalance.Add(totalTrialBalance)

			logrus.Info(1, asset.UID, "---------- totalBalance + totalTrialBalance", totalBalance)
			logrus.Info(1, asset.UID, "---------- totalTrialBalance", totalTrialBalance)

			totalUnreal := repo.CrossUnrealTotal(ctx, asset, currency)
			logrus.Info(1, asset.UID, "---------- totalUnreal", totalUnreal)

			//  ∑{全部交易区全部币种余额 * USD汇率} - ∑n{全部交易区全部标的逐仓持仓成本 * USD汇率} - ∑m{全部交易区全部标的全仓持仓成本 * USD汇率} - ∑{全部交易区全部标的委托冻结 * USD汇率} + ∑m{全部交易区全部标的全仓未实现盈亏 * USD汇率}
			available = totalBalance.Sub(holdCostTotal).Add(holdCostTrialTotal).Sub(frozenTotal).Add(totalUnreal)

		case futuresassetpb.AssetMode_ASSET_MODE_SINGLE:
			logrus.Info(1, asset.UID, "---------- totalBalance + totalTrialBalance", totalBalance)
			logrus.Info(1, asset.UID, "---------- totalTrialBalance", totalTrialBalance)

			totalUnreal := repo.CrossUnrealTotal(ctx, asset, currency)
			logrus.Info(1, asset.UID, "---------- totalUnreal", totalUnreal)

			// 当前交易区计价币账户余额 - ∑n{当前交易区全部标的逐仓持仓成本}  - ∑m{当前交易区全部标的全仓持仓成本} - ∑{当前交易区全部标的委托冻结} + ∑m{当前交易区全仓未实现盈亏}
			available = totalBalance.Sub(holdCostTotal).Add(holdCostTrialTotal).Sub(frozenTotal).Add(totalUnreal)

		default:
		}

	default:
	}

	return available, nil
}

// CalcTrialMargin implements repository.AssetRepository.
func (repo *assetRepository) CalcTrialMargin(ctx context.Context, asset *repository.AssetSwap, currency string, posMargin decimal.Decimal, pos repository.PosSwap) (repository.PosSwap, []*entity.TrialAsset) {
	// TODO: 拦截函数返回, 不实用老的体验金
	if true {
		return pos, make([]*entity.TrialAsset, 0)
	}

	totalPosMargin := posMargin
	trialBalance := asset.TrialCBalance(currency)
	trialBillList := make([]*entity.TrialAsset, 0)

	// 当前体验金够扣
	if trialBalance.GreaterThanOrEqual(totalPosMargin) {
		pos.TrialMargin = pos.TrialMargin.Add(totalPosMargin)
		trialBillList = asset.ConsumeTrialBalance(currency, totalPosMargin)
		return pos, trialBillList
	}

	// 如果有体验金有余额
	if trialBalance.GreaterThan(decimal.Zero) {
		pos.TrialMargin = pos.TrialMargin.Add(trialBalance)
		trialAssetList := asset.ConsumeTrialBalance(currency, trialBalance)
		trialBillList = append(trialBillList, trialAssetList...)
		totalPosMargin = totalPosMargin.Sub(trialBalance)
	}

	if !pos.Isolated() && asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		currencyURate := repo.priceRepo.SpotURate(ctx, currency)
		uPosMargin := totalPosMargin.Mul(currencyURate)
		// 混合保证金要看其他币种体验金是否够用
		for _, platformCurrency := range domain.CurrencyList {
			if platformCurrency == strings.ToUpper(currency) {
				continue
			}
			otherURate := repo.priceRepo.SpotURate(ctx, platformCurrency)
			otherTrialBalance := asset.TrialCBalance(platformCurrency)
			otherTrialUBalance := otherTrialBalance.Mul(otherURate)

			if otherTrialUBalance.GreaterThanOrEqual(uPosMargin) {
				// 当前余额折合够扣
				otherCurrencyAmount := uPosMargin.Div(otherURate)
				trialBillList = asset.ConsumeTrialBalance(currency, otherCurrencyAmount)
				pos.TrialMargin = pos.TrialMargin.Add(uPosMargin.Div(currencyURate).Truncate(domain.CurrencyPrecision))
				return pos, trialBillList
			} else {
				// 当前余额折合不够扣
				trialAssetList := asset.ConsumeTrialBalance(currency, otherTrialBalance)
				trialBillList = append(trialBillList, trialAssetList...)
				pos.TrialMargin = pos.TrialMargin.Add(otherTrialUBalance.Div(currencyURate).Truncate(domain.CurrencyPrecision))
				uPosMargin = uPosMargin.Sub(otherTrialUBalance)
			}
		}
	}

	return pos, trialBillList
}

// TotalJoinBalance implements repository.AssetRepository.
func (repo *assetRepository) TotalJoinBalance(ctx context.Context, asset *repository.AssetSwap) (balance decimal.Decimal, err error) {
	for c, v := range asset.Balance {
		if v == decimal.Zero {
			continue
		}

		if c == domain.CurrencyUSDT {
			balance = balance.Add(v)
		} else {
			rate := repo.priceRepo.GetIndexPrice(ctx, c)
			if rate.IsZero() {
				return decimal.Zero, errors.New(fmt.Sprintf("%s-USDT rate exciption: %s", c, rate))
			}

			balance = balance.Add(v.Mul(rate))
		}
	}

	return balance.Truncate(domain.CurrencyPrecision), nil
}

// UpdateUserHoldPos implements repository.AssetRepository.
func (repo *assetRepository) UpdateUserHoldPos(ctx context.Context, asset *repository.AssetSwap, long repository.PosSwap, short repository.PosSwap, both repository.PosSwap, userType int32, trial bool) error {
	contractCode := asset.LongPos.Symbol
	if len(contractCode) <= 0 {
		contractCode = asset.ShortPos.Symbol
	}
	currency := asset.LongPos.Currency
	if len(contractCode) <= 0 {
		currency = asset.ShortPos.Currency
	}
	marginMode := asset.LongPos.MarginMode
	if marginMode == 0 {
		marginMode = asset.ShortPos.MarginMode
	}

	holdPos := &repository.UserHoldPos{
		UID:        asset.UID,
		UserType:   userType,
		Symbol:     contractCode,
		MarginMode: marginMode,
		Currency:   currency,
	}

	holdPos.LongPos = long.Pos
	holdPos.LongOpenTime = long.OpenTime
	holdPos.LongPosId = long.PosId
	holdPos.LongPriceAvg = long.OpenPriceAvg
	holdPos.LongIsolatedMargin = long.IsolatedMargin
	holdPos.LongTrialMargin = long.TrialMargin

	holdPos.ShortPos = short.Pos
	holdPos.ShortOpenTime = short.OpenTime
	holdPos.ShortPosId = short.PosId
	holdPos.ShortPriceAvg = short.OpenPriceAvg
	holdPos.ShortIsolatedMargin = short.IsolatedMargin
	holdPos.ShortTrialMargin = short.TrialMargin

	holdPos.BothPos = both.Pos
	holdPos.BothOpenTime = both.OpenTime
	holdPos.BothPosId = both.PosId
	holdPos.BothPriceAvg = both.OpenPriceAvg
	holdPos.BothIsolatedMargin = both.IsolatedMargin
	holdPos.BothTrialMargin = both.TrialMargin

	posStr, _ := json.Marshal(holdPos)
	cacheKey := domain.GetAllUserPosKey(contractCode)
	if trial {
		cacheKey = domain.GetAllUserTrialPosKey(contractCode)
	}
	err := repo.rdb.HSet(context.Background(), cacheKey, asset.UID, string(posStr)).Err()
	if err != nil {
		return errors.Wrapf(err, "[UpdateUserHoldPos] hset hold pos err")
	}

	return nil
}

// CrossMarginBalance implements repository.FormulaRepository.
func (repo *assetRepository) CrossMarginBalance(ctx context.Context, asset *repository.AssetSwap, uid, currency string) (decimal.Decimal, error) {
	totalBalance := asset.CBalance(currency)
	totalTrialBalance := asset.TrialCBalance(currency)
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		var err error = nil
		totalBalance, err = repo.TotalJoinBalance(ctx, asset)
		if err != nil {
			return decimal.Zero, err
		}
		rate = repo.priceRepo.SpotURate(ctx, currency)

		totalTrialBalance, err = asset.TotalJoinTrialBalance()
		if err != nil {
			return decimal.Zero, err
		}
	}

	totalBalance = totalBalance.Add(totalTrialBalance)
	crossUnrealTotal := repo.CrossUnrealTotal(ctx, asset, currency)
	isolatedHoldCost := repo.HoldCostTotalIsolated(ctx, asset, currency, true).Mul(rate)

	return totalBalance.Add(crossUnrealTotal).Sub(isolatedHoldCost).Truncate(domain.CurrencyPrecision), nil
}

// FrozenTotal implements repository.FormulaRepository.
func (repo *assetRepository) FrozenTotal(ctx context.Context, asset *repository.AssetSwap, area string, isTrials ...bool) decimal.Decimal {
	isTrial := len(isTrials) > 0 && isTrials[0]
	frozen := decimal.Zero

	logrus.Infof("user %s AssetMode %d PositionMode %d frozen: %+v", asset.UID, asset.AssetMode, asset.PositionMode, asset.Frozen)

	switch asset.AssetMode {
	case futuresassetpb.AssetMode_ASSET_MODE_MULTI:
		for k, v := range asset.Frozen {
			if v.IsZero() {
				continue
			}
			haveTrial, _, quote, _ := util.FrozenInfo2(k)
			if haveTrial != isTrial {
				continue
			}
			rate := repo.priceRepo.SpotURate(ctx, quote)
			if rate.IsZero() {
				logrus.Error(1, "FrozenTotal", fmt.Sprintf("%s-USDT rate exciption: %s", quote, rate))
			}
			frozen = frozen.Add(v.Mul(rate))
		}

	case futuresassetpb.AssetMode_ASSET_MODE_SINGLE:
		switch asset.PositionMode {
		case futuresassetpb.PositionMode_POSITION_MODE_HEDGE:
			for k, v := range asset.Frozen {
				logrus.Infof("---k %s v %s", k, v.String())
				if v.IsZero() {
					continue
				}
				haveTrial, _, quote, _ := util.FrozenInfo2(k)
				logrus.Infof("---haveTrial %v isTrial %v quote %s area %s", haveTrial, isTrial, quote, area)
				if haveTrial != isTrial {
					continue
				}
				if !strings.EqualFold(quote, area) {
					continue
				}
				frozen = frozen.Add(v)
				logrus.Infof("---frozen %s", frozen)
			}

		case futuresassetpb.PositionMode_POSITION_MODE_ONE_WAY:
			for k, v := range asset.CrossList {
				if !strings.HasSuffix(k, area) {
					continue
				}
				for i := range v {
					if v[i].PosSide == domain.BothPos {
						frozen = frozen.Add(asset.BothFrozen(k, v[i], false))
					}
				}
			}

			for k, v := range asset.IsolatedList {
				if !strings.HasSuffix(k, area) {
					continue
				}
				for i := range v {
					if v[i].PosSide == domain.BothPos {
						frozen = frozen.Add(asset.BothFrozen(k, v[i], false))
					}
				}
			}

		default:

		}

	default:

	}

	return frozen
}

// GetMarginBalanceAndMaintainMargin implements repository.FormulaRepository.
func (repo *assetRepository) GetMarginBalanceAndMaintainMargin(ctx context.Context, asset *repository.AssetSwap, pos repository.PosSwap) (decimal.Decimal, decimal.Decimal, error) {
	// 计算保证金余额
	if pos.Isolated() {
		// 计算维持保证金 逐仓单用逐仓
		markPrice := repo.priceRepo.GetMarkPrice(ctx, pos.Symbol)
		marginLevel, _, _ := repo.settingRepo.FetchMarginLevel(ctx, pos.Symbol, pos.CalcPosValue(markPrice))
		unreal := pos.IsolatedMargin.Add(pos.ProfitUnreal)
		margin := pos.Pos.Mul(markPrice).Mul(marginLevel.HoldingMarginRate).Truncate(domain.CurrencyPrecision)
		logrus.Infof("query user margin balance: %s maintain margin: %s (uid:%s maintenRate:%s markPrice:%s pos.ProfitUnreal:%s)",
			unreal, margin, pos.UID, marginLevel.HoldingMarginRate, markPrice, pos.ProfitUnreal)

		return unreal, margin, nil
	} else {
		// 计算维持保证金 全仓多空一起
		_, quote := util.BaseQuote(asset.LongPos.Symbol)
		markPrice := repo.priceRepo.GetMarkPrice(ctx, asset.LongPos.Symbol)
		marginLevel, _, _ := repo.settingRepo.FetchMarginLevel(ctx, asset.LongPos.Symbol, asset.LongPos.CalcPosValue(markPrice).
			Add(asset.ShortPos.CalcPosValue(markPrice)))
		unreal, err := repo.CrossMarginBalance(ctx, asset, asset.UID, quote)
		if err != nil {
			logrus.Error("GetMarginBalanceAndMaintainMargin CrossMarginBalance error:", err)
			return decimal.Zero, decimal.Zero, err
		}
		totalCrossMargin, _, _, _, err := repo.TotalCrossMaintainMargin(ctx, asset, asset.LongPos.Symbol)
		if err != nil {
			return decimal.Zero, decimal.Zero, err
		}
		logrus.Infof("query user margin balance: %s maintain margin: %s (uid:%s maintenRate:%s markPrice:%s pos.ProfitUnreal:%s)",
			unreal, totalCrossMargin, pos.UID, marginLevel.HoldingMarginRate, markPrice, pos.ProfitUnreal)

		return unreal, totalCrossMargin.Truncate(domain.CurrencyPrecision), nil
	}
}

// OptionsMarginBalance implements repository.FormulaRepository.
func (repo *assetRepository) OptionsMarginBalance(ctx context.Context, asset *repository.AssetSwap, currency string) (decimal.Decimal, error) {
	totalBalance := asset.CBalance(currency)
	totalTrialBalance := asset.TrialCBalance(currency)
	rate := decimal.NewFromInt(1)

	totalBalance = totalBalance.Add(totalTrialBalance)

	crossUnrealTotal := repo.CrossUnrealTotal(ctx, asset, currency)
	isolatedHoldCost := repo.HoldCostTotalIsolated(ctx, asset, currency, true).Mul(rate)

	return totalBalance.Add(crossUnrealTotal).Sub(isolatedHoldCost).Truncate(domain.CurrencyPrecision), nil
}

// OtherCrossMaintainMargin implements repository.FormulaRepository.
func (repo *assetRepository) OtherCrossMaintainMargin(ctx context.Context, asset *repository.AssetSwap, code string) decimal.Decimal {
	margin := decimal.Zero
	for _, posList := range asset.CrossList {
		if len(posList) <= 0 || code == strings.ToUpper(posList[0].Symbol) {
			continue
		}

		markPrice := repo.priceRepo.GetMarkPrice(ctx, posList[0].Symbol)
		longPosValue := posList[0].CalcPosValue(markPrice)
		shortPosValue := decimal.Zero
		if len(posList) == 2 {
			shortPosValue = posList[1].CalcPosValue(markPrice)
		}
		if posList[0].Symbol == "" {
			continue
		}
		base, quote := util.BaseQuote(posList[0].Symbol)

		// 过滤脏数据
		if base == quote {
			repo.rdb.HDel(ctx, domain.AssetPrefix.Key(posList[0].UID), fmt.Sprintf("%s:pos:both", posList[0].Symbol))
			repo.rdb.HDel(ctx, domain.AssetPrefix.Key(posList[0].UID), fmt.Sprintf("%s:pos:long", posList[0].Symbol))
			repo.rdb.HDel(ctx, domain.AssetPrefix.Key(posList[0].UID), fmt.Sprintf("%s:pos:short", posList[0].Symbol))
			continue
		}

		holdingMarginRate, _, _ := repo.settingRepo.FetchMarginLevel(ctx, posList[0].Symbol, longPosValue.Add(shortPosValue))

		totalMargin := longPosValue.Mul(holdingMarginRate.HoldingMarginRate).Add(shortPosValue.Mul(holdingMarginRate.HoldingMarginRate))
		margin = margin.Add(totalMargin)
	}

	return margin
}

// OtherCrossUnreal implements repository.FormulaRepository.
func (repo *assetRepository) OtherCrossUnreal(ctx context.Context, asset *repository.AssetSwap, code string) decimal.Decimal {
	crossUnreal := decimal.Zero
	pos := make([]repository.PosSwap, 0)
	for _, symbol := range asset.CrossList {
		pos = append(pos, symbol...)
	}
	for _, p := range pos {
		if p.Pos.IsZero() {
			continue
		}
		if code == strings.ToUpper(p.Symbol) {
			continue
		}

		markPrice := repo.priceRepo.GetMarkPrice(ctx, p.Symbol)
		p.ProfitUnreal = p.CalcProfitUnreal(markPrice)
		crossUnreal = crossUnreal.Add(p.ProfitUnreal)
	}

	return crossUnreal
}

// TotalCrossMaintainMargin implements repository.FormulaRepository.
func (repo *assetRepository) TotalCrossMaintainMargin(ctx context.Context, asset *repository.AssetSwap, code string) (decimal.Decimal, decimal.Decimal, map[string]bool, map[string]repository.LevelFilter, error) {
	levelFilterMap := make(map[string]repository.LevelFilter)
	holdMargin := decimal.Zero
	warnMargin := decimal.Zero
	isLastLevelMap := make(map[string]bool)
	_, currency := util.BaseQuote(code)

	isSingleMargin := asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE

	for _, posList := range asset.CrossList {
		if len(posList) < 1 {
			continue
		}
		posBase, posQuote := util.BaseQuote(posList[0].Symbol)
		if len(posBase) < 1 || len(posQuote) < 1 {
			continue
		}
		if isSingleMargin && posQuote != currency {
			continue
		}

		longPosValue := decimal.Zero
		shortPosValue := decimal.Zero
		for _, posInfo := range posList {
			if posInfo.Symbol == "" {
				continue
			}
			if posInfo.Pos.IsZero() {
				continue
			}
			switch posInfo.PosSide {
			case domain.LongPos:
				longPosValue = longPosValue.Add(posInfo.CalcPosHoldValue())

			case domain.ShortPos:
				shortPosValue = shortPosValue.Add(posInfo.CalcPosHoldValue())

			case domain.BothPos:
				if posInfo.Pos.GreaterThan(decimal.Zero) {
					longPosValue = longPosValue.Add(posInfo.CalcPosHoldValue())
				} else if posInfo.Pos.LessThan(decimal.Zero) {
					shortPosValue = shortPosValue.Add(posInfo.CalcPosHoldValue())
				}

			default:
			}
		}
		if longPosValue.Add(shortPosValue).LessThanOrEqual(decimal.Zero) {
			continue
		}

		symbolMarginLevel, symbolIsLastLevel, _ := repo.settingRepo.FetchMarginLevel(ctx, posList[0].Symbol, longPosValue.Add(shortPosValue))

		isLastLevelMap[posList[0].Symbol] = symbolIsLastLevel

		levelFilterMap[posList[0].Symbol] = symbolMarginLevel

		if !isSingleMargin {
			uRate := repo.priceRepo.SpotURate(ctx, currency)
			longPosValue = longPosValue.Mul(uRate)
			shortPosValue = shortPosValue.Mul(uRate)
		}

		tempHoldMargin := longPosValue.Mul(symbolMarginLevel.HoldingMarginRate).Add(shortPosValue.Mul(symbolMarginLevel.HoldingMarginRate))
		holdMargin = holdMargin.Add(tempHoldMargin)
		tempWarnMargin := longPosValue.Mul(symbolMarginLevel.WarnMarginRate).Add(shortPosValue.Mul(symbolMarginLevel.WarnMarginRate))
		warnMargin = warnMargin.Add(tempWarnMargin)
	}

	return holdMargin, warnMargin, isLastLevelMap, levelFilterMap, nil
}

// CrossUnrealTotal 全部全仓的未实现盈亏 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *assetRepository) CrossUnrealTotal(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	unreal := decimal.Zero

	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		for _, posList := range asset.CrossList {
			for _, v := range posList {
				if v.Pos.IsZero() {
					continue
				}
				if v.IsTrial() {
					continue
				}
				if !strings.HasSuffix(v.Symbol, strings.ToUpper(area)) {
					continue
				}
				v.ProfitUnreal = v.CalcProfitUnreal(repo.priceRepo.GetMarkPrice(ctx, v.Symbol))
				unreal = unreal.Add(v.ProfitUnreal)
			}
		}
	} else if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		for _, posList := range asset.CrossList {
			for _, v := range posList {
				if v.Pos.IsZero() {
					continue
				}
				if v.IsTrial() {
					continue
				}
				_, quote := util.BaseQuote(v.Symbol)
				rate := repo.priceRepo.SpotURate(ctx, quote)
				v.ProfitUnreal = v.CalcProfitUnreal(repo.priceRepo.GetMarkPrice(ctx, v.Symbol))
				unreal = unreal.Add(v.ProfitUnreal.Mul(rate))
			}
		}
	}

	return unreal
}

// HoldCostTotalIsolated 全部逐仓的持仓成本 只有单币保证金有逐仓 所以必须带交易区
func (repo *assetRepository) HoldCostTotalIsolated(ctx context.Context, asset *repository.AssetSwap, area string, isRemoveTrialPos bool) decimal.Decimal {
	holdCost := decimal.Zero
	for _, symbolPosList := range asset.IsolatedList {
		for _, v := range symbolPosList {
			if v.Pos.IsZero() {
				continue
			}
			if isRemoveTrialPos && v.IsTrial() {
				continue
			}
			if len(area) > 0 {
				if !strings.HasSuffix(v.Symbol, area) {
					continue
				}
			}
			holdCost = holdCost.Add(v.HoldCost())
		}
	}

	return holdCost
}

// HoldCostTotalCross 全部全仓的持仓成本 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *assetRepository) HoldCostTotalCross(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	holdCost := decimal.Zero
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		for _, symbolPosList := range asset.CrossList {
			for _, v := range symbolPosList {
				if v.Pos.IsZero() {
					continue
				}
				if !strings.HasSuffix(v.Symbol, area) {
					continue
				}
				holdCost = holdCost.Add(v.HoldCost())
			}
		}
	} else if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		for _, symbolPosList := range asset.CrossList {
			for _, v := range symbolPosList {
				if v.Pos.IsZero() {
					continue
				}
				_, quote := util.BaseQuote(v.Symbol)
				rate := repo.priceRepo.SpotURate(ctx, quote)
				holdCost = holdCost.Add(v.HoldCost().Mul(rate))
			}
		}
	}

	return holdCost
}

// HoldCostTotal 所有币对的持仓成本 单币保证金不折u按照交易区获取 联合保证金折u
// 跳过体验金
func (repo *assetRepository) HoldCostTotal(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	area = strings.ToUpper(area)
	holdCost := decimal.Zero
	switch asset.AssetMode {
	case futuresassetpb.AssetMode_ASSET_MODE_MULTI:
		for _, symbolPosList := range asset.CrossList {
			for _, v := range symbolPosList {
				if v.Pos.IsZero() {
					continue
				}
				if v.IsTrial() {
					continue
				}
				_, quote := util.BaseQuote(v.Symbol)
				rate := repo.priceRepo.SpotURate(ctx, quote)
				holdCost = holdCost.Add(v.HoldCost().Mul(rate))
			}
		}

	default:
		for _, symbolPosList := range asset.IsolatedList {
			for _, v := range symbolPosList {
				if v.Pos.IsZero() {
					continue
				}
				if v.IsTrial() {
					continue
				}
				if !strings.HasSuffix(v.Symbol, area) {
					continue
				}
				holdCost = holdCost.Add(v.HoldCost())
			}
		}
		for _, symbolPosList := range asset.IsolatedList {
			for _, v := range symbolPosList {
				if v.Pos.IsZero() {
					continue
				}
				if v.IsTrial() {
					continue
				}
				if !strings.HasSuffix(v.Symbol, area) {
					continue
				}
				holdCost = holdCost.Add(v.HoldCost())
			}
		}

	}
	return holdCost
}

// HoldCostTrialTotal 所有币对的体验金成本 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *assetRepository) HoldCostTrialTotal(ctx context.Context) decimal.Decimal {
	return decimal.Zero
}

// CrossTrialUnrealTotal 全部全仓的未实现盈亏 单币保证金不折u按照交易区获取 联合保证金折u
func (repo *assetRepository) CrossTrialUnrealTotal(ctx context.Context) decimal.Decimal {
	return decimal.Zero
}
