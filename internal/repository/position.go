package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"sync"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/dig"
	"gorm.io/gorm"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type PositionRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	MDB *mongo.Database      `name:"mongodb"`
	RDB *redis.ClusterClient `name:"redis-cluster"`

	FormulaRepository repository.FormulaRepository
	PriceRepository   repository.PriceRepository
	AssetRepository   repository.AssetRepository
	BurstRepository   repository.BurstRepository
	CacheRepository   repository.CacheRepository
	SettingRepository repository.SettingRepository
}

type positionRepository struct {
	db  *gorm.DB
	mdb *mongo.Database
	rdb *redis.ClusterClient

	formulaRepo repository.FormulaRepository
	priceRepo   repository.PriceRepository
	assetRepo   repository.AssetRepository
	burstRepo   repository.BurstRepository
	cacheRepo   repository.CacheRepository
	settingRepo repository.SettingRepository
}

func NewPositionRepository(param PositionRepositoryParam) repository.PositionRepository {
	return &positionRepository{
		db:  param.DB,
		mdb: param.MDB,
		rdb: param.RDB,

		formulaRepo: param.FormulaRepository,
		priceRepo:   param.PriceRepository,
		assetRepo:   param.AssetRepository,
		burstRepo:   param.BurstRepository,
		cacheRepo:   param.CacheRepository,
		settingRepo: param.SettingRepository,
	}
}

func (repo *positionRepository) UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error) {
	reply := make([]repository.PosSwap, 0)
	asset, err := repo.cacheRepo.Load(ctx, req.UID, req.Symbol, req.Currency)
	if err != nil {
		return reply, err
	}

	cross, isolated, err := asset.UserPos(true, "", req.Symbol, repo.priceRepo.GetMarkPrice(ctx, req.Symbol))
	reply = append(reply, cross...)
	reply = append(reply, isolated...)
	sort.Sort(repository.PosSwapSlice(reply))

	return reply, nil
}

func (repo *positionRepository) QueryUserPos(ctx context.Context, param *repository.UserPosParam) (repository.UserPosReply, error) {
	reply := repository.UserPosReply{
		List: make([]repository.PosQuery, 0),
	}

	searchParam := &entity.PositionSearch{
		Base:      param.Base,
		Quote:     param.Quote,
		StartTime: param.StartTime,
		EndTime:   param.EndTime,
		PageSize:  param.PageSize,
		PageNum:   param.PageIndex,
		Position: entity.Position{
			Id:          param.PosId,
			AccountType: param.AccountType,
			UID:         param.UID,
			PosSide:     param.PosSide,
			PosStatus:   param.PosStatus,
			MarginMode:  param.MarginMode,
		},
	}
	total, list, err := repo.GetPosSwapList(searchParam, param.UserType, param.LiquidationType, param.IsHasPos)
	reply.ShortPosTotal, reply.LongPosTotal = repo.GetPosTotal(ctx, util.ContractCode(param.Base, param.Quote))
	for _, pos := range list {
		_, quote := util.BaseQuote(pos.Symbol)
		pair, err := repo.settingRepo.GetCachePair(ctx, pos.Symbol)
		if err != nil || pair == nil {
			pair = &repository.ContractPair{Precision: repository.SymbolPrecision{
				PricePrecision: 2,
			}}
		}
		posSwap := repository.PosQuery{
			PosSwap: repository.PosSwap{
				Currency:        pos.Currency,
				Leverage:        pos.Leverage,
				IsolatedMargin:  pos.IsolatedMargin,
				Liquidation:     pos.Liquidation,
				LiquidationType: domain.LiquidationType(pos.LiquidationType),
				MarginMode:      pos.MarginMode,
				OpenPriceAvg:    pos.OpenPriceAvg.Truncate(pair.Precision.PricePrecision),
				OpenTime:        pos.OpenTime / 1e9,
				Pos:             pos.Pos,
				PosAvailable:    pos.PosAvailable,
				PosSide:         pos.PosSide,
				Symbol:          pos.Symbol,
				UID:             pos.UID,
				UserType:        int32(pos.UserType),
				PosId:           pos.Id,
				PosStatus:       domain.PosStatus(pos.PosStatus),
				ProfitReal:      pos.ProfitReal,
				Subsidy:         pos.Subsidy,
			},
			RivalScore: pos.RivalScore,
			Margin:     pos.IsolatedMargin,
		}
		markPrice := repo.priceRepo.GetMarkPrice(ctx, pos.Symbol)
		posSwap.ProfitUnreal = posSwap.CalcProfitUnreal(markPrice)
		if posSwap.Leverage > 0 {
			// 计算全仓仓位保证金
			levearage := decimal.NewFromInt(int64(posSwap.Leverage))
			crossMargin := markPrice.Mul(posSwap.Pos).Div(levearage)
			if !posSwap.Isolated() {
				posSwap.Margin = crossMargin.Truncate(domain.CurrencyPrecision) // 全仓仓位保证金
			}
			// 计算回报率
			if crossMargin.Sign() != 0 {
				posSwap.ReturnRate = posSwap.ProfitUnreal.Div(crossMargin).Mul(decimal.NewFromInt(100)).Truncate(domain.CnyPrecision)
			}
		}

		userAsset, err := repo.cacheRepo.Load(ctx, pos.UID, pos.Symbol, pos.Currency)
		if err != nil {
			logrus.Info(fmt.Sprintf("PosInfo get asset err.userAsset:%+v", userAsset))
		}
		posSwap.MarkPrice = markPrice
		if userAsset != nil {
			totalBalance := userAsset.CBalance(param.Quote)
			rate := decimal.NewFromInt(1)
			if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
				totalBalance, err = repo.assetRepo.TotalJoinBalance(ctx, userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("PosInfo TotalJoinBalance err.userAsset:%+v err:%s", userAsset, err))
					return reply, err
				}
				rate = repo.priceRepo.SpotURate(ctx, param.Quote)
			}
			isRemoveTrialPos := true
			if posSwap.IsTrial() {
				isRemoveTrialPos = false
			}
			posSwap.Liquidation = repo.formulaRepo.CalcLiquidationPrice(ctx, &posSwap, &userAsset.LongPos, &userAsset.ShortPos, &userAsset.BothPos,
				repo.assetRepo.HoldCostTotalIsolated(ctx, userAsset, quote, isRemoveTrialPos).Mul(rate),
				totalBalance, repo.assetRepo.CrossUnrealTotal(ctx, userAsset, quote).Mul(rate),
				repo.assetRepo.CrossTrialUnrealTotal(ctx).Mul(rate), markPrice)
		}
		posSwap.MarginBalance, posSwap.MaintainMargin, err = repo.assetRepo.GetMarginBalanceAndMaintainMargin(ctx, userAsset, posSwap.PosSwap)
		if err != nil {
			log.Println("QueryUserPos GetMarginBalanceAndMaintainMargin error:", err)
			return reply, err
		}
		posSwap.RivalScore = repo.burstRepo.RivalScore(ctx, posSwap.UID, posSwap.Symbol, posSwap.PosSide, posSwap.IsTrial()).Truncate(domain.CurrencyPrecision)
		if posSwap.MarginBalance.Sign() != 0 {
			posSwap.RiskRate = posSwap.MaintainMargin.Div(posSwap.MarginBalance).Truncate(domain.RatePrecision).Mul(decimal.NewFromInt(100))
		}

		reply.List = append(reply.List, posSwap)
	}
	reply.PageIndex = param.PageIndex
	reply.PageSize = param.PageSize
	reply.Total = total

	return reply, err
}

func (repo *positionRepository) PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error) {
	pos, err := repo.GetPosSwap(ctx, param.PosId)
	if err != nil {
		return repository.PosQuery{}, err
	}
	pair, err := repo.settingRepo.GetCachePair(ctx, pos.Symbol)
	if err != nil || pair == nil {
		log.Printf("get contract pair err: %+v", err)
		pair = &repository.ContractPair{Precision: repository.SymbolPrecision{
			PricePrecision: 2,
		}}
	}

	markPrice := repo.priceRepo.GetMarkPrice(ctx, pos.Symbol)
	posSwap := repository.PosQuery{
		PosSwap: repository.PosSwap{
			Currency:       pos.Currency,
			Leverage:       pos.Leverage,
			Liquidation:    pos.Liquidation,
			IsolatedMargin: pos.IsolatedMargin,
			MarginMode:     pos.MarginMode,
			OpenPriceAvg:   pos.OpenPriceAvg.Truncate(pair.Precision.PricePrecision),
			OpenTime:       pos.OpenTime / 1e9,
			Pos:            pos.Pos,
			PosAvailable:   pos.PosAvailable,
			PosSide:        pos.PosSide,
			Symbol:         pos.Symbol,
			UID:            pos.UID,
			PosId:          pos.Id,
		},
		RivalScore: pos.RivalScore,
		Margin:     pos.IsolatedMargin,
	}
	posSwap.ProfitUnreal = posSwap.CalcProfitUnreal(markPrice)
	posSwap.RivalScoreRate = repo.burstRepo.RivalScoreRate(ctx, posSwap.UID, posSwap.Symbol, posSwap.PosSide, posSwap.IsTrial())

	userAsset, err := repo.cacheRepo.Load(ctx, pos.UID, pos.Symbol, pos.Currency)
	if err != nil {
		logrus.Info(fmt.Sprintf("PosInfo get asset err.userAsset:%+v", userAsset))
	}

	// 计算风险率
	posSwap.MarginBalance, posSwap.MaintainMargin, err = repo.assetRepo.GetMarginBalanceAndMaintainMargin(ctx, userAsset, posSwap.PosSwap)
	if err != nil {
		log.Println("PosInfo GetMarginBalanceAndMaintainMargin error:", err)
		return posSwap, err
	}
	if posSwap.MarginBalance.Sign() != 0 {
		posSwap.RiskRate = posSwap.MaintainMargin.Div(posSwap.MarginBalance).Truncate(domain.RatePrecision).Mul(decimal.NewFromInt(100))
	}
	if userAsset != nil {
		totalBalance := userAsset.CBalance(param.Quote)
		rate := decimal.NewFromInt(1)
		if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
			totalBalance, err = repo.assetRepo.TotalJoinBalance(ctx, userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("PosInfo TotalJoinBalance err.userAsset:%+v err:%v", userAsset, err))
				return posSwap, err
			}
			rate = repo.priceRepo.SpotURate(ctx, param.Quote)
		}
		isRemoveTrialPos := true
		if posSwap.IsTrial() {
			isRemoveTrialPos = false
		}
		posSwap.Liquidation = repo.formulaRepo.CalcLiquidationPrice(ctx, &posSwap, &userAsset.LongPos, &userAsset.ShortPos, &userAsset.BothPos,
			repo.assetRepo.HoldCostTotalIsolated(ctx, userAsset, param.Quote, isRemoveTrialPos).Mul(rate),
			totalBalance, repo.assetRepo.OtherCrossUnreal(ctx, userAsset, posSwap.Symbol).Mul(rate),
			repo.assetRepo.OtherCrossMaintainMargin(ctx, userAsset, posSwap.Symbol).Mul(rate), markPrice) // 预估强评价
	}
	if !posSwap.Isolated() && posSwap.Leverage > 0 {
		levearage := decimal.NewFromInt(int64(posSwap.Leverage))
		posSwap.Margin = markPrice.Mul(posSwap.Pos).Div(levearage).Truncate(domain.CurrencyPrecision)
	}

	posSwap.MarkPrice = markPrice

	return posSwap, err
}

// PosTotal 获取结算总持仓数 多仓仓位总是与空仓仓位总数是一样的，所以只需要获取多仓
func (repo *positionRepository) PosTotal(ctx context.Context, contractCode string) decimal.Decimal {
	total := decimal.Zero
	usersKey := domain.GetAllUserPosKey(contractCode)
	users, err := repo.rdb.HGetAll(ctx, usersKey).Result()
	if err != nil {
		logrus.Error("usersKey HGetAll", usersKey, "error:", err)
		return total
	}
	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() {
			continue
		}
		total = total.Add(userPosSwap.LongPos)
	}

	return total
}

func (repo *positionRepository) UserHoldPos(ctx context.Context, _req *repository.UserHoldPosReq) (repository.HoldPosReply, error) {
	reply := repository.HoldPosReply{}
	asset, err := repo.cacheRepo.Load(ctx, _req.UID, util.ContractCode(_req.Base, _req.Quote), _req.Quote)
	if err != nil {
		logrus.Error(fmt.Sprintf("useAsset Load err:%+v, req:%+v", err, _req))
		return reply, err
	}
	reply.PositionMode = asset.PositionMode

	if len(_req.Base) > 0 && len(_req.Quote) > 0 && _req.PosSide > 0 {
		switch _req.PosSide {
		case domain.LongPos:
			if asset.PositionMode == domain.HoldModeBoth && asset.BothPos.Pos.IsPositive() {
				reply.PosAmount = asset.BothPos.Pos
			} else {
				reply.PosAmount = asset.LongPos.Pos
			}

		case domain.ShortPos:
			if asset.PositionMode == domain.HoldModeBoth && asset.BothPos.Pos.IsNegative() {
				reply.PosAmount = asset.BothPos.Pos.Abs()
			} else {
				reply.PosAmount = asset.ShortPos.Pos
			}

		default:
			return reply, errors.New(fmt.Sprintln("pos side error:", _req.PosSide))
		}
	}

	return reply, nil
}

// PlatPosList 合约持仓列表
func (repo *positionRepository) PlatPosList(ctx context.Context) (repository.PlatPosList, error) {
	// 获取合约币对配置
	pairs, err := repo.settingRepo.GetAllPairSettingInfo(ctx)
	if err != nil {
		logrus.Error("GetAllPairInfo err.")
		return repository.PlatPosList{}, err
	}
	// 获取合约持仓数据
	platData, err := repo.rdb.HGetAll(ctx, domain.PlatHoldPos).Result()
	if err != nil {
		return repository.PlatPosList{}, errors.Wrap(err, "redis hgetall")
	}

	data := repository.PlatPosList{}
	for code, platPosStr := range platData {
		// 根据币对的状态判断是否返回数据
		pair, ok := pairs[code]
		if !ok || pair.State == domain.ContractDisable {
			continue
		}
		// 进行数据的返回操作
		platPosDetail := repository.PlatPosDetail{}
		err = json.Unmarshal([]byte(platPosStr), &platPosDetail)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal platPosStr err:%+v, platPosStr:%s", err, platPosStr))
			continue
		}
		data.PosList = append(data.PosList, platPosDetail)
		if platPosDetail.UpdateTime > data.UpdateTime {
			data.UpdateTime = platPosDetail.UpdateTime
		}
	}
	sort.Slice(data.PosList, func(i, j int) bool {
		return data.PosList[i].Base < data.PosList[j].Base
	})
	if len(data.PosList) <= 0 {
		return repository.PlatPosList{}, errors.New("no data")
	}

	return data, nil
}

func (repo *positionRepository) PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error) {
	contract := util.ContractCode(req.Base, req.Quote)
	platPosStr, err := repo.rdb.HGet(ctx, domain.PlatHoldPos, contract).Result()
	if err != nil {
		logrus.Error(fmt.Sprintf("get Plat Hold Pos err:%+v.req:%+v", err, req))
		return repository.PlatPosDetail{}, err
	}
	data := repository.PlatPosDetail{}
	err = json.Unmarshal([]byte(platPosStr), &data)
	if err != nil {
		logrus.Error(fmt.Sprintf("unmarshal platPosStr err:%+v, platPosStr:%s,req:%+v", err, platPosStr, req))
		return repository.PlatPosDetail{}, err
	}

	return data, nil
}

// StaticPlatPos 统计平台持仓数据
func (repo *positionRepository) StaticPlatPos(_contractCode string, _wg *sync.WaitGroup) {
	defer _wg.Done()

	usersKey := domain.GetAllUserPosKey(_contractCode)
	users, err := repo.rdb.HGetAll(context.Background(), usersKey).Result()
	if err != nil {
		logrus.Error("burstService HGetAll", usersKey, "error:", err)
		return
	}
	base, quote := util.BaseQuote(_contractCode)
	platPos := repository.PlatPosDetail{
		Base:  base,
		Quote: quote,
	}
	var totalUserCount int64 // 不包含机器人的总持仓用户数
	holdPos := map[string]interface{}{}
	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() {
			continue
		}

		platPos.TotalPos = platPos.TotalPos.Add(userPosSwap.LongPos).Add(userPosSwap.ShortPos)
		platPos.LongPos = platPos.LongPos.Add(userPosSwap.LongPos)
		platPos.ShortPos = platPos.ShortPos.Add(userPosSwap.ShortPos)

		if userPosSwap.UserType == int32(futuresassetpb.UserType_USER_TYPE_PLATFORM_ROBOT) {
			// 是机器人用户类型处理逻辑
			if userPosSwap.LongPos.IsPositive() {
				holdPos[userPosSwap.UID] = nil
				platPos.LongUserCount = platPos.LongUserCount + 1
				platPos.RobotLongPos = platPos.RobotLongPos.Add(userPosSwap.LongPos)
			}
			if userPosSwap.ShortPos.IsPositive() {
				holdPos[userPosSwap.UID] = nil
				platPos.ShortUserCount = platPos.ShortUserCount + 1
				platPos.RobotShortPos = platPos.RobotShortPos.Add(userPosSwap.ShortPos)
			}
		} else { // 不是机器人
			if userPosSwap.LongPos.IsPositive() {
				holdPos[userPosSwap.UID] = nil
				totalUserCount += 1
				platPos.LongUserCount = platPos.LongUserCount + 1
				platPos.UserLongPos = platPos.UserLongPos.Add(userPosSwap.LongPos)
			}
			if userPosSwap.ShortPos.IsPositive() {
				holdPos[userPosSwap.UID] = nil
				totalUserCount += 1
				platPos.ShortUserCount = platPos.ShortUserCount + 1
				platPos.UserShortPos = platPos.UserShortPos.Add(userPosSwap.ShortPos)
			}
		}
	}
	platPos.TotalUserCount = int64(len(holdPos))
	if platPos.TotalUserCount != 0 {
		platPos.LongUserPercent = decimal.NewFromInt(platPos.LongUserCount).Div(decimal.NewFromInt(platPos.TotalUserCount)).
			Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
		platPos.ShortUserPercent = decimal.NewFromInt(platPos.ShortUserCount).Div(decimal.NewFromInt(platPos.TotalUserCount)).
			Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
		if totalUserCount != 0 {
			platPos.UserAveragePos = platPos.UserLongPos.Add(platPos.UserShortPos).Div(decimal.NewFromInt(totalUserCount)).Truncate(domain.CurrencyPrecision)
		}
	}

	if !platPos.LongPos.IsZero() {
		platPos.UserLongPosPercent = platPos.UserLongPos.Div(platPos.LongPos).Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
		platPos.RobotLongPosPercent = platPos.RobotLongPos.Div(platPos.LongPos).Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
	}

	if !platPos.ShortPos.IsZero() {
		platPos.UserShortPosPercent = platPos.UserShortPos.Div(platPos.ShortPos).Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
		platPos.RobotShortPosPercent = platPos.RobotShortPos.Div(platPos.ShortPos).Truncate(domain.RatePrecision).Mul(decimal.NewFromFloat(100))
	}

	platPos.TotalUserPos = platPos.UserLongPos.Add(platPos.UserShortPos)
	platPos.TotalRobotPos = platPos.RobotLongPos.Add(platPos.RobotShortPos)
	platPos.UpdateTime = time.Now().Unix()

	platStr, _ := json.Marshal(platPos)
	err = repo.rdb.HSet(context.Background(), domain.PlatHoldPos, _contractCode, string(platStr)).Err()
	if err != nil {
		logrus.Error(fmt.Sprintf("HSet err:%+v, platPos:%+v,platStr:%s", err, platPos, string(platStr)))
	}
}

// GetIsolatedHoldingMargin implements repository.PositionRepository.
func (repo *positionRepository) GetIsolatedHoldingMargin(ctx context.Context, pos repository.PosSwap, markPrice decimal.Decimal) decimal.Decimal {
	marginLevel, _, _ := repo.settingRepo.FetchMarginLevel(ctx, pos.Symbol, markPrice)
	posValue := pos.CalcPosValue(markPrice)
	holdingMargin := posValue.Mul(marginLevel.HoldingMarginRate)

	return holdingMargin
}

// DeleteRecords implements repository.PositionRepository.
func (repo *positionRepository) DeleteRecords(ctx context.Context, createTime int64) error {
	sql := fmt.Sprintf("DELETE FROM `%s` WHERE `create_time`<? AND `user_type`=? AND `pos_status`=?;", new(entity.Position).TableName())
	if err := repo.db.Exec(sql, createTime, domain.UserTypePlatformRobot, domain.PosStatusEnd).Error; err != nil {
		return err
	}

	return nil
}

// GetPosPartInfo implements repository.PositionRepository.
func (repo *positionRepository) GetPosPartInfo(ctx context.Context, awardOpId string) ([]*entity.PosPartInfo, error) {
	posPartInfos := make([]*entity.PosPartInfo, 0)
	sqlStr := "SELECT id FROM pos_swap WHERE award_ids LIKE " + "'%" + awardOpId + "%'"
	err := repo.db.Raw(sqlStr).Scan(&posPartInfos).Error

	return posPartInfos, err
}

// GetPosSwap implements repository.PositionRepository.
func (repo *positionRepository) GetPosSwap(ctx context.Context, posId string) (pos entity.Position, err error) {
	pos = entity.Position{}

	if err := repo.db.Where("id = ?", posId).First(&pos).Error; err != nil {
		return pos, err
	}

	return pos, nil
}

// GetPosSwapList implements repository.PositionRepository.
func (repo *positionRepository) GetPosSwapList(param *entity.PositionSearch, userType []int, liquidationType []int, isHasPos bool) (int64, []entity.Position, error) {
	// 获取数据和总数
	posList := make([]entity.Position, 0)
	var total int64

	query := fmt.Sprintf("is_deleted=0")
	args := make([]interface{}, 0)
	if param.Id != "" {
		query += fmt.Sprintf(" and id like ?")
		args = append(args, param.Id)
	}
	if param.UID != "" {
		query += fmt.Sprintf(" and user_id=?")
		args = append(args, param.UID)
	}
	if param.Base != "" && param.Quote != "" {
		query += fmt.Sprintf(" and symbol=?")
		args = append(args, util.ContractCode(param.Base, param.Quote))
	}
	if param.Base == "" && param.Quote != "" {
		query += fmt.Sprintf(" and symbol like ?")
		args = append(args, "%"+param.Quote)
	}
	if param.Base != "" && param.Quote == "" {
		query += fmt.Sprintf(" and symbol like ?")
		args = append(args, param.Base+"%")
	}
	if isHasPos {
		query += fmt.Sprintf(" and pos>0")
	}
	if param.PosSide != 0 {
		query += fmt.Sprintf(" and pos_side=?")
		args = append(args, param.PosSide)
	}
	if len(param.AccountType) > 0 {
		query += fmt.Sprintf(" and account_type=?")
		args = append(args, param.AccountType)
	}
	if len(userType) > 0 {
		query += fmt.Sprintf(" and user_type in (?)")
		args = append(args, userType)
	}
	if param.StartTime > 0 {
		query += fmt.Sprintf(" and open_time>=?")
		args = append(args, param.StartTime*1e9)
	}
	if param.EndTime > 0 {
		query += fmt.Sprintf(" and open_time<=?")
		args = append(args, param.EndTime*1e9)
	}
	if param.PosStatus > 0 {
		query += fmt.Sprintf(" and pos_status=?")
		args = append(args, param.PosStatus)
	}
	if param.MarginMode > 0 {
		query += fmt.Sprintf(" and margin_mode=?")
		args = append(args, param.MarginMode)
	}
	if len(liquidationType) > 0 {
		query += fmt.Sprintf(" and liquidation_type in (?)")
		args = append(args, liquidationType)
	}

	sql := repo.db.Table(new(entity.Position).TableName()).Select("*").Order("pos_status, create_time desc")
	if param.PageNum != 0 && param.PageSize != 0 {
		sql = sql.Offset((param.PageNum - 1) * param.PageSize).Limit(param.PageSize)
	}
	if query != "" && args != nil {
		sql = sql.Where(query, args...)
	}
	if err := sql.Find(&posList).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return 0, posList, err
		}
	}
	if err := sql.Offset(-1).Count(&total).Error; err != nil {
		return 0, posList, err
	}

	return total, posList, nil
}

// GetPosTotal implements repository.PositionRepository.
func (repo *positionRepository) GetPosTotal(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal) {
	shortTotal := decimal.Zero
	longTotal := decimal.Zero
	if symbol == "" {
		return shortTotal, longTotal
	}

	var err error
	pos := entity.Position{}
	rows, err := repo.db.Table(pos.TableName()).Select("pos_side, sum(pos) as total").
		Where("symbol=?", symbol).
		Group("pos_side").Rows()
	if err != nil {
		log.Printf("GetPosTotal Group Rows err:%s", err)
		return shortTotal, longTotal
	}
	defer rows.Close()
	for rows.Next() {
		posSide := 0
		total := decimal.Zero
		if err := rows.Scan(&posSide, &total); err != nil {
			log.Printf("GetPosTotal rows.Next() scan err:%s", err)
			continue
		}
		if int32(posSide) == domain.LongPos {
			longTotal = total
		} else {
			shortTotal = total
		}
	}

	return shortTotal, longTotal
}

// UpdatePosLeverage implements repository.PositionRepository.
func (repo *positionRepository) UpdatePosLeverage(ctx context.Context, uid string, leverage int) error {
	sql := fmt.Sprintf("UPDATE %s SET `leverage`=? WHERE `user_id`=? AND `symbol`=?", new(entity.Position).TableName())
	if err := repo.db.Exec(sql, leverage, uid).Error; err != nil {
		return err
	}

	return nil
}

// UpdateSubsidy implements repository.PositionRepository.
func (repo *positionRepository) UpdateSubsidy(ctx context.Context, uid string, subsidy decimal.Decimal) error {
	sql := fmt.Sprintf("UPDATE %s SET `subsidy`=? WHERE `user_id`=? AND `id`=?", new(entity.Position).TableName())
	if err := repo.db.Exec(sql, subsidy, uid).Error; err != nil {
		return err
	}

	return nil
}

// UpsertPos implements repository.PositionRepository.
func (repo *positionRepository) UpsertPos(ctx context.Context, pos *entity.Position) error {
	insertColumns := "`id`,`user_id`,`user_type`,`pos_side`,`leverage`," +
		"`account_type`,`symbol`,`currency`,`pos`,`pos_available`," +
		"`margin_mode`,`isolated_margin`,`open_price_avg`,`open_time`,`pos_status`," +
		"`liquidation_type`,`profit_real`,`subsidy`,`create_time`," +
		"`trial_margin`,`award_ids`"

	updateColumns := "`leverage`=?,`pos`=?,`reborn_id`=?,`pos_available`=?,`isolated_margin`=?,`open_price_avg`=?," +
		"`margin_mode`=?,`open_time`=?,`pos_status`=?,`liquidation_type`=?,`profit_real`=`profit_real`+CAST(? AS DECIMAL(30,15))," +
		"`subsidy`=`subsidy`+CAST(? AS DECIMAL(30,15)),`update_time`=?,`trial_margin`=?,`award_ids`=?"
	sql := fmt.Sprintf(`INSERT INTO %s (%s) 
		VALUES (
		    ?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,?,
			?,?,?,?,
		    ?,?) ON DUPLICATE KEY UPDATE %s;`,
		pos.TableName(), insertColumns, updateColumns)
	if err := repo.db.Exec(sql,
		pos.Id, pos.UID, pos.UserType, pos.PosSide, pos.Leverage,
		pos.AccountType, pos.Symbol, pos.Currency, pos.Pos, pos.PosAvailable,
		pos.MarginMode, pos.IsolatedMargin, pos.OpenPriceAvg, pos.OpenTime, pos.PosStatus,
		pos.LiquidationType, pos.ProfitReal, pos.Subsidy, pos.CreateTime,
		pos.TrialMargin, pos.AwardIds,

		pos.Leverage, pos.Pos, pos.RebornId, pos.PosAvailable, pos.IsolatedMargin,
		pos.OpenPriceAvg, pos.MarginMode, pos.OpenTime, pos.PosStatus, pos.LiquidationType,
		pos.ProfitReal, pos.Subsidy, pos.UpdateTime, pos.TrialMargin, pos.AwardIds,
	).Error; err != nil {
		return err
	}

	return nil
}
