package typex

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// 时间类型
const (
	Second      = iota // 0 秒
	Millisecond        // 1 毫秒
	Microsecond        // 2 微秒
	Nanosecond         // 3 纳秒
)

/*
 * 函数介绍：纳秒时间戳转换当前时间日期
 * 创建人：仲德
 * 创建时间： 2019/08/22 14:55
 * 传入参数：_timer: time.Now().UnixNano()
 * 返回值：string:2019-08-01
 */
func NanoParseDate(_timer int64) string {
	if _timer == 0 {
		return ""
	}
	fmtString := time.Unix(0, _timer).Format("2006-01-02 15:04:05")
	return strings.Split(fmtString, " ")[0]
}

/*
 * 函数介绍：秒时间转换当前时间月
 * 创建人：仲德
 * 创建时间： 2019/08/26 11:41
 * 传入参数：_timer:time.Now().Unix()
 * 返回值：string:2019-08-01
 */
func ParseMonth(_timer int64) string {
	// data := GetBlockStartTime(_timer, TMonth)
	fmtString := time.Unix(0, _timer).Format("2006-01-02 15:04:05")
	date := strings.Split(strings.Split(fmtString, " ")[0], "-")
	return date[0] + "-" + date[1]
}

/**
 * @description: 秒或纳秒时间转换当前时间月整数类型
 * @author: 奉先
 * @date 5/21/2020 11:52 AM
 * @param flag []
 */
func ParseMonthInt(_timer int64, timeType int) int {
	var timeObj time.Time

	switch timeType {
	case Second:
		timeObj = time.Unix(_timer, 0)
	case Millisecond:
		timeObj = time.Unix(0, _timer*1e6)
	case Microsecond:
		timeObj = time.Unix(0, _timer*1e3)
	case Nanosecond:
		timeObj = time.Unix(0, _timer)
	default:
		return 0
	}

	yearMonth := fmt.Sprintf("%d%d", timeObj.Year(), timeObj.Month())
	i, _ := strconv.Atoi(yearMonth)
	return i
}

/*
 * 函数介绍：秒时间转换当前时间月
 * 创建人：仲德
 * 创建时间： 2019/08/26 11:41
 * 传入参数：_timer:time.Now().Unix()
 * 返回值：string:2019-08-01
 */
func ParseYear(_timer int64) string {
	// data := GetBlockStartTime(_timer/1e9, TMonth)
	fmtString := time.Unix(0, _timer).Format("2006-01-02 15:04:05")
	return strings.Split(fmtString, "-")[0]
}

const (
	TSec  = iota // value --> 0
	TMin         // value --> 1
	TMin5        // value --> 2
	TMin15
	TMin30
	THour
	THour4
	TDay
	TWeek
	TMonth // value --> 3
)

// 获得一个中国时区的标准0点0分
const mybr = 262195200 // 1978/04/24 0:0:0

// 设置全局时区是中国时区
const timezone = "Asia/Shanghai"

func GetBlockStartTime(_t int64, _btype int) int64 {
	switch _btype {
	case TSec:
		return _t
	case TMin:
		yu := (_t - mybr) % 60
		return _t - yu
	case TMin5:
		yu := (_t - mybr) % (60 * 5)
		return _t - yu
	case TMin15:
		yu := (_t - mybr) % (60 * 15)
		return _t - yu
	case TMin30:
		yu := (_t - mybr) % (60 * 30)
		return _t - yu
	case THour:
		yu := (_t - mybr) % (60 * 60)
		return _t - yu
	case THour4:
		yu := (_t - mybr) % (60 * 60 * 4)
		return _t - yu
	case TDay:
		loc, _ := time.LoadLocation(timezone)
		l := time.Unix(_t, 0).In(loc)
		k := time.Date(l.Year(), l.Month(), l.Day(), 0, 0, 0, 0, loc).Unix()
		return k
	case TWeek:
		loc, _ := time.LoadLocation(timezone)
		l := time.Unix(_t, 0).In(loc)
		d := l.Weekday() - time.Sunday
		k := time.Date(l.Year(), l.Month(), l.Day(), 0, 0, 0, 0, loc).Unix()
		return k - (int64(d) * 24 * 60 * 60)
	case TMonth:
		loc, _ := time.LoadLocation(timezone)
		l := time.Unix(_t, 0).In(loc)
		k := time.Date(l.Year(), l.Month(), 1, 0, 0, 0, 0, loc).Unix()
		return k
	}
	return 0
}

// ParseDayDate 纳秒转换天日期
func ParseDayDate(_timer int64) (dayDate int) {
	if _timer == 0 {
		return
	}
	dayDate, _ = strconv.Atoi(time.Unix(0, _timer).Format("20060102"))
	return
}

// ParseToUnix 纳秒转换秒
func ParseToUnix(_timer int64) (unix int64) {
	if _timer == 0 {
		return
	}
	unix = time.Unix(0, _timer).Unix()
	return
}

// UnixParseDayDate 秒转换天日期
func UnixParseDayDate(_timer int64) (dayDate int) {
	if _timer == 0 {
		return
	}
	dayDate, _ = strconv.Atoi(time.Unix(_timer, 0).Format("20060102"))
	return
}

// DayDateToUnix 天日期转换时间戳
func DayDateToUnix(dayDate int) (unix int) {
	if dayDate == 0 {
		return
	}
	timer, _ := time.ParseInLocation("20060102", strconv.Itoa(dayDate), time.Local)
	return int(timer.Unix())
}

// DayDateToTimeStamp 天日期转换时间戳, int64
func DayDateToTimeStamp(dayDate int) (unix int64) {
	if dayDate == 0 {
		return
	}
	timer, _ := time.ParseInLocation("20060102", strconv.Itoa(dayDate), time.Local)
	return timer.Unix()
}

// GetEndTime 天转时间戳为23.59.59
func GetEndTime(end int) int64 {
	unix := DayDateToTimeStamp(end)

	// 将 Unix 时间戳转换为 time.Time 对象
	t := time.Unix(unix, 0)

	// 获取该日期的年、月、日
	year, month, day := t.Date()

	// 构造当天 23:59:59 的时间
	endOfDay := time.Date(year, month, day, 23, 59, 59, 0, t.Location())

	// 返回 Unix 时间戳
	return endOfDay.Unix()
}

// GetDayStartAndEndTime 获取天的起始结束时间
func GetDayStartAndEndTime(t time.Time) (start, end int64) {
	start = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).UnixNano()
	end = time.Date(t.Year(), t.Month(), t.Day()+1, 0, 0, 0, 0, t.Location()).UnixNano()
	return
}

// UnixParseMonth 秒转月日期
func UnixParseMonth(_timer int64) string {
	// data := GetBlockStartTime(_timer, TMonth)
	fmtString := time.Unix(_timer, 0).Format("2006-01-02 15:04:05")
	date := strings.Split(strings.Split(fmtString, " ")[0], "-")
	return date[0] + "-" + date[1]
}

// GetThirtyDaysStartAndEnd 获取近三十天起始和结束时间
func GetThirtyDaysStartAndEnd() (startTime, endTime int64) {
	now := time.Now()
	startTime = time.Date(now.Year(), now.Month(), now.Day()-31, 0, 0, 0, 0, now.Location()).Unix()
	endTime = time.Date(now.Year(), now.Month(), now.Day()-1, 0, 0, 0, 0, now.Location()).Unix()
	return
}

func ConvertTimeToday(_currentTime int64) int64 { // 1.代表7天；2.代表30天
	beginTime := time.Unix(_currentTime, 0)
	timeResult := time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day(), 0, 0, 0, 0, beginTime.Location())
	return timeResult.Unix()
}

// 根据当前时间计算时间段
func ConvertTime(_currentTime int64, _timeType int) int64 { // 1.代表7天；2.代表30天
	beginTime := time.Unix(_currentTime, 0)
	var subTime int
	if _timeType == 1 {
		subTime = 7
	} else {
		subTime = 30
	}
	timeResult := time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day()-subTime, 0, 0, 0, 0, beginTime.Location())
	return timeResult.Unix()
}

func OneDayBeginAndEndTimeStamp(_oneDayTime int64) (int64, int64) {
	beginTime := time.Unix(_oneDayTime, 0)
	timeBegin := time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day(), 0, 0, 0, 0, beginTime.Location())
	timeEnd := time.Date(beginTime.Year(), beginTime.Month(), beginTime.Day()+1, 0, 0, 0, 0, beginTime.Location())
	return timeBegin.Unix(), timeEnd.Unix()
}

/*
 * 函数介绍：获取当日零点时间戳
 * 创建人：仲德
 * 创建时间： 2019/07/17 15:47
 * 传入参数：
 * 返回值：
 */
func GetExpireTime() int64 {
	t, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	return t.Unix() - 3600*8
}

/*
 * 函数介绍：时间是否大于90天
 * 创建人：仲德
 * 创建时间： 2019/11/29 15:06
 * 传入参数：
 * 返回值：
 */
func CheckTime90Day(_time int64) bool {
	t, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if _time < t.Unix()-7718400 && _time != 0 {
		return false
	}
	return true
}

/*
 * 函数介绍：时间是否大于30天
 * 创建人：仲德
 * 创建时间： 2019/11/29 15:06
 * 传入参数：
 * 返回值：
 */
func CheckTime30Day(_time int64) bool {
	t, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if _time < t.Unix()-2534400 && _time != 0 {
		return false
	}
	return true
}

/*
 * 函数介绍：开始时间不能大于结束时间
 * 创建人：仲德
 * 创建时间： 2019/11/29 15:23
 * 传入参数：
 * 返回值：
 */
func CheckTimeBigOrSmall(_start, _end int64) bool {
	if _start <= _end && _start >= 0 && _end >= 0 {
		return true
	}
	return false
}

// TimeStampToTime 时间戳转天string
func TimeStampToTime(_time int64) string {
	if _time == 0 {
		return ""
	}
	return time.Unix(_time, 0).Format("20060102")
}

// 秒时间戳转天时间戳
func SecondsToDaysTimestamp(ts int64) int64 {
	t := time.Unix(ts, 0)

	d := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())

	return d.Unix()
}

func OneDayBeginAndEndTimeStampUtc(_oneDayTime time.Time) (int64, int64) {
	year, month, day := _oneDayTime.Date()
	startTimeStr := strconv.Itoa(year) + "-" + strconv.Itoa(int(month)) + "-" + strconv.Itoa(day) + " 0:0:0"
	starTime, _ := time.ParseInLocation("2006-1-2 15:4:5", startTimeStr, _oneDayTime.Location())
	endTime := _oneDayTime.AddDate(0, 0, 1)
	endTimeStr := strconv.Itoa(endTime.Year()) + "-" + strconv.Itoa(int(endTime.Month())) + "-" + strconv.Itoa(endTime.Day()) + " 0:0:0"
	endTime, _ = time.ParseInLocation("2006-1-2 15:4:5", endTimeStr, endTime.Location())

	return starTime.Unix(), endTime.Unix()
}

func GetProfitLossStartTime(_currentTime time.Time, _timeType int) int64 { // 1.代表7天；2.代表30天
	subTime := 0
	if _timeType == 1 {
		subTime = -7
	} else {
		subTime = -30
	}
	_currentTime = _currentTime.AddDate(0, 0, subTime)
	_currentTime = time.Date(_currentTime.Year(), _currentTime.Month(), _currentTime.Day(), 0, 0, 0, 0, _currentTime.Location())
	return _currentTime.Unix()
}

func GetProfitLossEndTime(_currentTime time.Time) int64 {
	_currentTime = time.Date(_currentTime.Year(), _currentTime.Month(), _currentTime.Day(), 0, 0, 0, 0, _currentTime.Location())
	return _currentTime.Unix()
}
