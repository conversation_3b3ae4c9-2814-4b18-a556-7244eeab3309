package snowflake

import (
	"fmt"
	"hash/fnv"
	"os"

	"github.com/bwmarrin/snowflake"
	ytSnowflake "yt.com/backend/common.git/snowflake"
)

func New() (*snowflake.Node, error) {
	st, err := ytSnowflake.GetNowDate()
	if err != nil {
		return nil, fmt.Errorf("ytSnowflake.GetNowDate failed :%w", err)
	}

	s := ytSnowflake.New(
		ytSnowflake.WithMachineNodeID(GetMachineNodeID()),
		ytSnowflake.WithStartTime(st),
	)

	node, err := s.NewNode()
	if err != nil {
		return nil, fmt.Errorf("ytSnowflake.NewNode failed :%w", err)
	}

	return node, nil
}

func GetMachineNodeID() int64 {
	h := fnv.New32a()
	h.Write([]byte(os.Getenv("POD_NAME")))

	// Snowflake 公式，工作機器 ID 佔用 10bit，最多容納 1024節點，
	// 故用 % 1024 取餘數做 ring
	const nodeMax = 1024

	return int64(h.Sum32()) % nodeMax
}
