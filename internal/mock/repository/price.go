// Code generated by MockGen. DO NOT EDIT.
// Source: ./price.go
//
// Generated by this command:
//
//	mockgen -source=./price.go -destination=../../mock/repository/price.go -package=repository
//

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	reflect "reflect"

	cmap "github.com/orcaman/concurrent-map/v2"
	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
)

// MockPriceRepository is a mock of PriceRepository interface.
type MockPriceRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPriceRepositoryMockRecorder
}

// MockPriceRepositoryMockRecorder is the mock recorder for MockPriceRepository.
type MockPriceRepositoryMockRecorder struct {
	mock *MockPriceRepository
}

// NewMockPriceRepository creates a new mock instance.
func NewMockPriceRepository(ctrl *gomock.Controller) *MockPriceRepository {
	mock := &MockPriceRepository{ctrl: ctrl}
	mock.recorder = &MockPriceRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPriceRepository) EXPECT() *MockPriceRepositoryMockRecorder {
	return m.recorder
}

// GetAllIndexPrice mocks base method.
func (m *MockPriceRepository) GetAllIndexPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllIndexPrice", ctx)
	ret0, _ := ret[0].(cmap.ConcurrentMap[string, decimal.Decimal])
	return ret0
}

// GetAllIndexPrice indicates an expected call of GetAllIndexPrice.
func (mr *MockPriceRepositoryMockRecorder) GetAllIndexPrice(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllIndexPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetAllIndexPrice), ctx)
}

// GetAllLastPrice mocks base method.
func (m *MockPriceRepository) GetAllLastPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLastPrice", ctx)
	ret0, _ := ret[0].(cmap.ConcurrentMap[string, decimal.Decimal])
	return ret0
}

// GetAllLastPrice indicates an expected call of GetAllLastPrice.
func (mr *MockPriceRepositoryMockRecorder) GetAllLastPrice(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLastPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetAllLastPrice), ctx)
}

// GetAllMarkPrice mocks base method.
func (m *MockPriceRepository) GetAllMarkPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllMarkPrice", ctx)
	ret0, _ := ret[0].(cmap.ConcurrentMap[string, decimal.Decimal])
	return ret0
}

// GetAllMarkPrice indicates an expected call of GetAllMarkPrice.
func (mr *MockPriceRepositoryMockRecorder) GetAllMarkPrice(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllMarkPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetAllMarkPrice), ctx)
}

// GetIndexAndMarkPrice mocks base method.
func (m *MockPriceRepository) GetIndexAndMarkPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexAndMarkPrice", ctx, symbol)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(decimal.Decimal)
	return ret0, ret1
}

// GetIndexAndMarkPrice indicates an expected call of GetIndexAndMarkPrice.
func (mr *MockPriceRepositoryMockRecorder) GetIndexAndMarkPrice(ctx, symbol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexAndMarkPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetIndexAndMarkPrice), ctx, symbol)
}

// GetIndexPrice mocks base method.
func (m *MockPriceRepository) GetIndexPrice(ctx context.Context, symbol string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexPrice", ctx, symbol)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// GetIndexPrice indicates an expected call of GetIndexPrice.
func (mr *MockPriceRepositoryMockRecorder) GetIndexPrice(ctx, symbol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetIndexPrice), ctx, symbol)
}

// GetLastPrice mocks base method.
func (m *MockPriceRepository) GetLastPrice(ctx context.Context, symbol string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastPrice", ctx, symbol)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// GetLastPrice indicates an expected call of GetLastPrice.
func (mr *MockPriceRepositoryMockRecorder) GetLastPrice(ctx, symbol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetLastPrice), ctx, symbol)
}

// GetMarkPrice mocks base method.
func (m *MockPriceRepository) GetMarkPrice(ctx context.Context, symbol string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarkPrice", ctx, symbol)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// GetMarkPrice indicates an expected call of GetMarkPrice.
func (mr *MockPriceRepositoryMockRecorder) GetMarkPrice(ctx, symbol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarkPrice", reflect.TypeOf((*MockPriceRepository)(nil).GetMarkPrice), ctx, symbol)
}

// SetIndexPrice mocks base method.
func (m *MockPriceRepository) SetIndexPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetIndexPrice", ctx, symbol, price)
}

// SetIndexPrice indicates an expected call of SetIndexPrice.
func (mr *MockPriceRepositoryMockRecorder) SetIndexPrice(ctx, symbol, price any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIndexPrice", reflect.TypeOf((*MockPriceRepository)(nil).SetIndexPrice), ctx, symbol, price)
}

// SetLastPrice mocks base method.
func (m *MockPriceRepository) SetLastPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetLastPrice", ctx, symbol, price)
}

// SetLastPrice indicates an expected call of SetLastPrice.
func (mr *MockPriceRepositoryMockRecorder) SetLastPrice(ctx, symbol, price any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLastPrice", reflect.TypeOf((*MockPriceRepository)(nil).SetLastPrice), ctx, symbol, price)
}

// SetMarkPrice mocks base method.
func (m *MockPriceRepository) SetMarkPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetMarkPrice", ctx, symbol, price)
}

// SetMarkPrice indicates an expected call of SetMarkPrice.
func (mr *MockPriceRepositoryMockRecorder) SetMarkPrice(ctx, symbol, price any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMarkPrice", reflect.TypeOf((*MockPriceRepository)(nil).SetMarkPrice), ctx, symbol, price)
}

// SpotRate mocks base method.
func (m *MockPriceRepository) SpotRate(ctx context.Context, base, quote string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SpotRate", ctx, base, quote)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// SpotRate indicates an expected call of SpotRate.
func (mr *MockPriceRepositoryMockRecorder) SpotRate(ctx, base, quote any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SpotRate", reflect.TypeOf((*MockPriceRepository)(nil).SpotRate), ctx, base, quote)
}

// SpotURate mocks base method.
func (m *MockPriceRepository) SpotURate(ctx context.Context, currency string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SpotURate", ctx, currency)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// SpotURate indicates an expected call of SpotURate.
func (mr *MockPriceRepositoryMockRecorder) SpotURate(ctx, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SpotURate", reflect.TypeOf((*MockPriceRepository)(nil).SpotURate), ctx, currency)
}
