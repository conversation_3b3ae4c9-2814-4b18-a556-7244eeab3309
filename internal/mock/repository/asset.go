// Code generated by MockGen. DO NOT EDIT.
// Source: ./asset.go
//
// Generated by this command:
//
//	mockgen -source=./asset.go -destination=../../mock/repository/asset.go -package=repository
//

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	entity "futures-asset/internal/domain/entity"
	repository "futures-asset/internal/domain/repository"
	reflect "reflect"

	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
)

// MockAssetRepository is a mock of AssetRepository interface.
type MockAssetRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAssetRepositoryMockRecorder
}

// MockAssetRepositoryMockRecorder is the mock recorder for MockAssetRepository.
type MockAssetRepositoryMockRecorder struct {
	mock *MockAssetRepository
}

// NewMockAssetRepository creates a new mock instance.
func NewMockAssetRepository(ctrl *gomock.Controller) *MockAssetRepository {
	mock := &MockAssetRepository{ctrl: ctrl}
	mock.recorder = &MockAssetRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetRepository) EXPECT() *MockAssetRepositoryMockRecorder {
	return m.recorder
}

// CalcTrialMargin mocks base method.
func (m *MockAssetRepository) CalcTrialMargin(ctx context.Context, asset *repository.AssetSwap, currency string, posMargin decimal.Decimal, pos repository.PosSwap) (repository.PosSwap, []*entity.TrialAsset) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalcTrialMargin", ctx, asset, currency, posMargin, pos)
	ret0, _ := ret[0].(repository.PosSwap)
	ret1, _ := ret[1].([]*entity.TrialAsset)
	return ret0, ret1
}

// CalcTrialMargin indicates an expected call of CalcTrialMargin.
func (mr *MockAssetRepositoryMockRecorder) CalcTrialMargin(ctx, asset, currency, posMargin, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalcTrialMargin", reflect.TypeOf((*MockAssetRepository)(nil).CalcTrialMargin), ctx, asset, currency, posMargin, pos)
}

// CanTransfer mocks base method.
func (m *MockAssetRepository) CanTransfer(ctx context.Context, asset *repository.AssetSwap, currency string) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CanTransfer", ctx, asset, currency)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CanTransfer indicates an expected call of CanTransfer.
func (mr *MockAssetRepositoryMockRecorder) CanTransfer(ctx, asset, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanTransfer", reflect.TypeOf((*MockAssetRepository)(nil).CanTransfer), ctx, asset, currency)
}

// CrossMarginBalance mocks base method.
func (m *MockAssetRepository) CrossMarginBalance(ctx context.Context, asset *repository.AssetSwap, uid, currency string) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CrossMarginBalance", ctx, asset, uid, currency)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CrossMarginBalance indicates an expected call of CrossMarginBalance.
func (mr *MockAssetRepositoryMockRecorder) CrossMarginBalance(ctx, asset, uid, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CrossMarginBalance", reflect.TypeOf((*MockAssetRepository)(nil).CrossMarginBalance), ctx, asset, uid, currency)
}

// CrossTrialUnrealTotal mocks base method.
func (m *MockAssetRepository) CrossTrialUnrealTotal(ctx context.Context) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CrossTrialUnrealTotal", ctx)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// CrossTrialUnrealTotal indicates an expected call of CrossTrialUnrealTotal.
func (mr *MockAssetRepositoryMockRecorder) CrossTrialUnrealTotal(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CrossTrialUnrealTotal", reflect.TypeOf((*MockAssetRepository)(nil).CrossTrialUnrealTotal), ctx)
}

// CrossUnrealTotal mocks base method.
func (m *MockAssetRepository) CrossUnrealTotal(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CrossUnrealTotal", ctx, asset, area)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// CrossUnrealTotal indicates an expected call of CrossUnrealTotal.
func (mr *MockAssetRepositoryMockRecorder) CrossUnrealTotal(ctx, asset, area any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CrossUnrealTotal", reflect.TypeOf((*MockAssetRepository)(nil).CrossUnrealTotal), ctx, asset, area)
}

// FrozenTotal mocks base method.
func (m *MockAssetRepository) FrozenTotal(ctx context.Context, asset *repository.AssetSwap, area string, isTrials ...bool) decimal.Decimal {
	m.ctrl.T.Helper()
	varargs := []any{ctx, asset, area}
	for _, a := range isTrials {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FrozenTotal", varargs...)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// FrozenTotal indicates an expected call of FrozenTotal.
func (mr *MockAssetRepositoryMockRecorder) FrozenTotal(ctx, asset, area any, isTrials ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, asset, area}, isTrials...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FrozenTotal", reflect.TypeOf((*MockAssetRepository)(nil).FrozenTotal), varargs...)
}

// GetAvailableBase mocks base method.
func (m *MockAssetRepository) GetAvailableBase(ctx context.Context, asset *repository.AssetSwap, marginMode futuresassetpb.MarginMode, currency string) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableBase", ctx, asset, marginMode, currency)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableBase indicates an expected call of GetAvailableBase.
func (mr *MockAssetRepositoryMockRecorder) GetAvailableBase(ctx, asset, marginMode, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableBase", reflect.TypeOf((*MockAssetRepository)(nil).GetAvailableBase), ctx, asset, marginMode, currency)
}

// GetBatchUserAsset mocks base method.
func (m *MockAssetRepository) GetBatchUserAsset(ctx context.Context, req repository.BalanceReq) ([]entity.Wallet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchUserAsset", ctx, req)
	ret0, _ := ret[0].([]entity.Wallet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchUserAsset indicates an expected call of GetBatchUserAsset.
func (mr *MockAssetRepositoryMockRecorder) GetBatchUserAsset(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchUserAsset", reflect.TypeOf((*MockAssetRepository)(nil).GetBatchUserAsset), ctx, req)
}

// GetMarginBalanceAndMaintainMargin mocks base method.
func (m *MockAssetRepository) GetMarginBalanceAndMaintainMargin(ctx context.Context, asset *repository.AssetSwap, pos repository.PosSwap) (decimal.Decimal, decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarginBalanceAndMaintainMargin", ctx, asset, pos)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(decimal.Decimal)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetMarginBalanceAndMaintainMargin indicates an expected call of GetMarginBalanceAndMaintainMargin.
func (mr *MockAssetRepositoryMockRecorder) GetMarginBalanceAndMaintainMargin(ctx, asset, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarginBalanceAndMaintainMargin", reflect.TypeOf((*MockAssetRepository)(nil).GetMarginBalanceAndMaintainMargin), ctx, asset, pos)
}

// HoldCostTotal mocks base method.
func (m *MockAssetRepository) HoldCostTotal(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HoldCostTotal", ctx, asset, area)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// HoldCostTotal indicates an expected call of HoldCostTotal.
func (mr *MockAssetRepositoryMockRecorder) HoldCostTotal(ctx, asset, area any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HoldCostTotal", reflect.TypeOf((*MockAssetRepository)(nil).HoldCostTotal), ctx, asset, area)
}

// HoldCostTotalCross mocks base method.
func (m *MockAssetRepository) HoldCostTotalCross(ctx context.Context, asset *repository.AssetSwap, area string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HoldCostTotalCross", ctx, asset, area)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// HoldCostTotalCross indicates an expected call of HoldCostTotalCross.
func (mr *MockAssetRepositoryMockRecorder) HoldCostTotalCross(ctx, asset, area any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HoldCostTotalCross", reflect.TypeOf((*MockAssetRepository)(nil).HoldCostTotalCross), ctx, asset, area)
}

// HoldCostTotalIsolated mocks base method.
func (m *MockAssetRepository) HoldCostTotalIsolated(ctx context.Context, asset *repository.AssetSwap, area string, isRemoveTrialPos bool) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HoldCostTotalIsolated", ctx, asset, area, isRemoveTrialPos)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// HoldCostTotalIsolated indicates an expected call of HoldCostTotalIsolated.
func (mr *MockAssetRepositoryMockRecorder) HoldCostTotalIsolated(ctx, asset, area, isRemoveTrialPos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HoldCostTotalIsolated", reflect.TypeOf((*MockAssetRepository)(nil).HoldCostTotalIsolated), ctx, asset, area, isRemoveTrialPos)
}

// HoldCostTrialTotal mocks base method.
func (m *MockAssetRepository) HoldCostTrialTotal(ctx context.Context) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HoldCostTrialTotal", ctx)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// HoldCostTrialTotal indicates an expected call of HoldCostTrialTotal.
func (mr *MockAssetRepositoryMockRecorder) HoldCostTrialTotal(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HoldCostTrialTotal", reflect.TypeOf((*MockAssetRepository)(nil).HoldCostTrialTotal), ctx)
}

// OptionsMarginBalance mocks base method.
func (m *MockAssetRepository) OptionsMarginBalance(ctx context.Context, asset *repository.AssetSwap, currency string) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OptionsMarginBalance", ctx, asset, currency)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OptionsMarginBalance indicates an expected call of OptionsMarginBalance.
func (mr *MockAssetRepositoryMockRecorder) OptionsMarginBalance(ctx, asset, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OptionsMarginBalance", reflect.TypeOf((*MockAssetRepository)(nil).OptionsMarginBalance), ctx, asset, currency)
}

// OtherCrossMaintainMargin mocks base method.
func (m *MockAssetRepository) OtherCrossMaintainMargin(ctx context.Context, asset *repository.AssetSwap, code string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OtherCrossMaintainMargin", ctx, asset, code)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// OtherCrossMaintainMargin indicates an expected call of OtherCrossMaintainMargin.
func (mr *MockAssetRepositoryMockRecorder) OtherCrossMaintainMargin(ctx, asset, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OtherCrossMaintainMargin", reflect.TypeOf((*MockAssetRepository)(nil).OtherCrossMaintainMargin), ctx, asset, code)
}

// OtherCrossUnreal mocks base method.
func (m *MockAssetRepository) OtherCrossUnreal(ctx context.Context, asset *repository.AssetSwap, code string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OtherCrossUnreal", ctx, asset, code)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// OtherCrossUnreal indicates an expected call of OtherCrossUnreal.
func (mr *MockAssetRepositoryMockRecorder) OtherCrossUnreal(ctx, asset, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OtherCrossUnreal", reflect.TypeOf((*MockAssetRepository)(nil).OtherCrossUnreal), ctx, asset, code)
}

// SumUserTotalAsset mocks base method.
func (m *MockAssetRepository) SumUserTotalAsset(ctx context.Context, req repository.TotalAssetReq) ([]entity.Wallet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SumUserTotalAsset", ctx, req)
	ret0, _ := ret[0].([]entity.Wallet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SumUserTotalAsset indicates an expected call of SumUserTotalAsset.
func (mr *MockAssetRepositoryMockRecorder) SumUserTotalAsset(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SumUserTotalAsset", reflect.TypeOf((*MockAssetRepository)(nil).SumUserTotalAsset), ctx, req)
}

// TotalCrossMaintainMargin mocks base method.
func (m *MockAssetRepository) TotalCrossMaintainMargin(ctx context.Context, asset *repository.AssetSwap, code string) (decimal.Decimal, decimal.Decimal, map[string]bool, map[string]repository.LevelFilter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TotalCrossMaintainMargin", ctx, asset, code)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(decimal.Decimal)
	ret2, _ := ret[2].(map[string]bool)
	ret3, _ := ret[3].(map[string]repository.LevelFilter)
	ret4, _ := ret[4].(error)
	return ret0, ret1, ret2, ret3, ret4
}

// TotalCrossMaintainMargin indicates an expected call of TotalCrossMaintainMargin.
func (mr *MockAssetRepositoryMockRecorder) TotalCrossMaintainMargin(ctx, asset, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TotalCrossMaintainMargin", reflect.TypeOf((*MockAssetRepository)(nil).TotalCrossMaintainMargin), ctx, asset, code)
}

// TotalJoinBalance mocks base method.
func (m *MockAssetRepository) TotalJoinBalance(ctx context.Context, asset *repository.AssetSwap) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TotalJoinBalance", ctx, asset)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TotalJoinBalance indicates an expected call of TotalJoinBalance.
func (mr *MockAssetRepositoryMockRecorder) TotalJoinBalance(ctx, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TotalJoinBalance", reflect.TypeOf((*MockAssetRepository)(nil).TotalJoinBalance), ctx, asset)
}

// UpdateAsset mocks base method.
func (m *MockAssetRepository) UpdateAsset(ctx context.Context, data entity.Wallet, changeType commonpb.FundingChangeType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAsset", ctx, data, changeType)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAsset indicates an expected call of UpdateAsset.
func (mr *MockAssetRepositoryMockRecorder) UpdateAsset(ctx, data, changeType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAsset", reflect.TypeOf((*MockAssetRepository)(nil).UpdateAsset), ctx, data, changeType)
}

// UpdateUserHoldPos mocks base method.
func (m *MockAssetRepository) UpdateUserHoldPos(ctx context.Context, asset *repository.AssetSwap, long, short, both repository.PosSwap, userType int32, trial bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserHoldPos", ctx, asset, long, short, both, userType, trial)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserHoldPos indicates an expected call of UpdateUserHoldPos.
func (mr *MockAssetRepositoryMockRecorder) UpdateUserHoldPos(ctx, asset, long, short, both, userType, trial any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserHoldPos", reflect.TypeOf((*MockAssetRepository)(nil).UpdateUserHoldPos), ctx, asset, long, short, both, userType, trial)
}

// UpsertAsset mocks base method.
func (m *MockAssetRepository) UpsertAsset(ctx context.Context, data entity.Wallet) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAsset", ctx, data)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAsset indicates an expected call of UpsertAsset.
func (mr *MockAssetRepositoryMockRecorder) UpsertAsset(ctx, data any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAsset", reflect.TypeOf((*MockAssetRepository)(nil).UpsertAsset), ctx, data)
}
