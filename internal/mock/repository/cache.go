// Code generated by MockGen. DO NOT EDIT.
// Source: ./cache.go
//
// Generated by this command:
//
//	mockgen -source=./cache.go -destination=../../mock/repository/cache.go -package=repository
//

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	repository "futures-asset/internal/domain/repository"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// MockCacheRepository is a mock of CacheRepository interface.
type MockCacheRepository struct {
	ctrl     *gomock.Controller
	recorder *MockCacheRepositoryMockRecorder
}

// MockCacheRepositoryMockRecorder is the mock recorder for MockCacheRepository.
type MockCacheRepositoryMockRecorder struct {
	mock *MockCacheRepository
}

// NewMockCacheRepository creates a new mock instance.
func NewMockCacheRepository(ctrl *gomock.Controller) *MockCacheRepository {
	mock := &MockCacheRepository{ctrl: ctrl}
	mock.recorder = &MockCacheRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCacheRepository) EXPECT() *MockCacheRepositoryMockRecorder {
	return m.recorder
}

// FixInitUser mocks base method.
func (m *MockCacheRepository) FixInitUser(ctx context.Context, uid string, leverage map[string]*repository.Leverage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FixInitUser", ctx, uid, leverage)
	ret0, _ := ret[0].(error)
	return ret0
}

// FixInitUser indicates an expected call of FixInitUser.
func (mr *MockCacheRepositoryMockRecorder) FixInitUser(ctx, uid, leverage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FixInitUser", reflect.TypeOf((*MockCacheRepository)(nil).FixInitUser), ctx, uid, leverage)
}

// GetAllCacheKey mocks base method.
func (m *MockCacheRepository) GetAllCacheKey(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCacheKey", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCacheKey indicates an expected call of GetAllCacheKey.
func (mr *MockCacheRepositoryMockRecorder) GetAllCacheKey(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCacheKey", reflect.TypeOf((*MockCacheRepository)(nil).GetAllCacheKey), ctx)
}

// GetUserAssetMode mocks base method.
func (m *MockCacheRepository) GetUserAssetMode(ctx context.Context, uid string) (futuresassetpb.AssetMode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserAssetMode", ctx, uid)
	ret0, _ := ret[0].(futuresassetpb.AssetMode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAssetMode indicates an expected call of GetUserAssetMode.
func (mr *MockCacheRepositoryMockRecorder) GetUserAssetMode(ctx, uid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAssetMode", reflect.TypeOf((*MockCacheRepository)(nil).GetUserAssetMode), ctx, uid)
}

// GetUserPositions mocks base method.
func (m *MockCacheRepository) GetUserPositions(ctx context.Context, uid, pair string) (map[string][]repository.PosSwap, map[string][]repository.PosSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPositions", ctx, uid, pair)
	ret0, _ := ret[0].(map[string][]repository.PosSwap)
	ret1, _ := ret[1].(map[string][]repository.PosSwap)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserPositions indicates an expected call of GetUserPositions.
func (mr *MockCacheRepositoryMockRecorder) GetUserPositions(ctx, uid, pair any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPositions", reflect.TypeOf((*MockCacheRepository)(nil).GetUserPositions), ctx, uid, pair)
}

// InitUser mocks base method.
func (m *MockCacheRepository) InitUser(ctx context.Context, uid string, leverage []*repository.Leverage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitUser", ctx, uid, leverage)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitUser indicates an expected call of InitUser.
func (mr *MockCacheRepositoryMockRecorder) InitUser(ctx, uid, leverage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitUser", reflect.TypeOf((*MockCacheRepository)(nil).InitUser), ctx, uid, leverage)
}

// Load mocks base method.
func (m *MockCacheRepository) Load(ctx context.Context, uid, pair, currency string) (*repository.AssetSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Load", ctx, uid, pair, currency)
	ret0, _ := ret[0].(*repository.AssetSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Load indicates an expected call of Load.
func (mr *MockCacheRepositoryMockRecorder) Load(ctx, uid, pair, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Load", reflect.TypeOf((*MockCacheRepository)(nil).Load), ctx, uid, pair, currency)
}

// SetBothPos mocks base method.
func (m *MockCacheRepository) SetBothPos(ctx context.Context, pos repository.PosSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBothPos", ctx, pos)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetBothPos indicates an expected call of SetBothPos.
func (mr *MockCacheRepositoryMockRecorder) SetBothPos(ctx, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBothPos", reflect.TypeOf((*MockCacheRepository)(nil).SetBothPos), ctx, pos)
}

// SetLeverage mocks base method.
func (m *MockCacheRepository) SetLeverage(ctx context.Context, uid string, leverage map[string]*repository.Leverage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLeverage", ctx, uid, leverage)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLeverage indicates an expected call of SetLeverage.
func (mr *MockCacheRepositoryMockRecorder) SetLeverage(ctx, uid, leverage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLeverage", reflect.TypeOf((*MockCacheRepository)(nil).SetLeverage), ctx, uid, leverage)
}

// SetLongPos mocks base method.
func (m *MockCacheRepository) SetLongPos(ctx context.Context, pos repository.PosSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLongPos", ctx, pos)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLongPos indicates an expected call of SetLongPos.
func (mr *MockCacheRepositoryMockRecorder) SetLongPos(ctx, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLongPos", reflect.TypeOf((*MockCacheRepository)(nil).SetLongPos), ctx, pos)
}

// SetPos mocks base method.
func (m *MockCacheRepository) SetPos(ctx context.Context, side futuresassetpb.PosSide, pos repository.PosSwap, trial bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPos", ctx, side, pos, trial)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPos indicates an expected call of SetPos.
func (mr *MockCacheRepositoryMockRecorder) SetPos(ctx, side, pos, trial any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPos", reflect.TypeOf((*MockCacheRepository)(nil).SetPos), ctx, side, pos, trial)
}

// SetShortPos mocks base method.
func (m *MockCacheRepository) SetShortPos(ctx context.Context, pos repository.PosSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShortPos", ctx, pos)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetShortPos indicates an expected call of SetShortPos.
func (mr *MockCacheRepositoryMockRecorder) SetShortPos(ctx, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShortPos", reflect.TypeOf((*MockCacheRepository)(nil).SetShortPos), ctx, pos)
}

// UpdateAnyPos mocks base method.
func (m *MockCacheRepository) UpdateAnyPos(ctx context.Context, pos repository.PosSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAnyPos", ctx, pos)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAnyPos indicates an expected call of UpdateAnyPos.
func (mr *MockCacheRepositoryMockRecorder) UpdateAnyPos(ctx, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAnyPos", reflect.TypeOf((*MockCacheRepository)(nil).UpdateAnyPos), ctx, pos)
}

// UpdateAssetMode mocks base method.
func (m *MockCacheRepository) UpdateAssetMode(ctx context.Context, uid string, mode futuresassetpb.AssetMode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAssetMode", ctx, uid, mode)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAssetMode indicates an expected call of UpdateAssetMode.
func (mr *MockCacheRepositoryMockRecorder) UpdateAssetMode(ctx, uid, mode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAssetMode", reflect.TypeOf((*MockCacheRepository)(nil).UpdateAssetMode), ctx, uid, mode)
}

// UpdateBalance mocks base method.
func (m *MockCacheRepository) UpdateBalance(ctx context.Context, asset *repository.AssetSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBalance", ctx, asset)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBalance indicates an expected call of UpdateBalance.
func (mr *MockCacheRepositoryMockRecorder) UpdateBalance(ctx, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBalance", reflect.TypeOf((*MockCacheRepository)(nil).UpdateBalance), ctx, asset)
}

// UpdateBothPos mocks base method.
func (m *MockCacheRepository) UpdateBothPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBothPos", ctx, pair, asset)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBothPos indicates an expected call of UpdateBothPos.
func (mr *MockCacheRepositoryMockRecorder) UpdateBothPos(ctx, pair, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBothPos", reflect.TypeOf((*MockCacheRepository)(nil).UpdateBothPos), ctx, pair, asset)
}

// UpdateLeverage mocks base method.
func (m *MockCacheRepository) UpdateLeverage(ctx context.Context, uid string, leverage []*repository.Leverage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLeverage", ctx, uid, leverage)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLeverage indicates an expected call of UpdateLeverage.
func (mr *MockCacheRepositoryMockRecorder) UpdateLeverage(ctx, uid, leverage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLeverage", reflect.TypeOf((*MockCacheRepository)(nil).UpdateLeverage), ctx, uid, leverage)
}

// UpdateLongPos mocks base method.
func (m *MockCacheRepository) UpdateLongPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLongPos", ctx, pair, asset)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLongPos indicates an expected call of UpdateLongPos.
func (mr *MockCacheRepositoryMockRecorder) UpdateLongPos(ctx, pair, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLongPos", reflect.TypeOf((*MockCacheRepository)(nil).UpdateLongPos), ctx, pair, asset)
}

// UpdatePos mocks base method.
func (m *MockCacheRepository) UpdatePos(ctx context.Context, pair string, asset *repository.AssetSwap, trial bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePos", ctx, pair, asset, trial)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePos indicates an expected call of UpdatePos.
func (mr *MockCacheRepositoryMockRecorder) UpdatePos(ctx, pair, asset, trial any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePos", reflect.TypeOf((*MockCacheRepository)(nil).UpdatePos), ctx, pair, asset, trial)
}

// UpdatePosAndAsset mocks base method.
func (m *MockCacheRepository) UpdatePosAndAsset(ctx context.Context, asset *repository.AssetSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePosAndAsset", ctx, asset)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePosAndAsset indicates an expected call of UpdatePosAndAsset.
func (mr *MockCacheRepositoryMockRecorder) UpdatePosAndAsset(ctx, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePosAndAsset", reflect.TypeOf((*MockCacheRepository)(nil).UpdatePosAndAsset), ctx, asset)
}

// UpdatePositionMode mocks base method.
func (m *MockCacheRepository) UpdatePositionMode(ctx context.Context, uid string, mode futuresassetpb.PositionMode) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePositionMode", ctx, uid, mode)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePositionMode indicates an expected call of UpdatePositionMode.
func (mr *MockCacheRepositoryMockRecorder) UpdatePositionMode(ctx, uid, mode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePositionMode", reflect.TypeOf((*MockCacheRepository)(nil).UpdatePositionMode), ctx, uid, mode)
}

// UpdateShortPos mocks base method.
func (m *MockCacheRepository) UpdateShortPos(ctx context.Context, pair string, asset *repository.AssetSwap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateShortPos", ctx, pair, asset)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateShortPos indicates an expected call of UpdateShortPos.
func (mr *MockCacheRepositoryMockRecorder) UpdateShortPos(ctx, pair, asset any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateShortPos", reflect.TypeOf((*MockCacheRepository)(nil).UpdateShortPos), ctx, pair, asset)
}
