// Code generated by MockGen. DO NOT EDIT.
// Source: ./position.go
//
// Generated by this command:
//
//	mockgen -source=./position.go -destination=../../mock/repository/position.go -package=repository
//

// Package repository is a generated GoMock package.
package repository

import (
	context "context"
	entity "futures-asset/internal/domain/entity"
	repository "futures-asset/internal/domain/repository"
	reflect "reflect"

	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
)

// MockPositionRepository is a mock of PositionRepository interface.
type MockPositionRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPositionRepositoryMockRecorder
}

// MockPositionRepositoryMockRecorder is the mock recorder for MockPositionRepository.
type MockPositionRepositoryMockRecorder struct {
	mock *MockPositionRepository
}

// NewMockPositionRepository creates a new mock instance.
func NewMockPositionRepository(ctrl *gomock.Controller) *MockPositionRepository {
	mock := &MockPositionRepository{ctrl: ctrl}
	mock.recorder = &MockPositionRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPositionRepository) EXPECT() *MockPositionRepositoryMockRecorder {
	return m.recorder
}

// DeleteRecords mocks base method.
func (m *MockPositionRepository) DeleteRecords(ctx context.Context, createTime int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRecords", ctx, createTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRecords indicates an expected call of DeleteRecords.
func (mr *MockPositionRepositoryMockRecorder) DeleteRecords(ctx, createTime any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecords", reflect.TypeOf((*MockPositionRepository)(nil).DeleteRecords), ctx, createTime)
}

// GetIsolatedHoldingMargin mocks base method.
func (m *MockPositionRepository) GetIsolatedHoldingMargin(ctx context.Context, pos repository.PosSwap, markPrice decimal.Decimal) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIsolatedHoldingMargin", ctx, pos, markPrice)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// GetIsolatedHoldingMargin indicates an expected call of GetIsolatedHoldingMargin.
func (mr *MockPositionRepositoryMockRecorder) GetIsolatedHoldingMargin(ctx, pos, markPrice any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIsolatedHoldingMargin", reflect.TypeOf((*MockPositionRepository)(nil).GetIsolatedHoldingMargin), ctx, pos, markPrice)
}

// GetPosPartInfo mocks base method.
func (m *MockPositionRepository) GetPosPartInfo(ctx context.Context, awardOpId string) ([]*entity.PosPartInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPosPartInfo", ctx, awardOpId)
	ret0, _ := ret[0].([]*entity.PosPartInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPosPartInfo indicates an expected call of GetPosPartInfo.
func (mr *MockPositionRepositoryMockRecorder) GetPosPartInfo(ctx, awardOpId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPosPartInfo", reflect.TypeOf((*MockPositionRepository)(nil).GetPosPartInfo), ctx, awardOpId)
}

// GetPosSwap mocks base method.
func (m *MockPositionRepository) GetPosSwap(ctx context.Context, posId string) (entity.Position, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPosSwap", ctx, posId)
	ret0, _ := ret[0].(entity.Position)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPosSwap indicates an expected call of GetPosSwap.
func (mr *MockPositionRepositoryMockRecorder) GetPosSwap(ctx, posId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPosSwap", reflect.TypeOf((*MockPositionRepository)(nil).GetPosSwap), ctx, posId)
}

// GetPosSwapList mocks base method.
func (m *MockPositionRepository) GetPosSwapList(param *entity.PositionSearch, userType, liquidationType []int, isHasPos bool) (int64, []entity.Position, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPosSwapList", param, userType, liquidationType, isHasPos)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]entity.Position)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPosSwapList indicates an expected call of GetPosSwapList.
func (mr *MockPositionRepositoryMockRecorder) GetPosSwapList(param, userType, liquidationType, isHasPos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPosSwapList", reflect.TypeOf((*MockPositionRepository)(nil).GetPosSwapList), param, userType, liquidationType, isHasPos)
}

// GetPosTotal mocks base method.
func (m *MockPositionRepository) GetPosTotal(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPosTotal", ctx, symbol)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(decimal.Decimal)
	return ret0, ret1
}

// GetPosTotal indicates an expected call of GetPosTotal.
func (mr *MockPositionRepositoryMockRecorder) GetPosTotal(ctx, symbol any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPosTotal", reflect.TypeOf((*MockPositionRepository)(nil).GetPosTotal), ctx, symbol)
}

// PlatPosDetail mocks base method.
func (m *MockPositionRepository) PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlatPosDetail", ctx, req)
	ret0, _ := ret[0].(repository.PlatPosDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlatPosDetail indicates an expected call of PlatPosDetail.
func (mr *MockPositionRepositoryMockRecorder) PlatPosDetail(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlatPosDetail", reflect.TypeOf((*MockPositionRepository)(nil).PlatPosDetail), ctx, req)
}

// PlatPosList mocks base method.
func (m *MockPositionRepository) PlatPosList(ctx context.Context) (repository.PlatPosList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PlatPosList", ctx)
	ret0, _ := ret[0].(repository.PlatPosList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PlatPosList indicates an expected call of PlatPosList.
func (mr *MockPositionRepositoryMockRecorder) PlatPosList(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PlatPosList", reflect.TypeOf((*MockPositionRepository)(nil).PlatPosList), ctx)
}

// PosInfo mocks base method.
func (m *MockPositionRepository) PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PosInfo", ctx, param)
	ret0, _ := ret[0].(repository.PosQuery)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PosInfo indicates an expected call of PosInfo.
func (mr *MockPositionRepositoryMockRecorder) PosInfo(ctx, param any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PosInfo", reflect.TypeOf((*MockPositionRepository)(nil).PosInfo), ctx, param)
}

// PosTotal mocks base method.
func (m *MockPositionRepository) PosTotal(ctx context.Context, contractCode string) decimal.Decimal {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PosTotal", ctx, contractCode)
	ret0, _ := ret[0].(decimal.Decimal)
	return ret0
}

// PosTotal indicates an expected call of PosTotal.
func (mr *MockPositionRepositoryMockRecorder) PosTotal(ctx, contractCode any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PosTotal", reflect.TypeOf((*MockPositionRepository)(nil).PosTotal), ctx, contractCode)
}

// QueryUserPos mocks base method.
func (m *MockPositionRepository) QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryUserPos", ctx, req)
	ret0, _ := ret[0].(repository.UserPosReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryUserPos indicates an expected call of QueryUserPos.
func (mr *MockPositionRepositoryMockRecorder) QueryUserPos(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryUserPos", reflect.TypeOf((*MockPositionRepository)(nil).QueryUserPos), ctx, req)
}

// UpdatePosLeverage mocks base method.
func (m *MockPositionRepository) UpdatePosLeverage(ctx context.Context, uid string, leverage int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePosLeverage", ctx, uid, leverage)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePosLeverage indicates an expected call of UpdatePosLeverage.
func (mr *MockPositionRepositoryMockRecorder) UpdatePosLeverage(ctx, uid, leverage any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePosLeverage", reflect.TypeOf((*MockPositionRepository)(nil).UpdatePosLeverage), ctx, uid, leverage)
}

// UpdateSubsidy mocks base method.
func (m *MockPositionRepository) UpdateSubsidy(ctx context.Context, uid string, subsidy decimal.Decimal) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSubsidy", ctx, uid, subsidy)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSubsidy indicates an expected call of UpdateSubsidy.
func (mr *MockPositionRepositoryMockRecorder) UpdateSubsidy(ctx, uid, subsidy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSubsidy", reflect.TypeOf((*MockPositionRepository)(nil).UpdateSubsidy), ctx, uid, subsidy)
}

// UpsertPos mocks base method.
func (m *MockPositionRepository) UpsertPos(ctx context.Context, pos *entity.Position) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertPos", ctx, pos)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertPos indicates an expected call of UpsertPos.
func (mr *MockPositionRepositoryMockRecorder) UpsertPos(ctx, pos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertPos", reflect.TypeOf((*MockPositionRepository)(nil).UpsertPos), ctx, pos)
}

// UserHoldPos mocks base method.
func (m *MockPositionRepository) UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserHoldPos", ctx, req)
	ret0, _ := ret[0].(repository.HoldPosReply)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserHoldPos indicates an expected call of UserHoldPos.
func (mr *MockPositionRepositoryMockRecorder) UserHoldPos(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserHoldPos", reflect.TypeOf((*MockPositionRepository)(nil).UserHoldPos), ctx, req)
}

// UserPos mocks base method.
func (m *MockPositionRepository) UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserPos", ctx, req)
	ret0, _ := ret[0].([]repository.PosSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserPos indicates an expected call of UserPos.
func (mr *MockPositionRepositoryMockRecorder) UserPos(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserPos", reflect.TypeOf((*MockPositionRepository)(nil).UserPos), ctx, req)
}
