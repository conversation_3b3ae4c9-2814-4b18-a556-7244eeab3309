package domain

import (
	"errors"
	"strconv"
)

type Code int

func (c Code) ToString() string {
	return strconv.Itoa(int(c))
}

func (c Code) ToInt() int {
	return int(c)
}

const (
	CodeOk               = Code(102000)
	CodeInsufflateFunds  = Code(992001) // 可用资金不足
	CodeAssetDetailError = Code(992002) // 获取用户资产失败

	ErrUserBurst       = Code(992355) // 爆仓中
	InternalError      = Code(992500) // 内部错误
	HoldModeHavePos    = Code(992502) // 调整持仓模式-存在仓位
	HoldModeHaveFrozen = Code(992503) // 调整持仓模式-存在冻结

	CodeParamInvalid = Code(992400) // 参数错误
	CodeExists       = Code(992401)
	CodeNotFound     = Code(992404)

	// 2500xx
	Code250002 = Code(992002) // 从Setting获取合约币对配置失败
	Code250005 = Code(992005) // 开仓数大于可开仓数
	Code250006 = Code(992006) // 签名错误
	Code250007 = Code(992007) // 仓位只能是多仓或空仓
	Code250009 = Code(992009) // 联合保证金 汇率错误
	Code250008 = Code(992008) // 普通用户领取了体验金禁止其API下单
	Code250010 = Code(992010) // 报表导出错误

	// 杠杆倍数-9921xx
	Code992101 = Code(992101) // 调整杠杆倍数-用户有未完成的委托单
	Code992102 = Code(992102) // 调整杠杆倍数-仓位保证金不足 (杠杆倍数过低, 没有足够的可用保证金可以追加, 请重新调整杠杆倍数)
	Code992103 = Code(992103) // 未开通合约或未同意合约协议
	Code992104 = Code(992104) // 调整杠杆倍数-保证金率小于或者等于初始保证金率时, 不能调小杠杆倍数
	Code992105 = Code(992105) // 调整杠杆倍数-逐仓不能调小杠杆倍数
	Code992106 = Code(992106) // 调整杠杆倍数-超过当前持仓仓位的最大杠杆倍数 不能调整
	Code992107 = Code(992107) // 调整杠杆倍数-杠杆倍数超过币对可调整最大杠杆倍数
	Code992108 = Code(992108) // 调整杠杆倍数-失败
	Code992120 = Code(992120) // 调整仓位模式-当前合约存在持仓或挂单，不支持调整保证金模式
	Code992121 = Code(992121) // 调整仓位模式-用户仓位模式错误
	Code992122 = Code(992122) // 调整仓位模式-失败
	Code992123 = Code(992123) // 调整杠杆倍数和仓位模式-联合保证金 仓位模式不能修改为逐仓
	Code992124 = Code(992124) // 调整杠杆倍数和仓位模式-联合保证金 仓位模式不能修逐仓杠杆倍数
	Code992125 = Code(992125) // 调整杠杆倍数时保证金模式错误
	Code992126 = Code(992126) // 调整杠杆倍数和仓位模式-持仓模式错误
	Code992127 = Code(992127) // 调整杠杆倍数和仓位模式-单向持仓不能修改逐仓多仓杠杆倍数和逐仓空仓杠杆倍数
	Code992128 = Code(992128) // 调整杠杆倍数和仓位模式-双向持仓不能修改逐仓单向持仓的杠杆倍数
	Code992129 = Code(992129) // 调整杠杆倍数和仓位模式-联合保证金 全仓 强后杠杆倍数相等不用调整
	Code992130 = Code(992130) // 一键修改全仓失败-逐仓有仓位或更新缓存杠杆倍数失败

	// 2511xx
	Code251101 = Code(251101) // Redis 添加用户锁失败
	Code251102 = Code(251102) // Redis 获取空仓仓位失败
	Code251103 = Code(251103) // Redis 获取多仓仓位失败
	Code251104 = Code(251104) // Redis 更新空仓仓位失败
	Code251105 = Code(251105) // Redis 更新多仓仓位失败
	Code251106 = Code(251106) // Redis 开空仓失败
	Code251107 = Code(251107) // Redis 开多仓失败
	Code251108 = Code(251108) // Redis 平空仓失败
	Code251109 = Code(251109) // Redis 平多仓失败
	Code251110 = Code(251110) // Redis 可平空仓仓位不足
	Code251111 = Code(251111) // Redis 可平多仓仓位不足
	Code251112 = Code(251112) // Redis 获取币对是否结算中状态失败
	Code251113 = Code(251113) // Redis 获取标记价格失败
	Code251114 = Code(251114) // Redis 获取用户杠杆倍数失败
	Code251115 = Code(251115) // Redis 冻结下单的杠杆倍数与用户设置不匹配
	Code251116 = Code(251116) // Redis 冻结仓位价值扣减不足
	Code251117 = Code(251117) // Redis 下单的仓位模式不匹配
	Code251118 = Code(251118) // Redis 解锁仓位超过持有仓位数(冻结仓位大于持有仓位)
	Code251119 = Code(251119) // Redis 暗成交减仓时可平仓位不足
	Code251120 = Code(251120) // Redis 更新空仓+多仓仓位失败
	Code251121 = Code(251121) // Redis 获取所有标记价格出错
	Code251122 = Code(251122) // Redis 更新用户资产及仓位失败
	Code251123 = Code(251123) // Redis 更新单向持仓仓位失败
	Code251124 = Code(251124) // 持仓模式错误
	Code251125 = Code(251125) // Redis 可平单向仓位不足

	// 2520xx
	Code992201 = Code(992201) // 逐仓追加或减少保证金-仓位保证金不足

	// 2523xx 体验金
	ErrOpIdNull          = Code(992350) // 手动回收体验金 领取id 错误
	ErrCurrencyErr       = Code(992351) // 币种不能为空
	ErrRecycleIdNull     = Code(992352) // 回收id 没有
	ErrOpIdNotFind       = Code(992353) // 在当前生效的体验金中没有找到对应的领取id
	TrialFinishOrInvalid = Code(992354) // 体验金已失效或者已完全使用
	ErrPageErr           = Code(992356) // pageNum or pageSize is 0
	ErrActivityIdErr     = Code(992357) // activity id = 0
	ErrValType           = Code(992358) // 估值类型错误
	ErrTrialAssetNotFind = Code(992359) // 没有要回收的体验金
	ErrGetAllCfg         = Code(992360) // 从redis 获取所有合约配置错误
	ErrMaxPos            = Code(992361) // 开的仓位大于当前杠杆倍数可开仓位
	ErrRedisGetCfg       = Code(992362) // 获取合约配置错误
	ErrRedisData         = Code(992363) // 从redis中获取的配置数据错误
	ErrPlatPosList       = Code(992364) // 获取合约持仓列表错误
	ErrPlatPosDetail     = Code(992365) // 获取合约持仓详情错误
	ErrEsSearch          = Code(992366) // es 查询错误
	ErrSqlErr            = Code(992367) // sql err

	// 2524xx
	Code992410     = Code(992410) // Redis 获取资产失败
	Code992405     = Code(992405) // Redis 操作可用或冻结资产失败
	RedisUpdateErr = Code(992406) // redis 数据更新失败
	Code992407     = Code(992407) // 获取资金异常
	Code992408     = Code(992408) // 获取资金异常
	Code992409     = Code(992409) // 获取汇率异常

	UserHoldMoldErr            = Code(992501) // Redis 获取仓位模式失败
	HoldModeRedisErr           = Code(992504) // 调整持仓模式-失败
	JoinMarginErr              = Code(992505) // 获取保证金模式失败-失败
	ChangeJoinMarginHaveFrozen = Code(992506) // 调整保证金模式失败-有冻结
	ChangeJoinMarginHavePos    = Code(992507) // 调整保证金模式-存在仓位
	ChangeJoinMarginRedisErr   = Code(992508) // 调整保证金模式失败-redis失败
	OrderConfirmErr            = Code(992509) // 获取二次确认模式失败-失败
	ChangeOrderConfirmErr      = Code(992510) // 调整二次确认模式失败-通用失败
	ChangeOrderConfirmRedisErr = Code(992511) // 调整二次确认模式失败-redis失败
	OrderConfigErr             = Code(992512) // 获取交易设置失败-失败
	ChangeOrderConfigErr       = Code(992513) // 调整交易设置失败-通用失败
	ChangeOrderConfigRedisErr  = Code(992514) // 调整交易设置失败-redis失败
	Code252515                 = Code(992515) // 调整保证金模式失败-有逐仓

	// 2526xx 体验金相关
	TrialOrderTypeErr             = Code(992601) // 体验金订单类型错误
	TrialMarginModeErr            = Code(992602) // 体验金持仓模式错误，比如位全仓
	TrialHoldModeErr              = Code(992603) // 体验金保证金模式错误，比如位单向持仓
	TrialMaxLeverageErr           = Code(992604) // 体验金杠杆倍数错误, msg=最大杠杆倍数为
	TrialMinHoldTimeErr           = Code(992605) // 体验金最小持仓时间错误, msg=剩余可平仓时间为
	TrialOpIdErr                  = Code(992606) // 体验金不存在
	TrialInsufficientErr          = Code(992607) // 体验金不足
	TrialSubMarginErr             = Code(992608) // 体验金不能减少保证金
	TrialHaveLongShortPositionErr = Code(992609) // 体验金不能同时开多仓和空仓

	// 2530xx 期权相关
	ErrLoadOption    = Code(253000) // Redis获取期权失败
	ErrLoadDemoAsset = Code(253001) // Redis获取模拟盘资金错误
	ErrUpdateOption  = Code(253002) // Redis更新取期权失败
	ErrOptionAsset   = Code(253003) // 获取期权资产失败

	// 2522xx
	Code252200 = Code(252200) // 币对正在结算中
	Code252201 = Code(252201) // 计算解冻资金错误
	Code252202 = Code(252202) //
	Code252203 = Code(252203) //

	// 2523xx 盈亏记录
	ErrUserIdNull   = Code(252300) // 用户id 为空
	ErrDayParam     = Code(252301) // 时间类型错误
	ErrTimeParamErr = Code(252302) // 时间范围错误
	ErrGetPLTrade   = Code(252303) // 获取盈亏记录错误
)

var (
	ErrLockPos                  = errors.New("lock pos err")
	ErrInsufficientOpenBuy      = errors.New("open buy insufficient funds available")        // 可用资金不足
	ErrInsufficientOpenSell     = errors.New("open sell insufficient funds available")       // 可用资金不足
	ErrInsufficientBoth         = errors.New("both insufficient funds available")            // 可用资金不足
	ErrInsufficientPos          = errors.New("open pos greater than pos that can be opened") // 开仓数大于可开仓数
	ErrOrderOrPos               = errors.New("has order or pos")                             // 当前合约存在持仓或挂单，不支持调整保证金模式
	ErrHasFrozen                = errors.New("has frozen")                                   // 有冻结
	ErrHasPos                   = errors.New("has pos")                                      // 有持仓
	ErrDistributedLock          = errors.New("failed to acquire distributed lock")           // 获取分布式锁失败
	ErrLoadUserAsset            = errors.New("failed to load user asset")                    // 加载用户资产失败
	ErrInvalidPosSide           = errors.New("invalid pos side")                             // 仓位方向参数错误
	ErrInsufficientPosAvailable = errors.New("insufficient pos available")                   // 可平仓位不足
)

var ErrMsg = map[Code]string{
	ErrSqlErr:            "sql err",
	Code251121:           "get all mark price err",
	ErrEsSearch:          "es search error",
	CodeParamInvalid:     "param err",
	ErrPlatPosDetail:     "get plat pos detail err",
	ErrPlatPosList:       "get plat pos list err",
	Code992103:           "Not agree contract",
	ErrRedisData:         "redis data err",
	ErrRedisGetCfg:       "get contract cfg redis err",
	ErrMaxPos:            "lever can not open pos",
	ErrGetAllCfg:         "get all contract cfg by redis err",
	ErrTrialAssetNotFind: "user not have no invalid trial asset",
	ErrValType:           "val type err",
	Code992102:           "leverage to low",
	Code992106:           "over max leverage",
	Code992105:           "Cannot be adjusted down leverage",
	ErrActivityIdErr:     "activity id = 0",
	ErrPageErr:           "pageNum or pageSize is 0",
	TrialFinishOrInvalid: "trial asset finish or invalid",
	ErrUserBurst:         "user in bursting",
	ErrOpIdNotFind:       "op id not find in used",
	ErrRecycleIdNull:     "Recycle id null",
	Code992410:           "get user asset err",
	Code251101:           "lock pos err",
	ErrCurrencyErr:       "currency null",
	ErrOpIdNull:          "op id is null",
	ErrUserIdNull:        "user id must not null",
	ErrDayParam:          "day param type err",
	ErrTimeParamErr:      "startTime or endTime err",
	ErrGetPLTrade:        "get profit loss data sql err",
}
