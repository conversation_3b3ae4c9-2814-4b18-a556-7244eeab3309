package domain

import (
	"time"
)

const (
	MutexSwapPosLock             = "fasset:mutex:pos:"
	MutexSwapCronFund            = "fasset:mutex:cron:funding"
	MutexSwapCronTrial           = "fasset:mutex:cron:trial"
	MutexSwapProfitStatic        = "fasset:mutex:cron:profit_static"
	MutesSwapCronSyncProfit      = "fasset:mutex:cron:sync_profit"
	MutesSwapCronSyncTotalProfit = "fasset:mutex:cron:sync_total_profit"

	MutexOptionDemoLock            = "fasset:mutex:option:demo:"
	MutexOptionProfitStatic        = "fasset:mutex:option:cron:profit_static"
	MutesOptionCronSyncProfit      = "fasset:mutex:option:cron:sync_profit"
	MutesOptionCronSyncTotalProfit = "fasset:mutex:option:cron:sync_total_profit"
)

const (
	SyncListSwapAsset      = "fasset:sync:asset:"          // 资产余额同步list
	SyncListSwapPos        = "fasset:sync:pos:"            // 仓位同步list
	SyncListSwapBill       = "fasset:sync:bill"            // 合约账单同步list
	SyncListSwapBillRobot  = "fasset:sync:bill:robot"      // 机器人合约账单同步list
	SyncListSwapProfitLoss = "fasset:sync:profit:loss"     // 盈亏统计同步list
	SyncListUserWinRate    = "fasset:sync:user_win_rate"   // 用户胜率同步list
	SyncListUserStatistics = "fasset:sync:user_statistics" // 用户统计数据同步list
	MutexSwapBurst         = "fasset:mutex:burst"          // 爆仓的 redis key
	MutexSwapTrialBurst    = "fasset:mutex:trial:burst"    // 体验金爆仓的 redis key
	MutexSwapPlatform      = "fasset:mutex:platform"       // 平台信息的 redis key
	MutexScanBurst         = "fasset:mutex:scan"           // 扫描爆仓的 redis key
	MutexTrialScanBurst    = "fasset:mutex:trial:scan"     // 体验金扫描爆仓的 redis key
	SyncPlatformFundRate   = "fasset:sync:funding_rate"    // 同步其他平台资金费率
	ContractSetting        = "swap:setting:contract-map"   // 合约配置

	ProfitLossDate  = "fasset:profit:loss:date"  // redis 中存在的利润管理的数据
	ProfitLossData  = "fasset:profit:loss:data:" // redis 中存在的合约盈亏记录数据
	TotalProfitHash = "fasset:total_profit"      // 累计盈亏数据

	SyncTrialAssetSwap = "fasset:trial:asset" // 合约体验金 数据库数据变化
	PlatHoldPos        = "fasset:plat:pos"    // 平台持仓统计的 key
	PosClear           = "fasset:pos:clear"   // 仓位变为0过
)

const (
	RedisAllUser = "users" // 开通合约用户的用户
)

const (
	ContractAssetKey = "ContractAssetKey" // 合约推送到财务数据的 redis key
	ContractTradeKey = "ContractTradeKey" // 合约推送交易数据的 redis key
)

const (
	ProfitKey = "wallet:fasset:profit"
)

const (
	RedisBRPOPTimeout = 3 * time.Second
)
