package repository

import (
	"context"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"
)

//nolint:interfacebloat
type TradeRepository interface {
	Create(ctx context.Context, trade *entity.Trade) error

	Update(ctx context.Context, trade *entity.Trade) error

	Page(ctx context.Context, param TradePageParam) ([]entity.Trade, int64, error)
}

type TradePageParam struct {
	UID          string
	OrderID      string
	ContractType int
	Symbol       string
	StartTime    int64
	EndTime      int64
	Side         int
	pager.Condition
}
