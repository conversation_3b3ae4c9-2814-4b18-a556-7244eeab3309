package repository

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

type TrialRepository interface {
	GetTrialAssetList(ctx context.Context, param *TAListReq) (TAListRes, error)
	TrialAssetList(ctx context.Context, param *TAListReq) ([]*entity.TrialAsset, int64, error)
	GetNoInvalidTrial(ctx context.Context, req *GetNoInvalidTrialReq) ([]*entity.TrialAsset, error)
	GetTrialAssetSummaryList(ctx context.Context, req *GetTrialAssetSummaryListReq) ([]*entity.TrialAssetSummaryInfo, int64, error)
	GetTrialAssetDetailList(ctx context.Context, req *GetTrialAssetDetailListReq) ([]*entity.TrialAssetDetailInfo, int64, error)
	GetLastUpdateTime(ctx context.Context, uid string) (int64, error)

	// trial asset
	AddTrialAsset(ctx context.Context, addTrial *TrialBase) error
	RecycleTrialAsset(ctx context.Context, req *OperateRecycleTrialAsset) error
	UpdateTrialAsset(ctx context.Context, asset *AssetSwap) error
	UpdateTrialDetail(ctx context.Context, asset *AssetSwap) error

	// trial position
	UpdateTrialPos(ctx context.Context, symbol string, asset *AssetSwap) error
	UpdateTrialLongPos(ctx context.Context, symbol string, asset *AssetSwap) error
	UpdateTrialShortPos(ctx context.Context, symbol string, asset *AssetSwap) error
	UpdateTrialBothPos(ctx context.Context, symbol string, asset *AssetSwap) error

	// db opt
	CreateData(ctx context.Context, addTrial *entity.TrialAsset) error
	Update(ctx context.Context, updateTrial *entity.TrialAsset) error
	FindDataByAwardOpId(ctx context.Context, _awardOpId string) (*entity.TrialAsset, error)
	CountDataByAwardOpId(ctx context.Context, _awardOpId string) (int, error)
	GetTrialStatsData(ctx context.Context, uid string) (res entity.TrialStatsData, err error)
	GetTotalAward(ctx context.Context, uid string) (decimal.Decimal, error)
	GetNoEffective(ctx context.Context, uid string) (decimal.Decimal, error)
	GetTotalUsed(ctx context.Context, uid string) (decimal.Decimal, error)
}

type TAListReq struct {
	pager.Condition

	Type             int    `json:"type"`
	UID              string `json:"uid"`              // 用户id
	Currency         string `json:"currency"`         // 币种 必传
	TrialAssetStatus int    `json:"trialAssetStatus"` // enum TrialAssetStatus
	DisplayStatus    int8   `json:"displayStatus"`    // enum TrialAssetDisplayStatus
	TimeType         int    `json:"timeType"`         // 时间类型 1.领取 2.生效 3.失效
	TimeStart        int64  `json:"timeStart"`        // 起始时间
	TimeEnd          int64  `json:"timeEnd"`          // 结束时间
}

type GetNoInvalidTrialReq struct {
	UID        string `json:"uid"`        // 用户id
	Currency   string `json:"currency"`   // 币种
	ActivityId int64  `json:"activityId"` // 活动id
}

type GetTrialAssetSummaryListReq struct {
	pager.Condition

	UID      string `json:"uid"`
	LastTime int64  `json:"lastTime"`
}

type GetTrialAssetDetailListReq struct {
	pager.Condition

	UID                string `json:"uid"`
	TrialTicketOrderId string `json:"trialTicketOrderId"` // 体验金券码
	ActivityId         int64  `json:"activityId"`         // 活动id
	GetType            int    `json:"getType"`            // 领取方式
	TrialTicketType    int    `json:"trialTicketType"`    // 体验金券类型
	TrialAssetStatus   int    `json:"trialAssetStatus"`   // 体验金券状态
	LastTime           int64  `json:"lastTime"`
}

type TAListRes struct {
	pager.Page
	Amount            decimal.Decimal      `json:"amount"`            // 当前可以体验金
	TAwardAmount      decimal.Decimal      `json:"tAwardAmount"`      // 累计领取体验金
	NoEffectiveAmount decimal.Decimal      `json:"noEffectiveAmount"` // 待生效体验金
	UsedAmount        decimal.Decimal      `json:"usedAmount"`        // 已用体验金
	TInvalidAmount    decimal.Decimal      `json:"tInvalidAmount"`    // 已失效体验金
	Total             int64                `json:"total"`             // 总数
	TrialList         []*entity.TrialAsset `json:"trialList"`         // 体验金列表
}

type OperateRecycleTrialAsset struct {
	RecycleOpId string `json:"recycleOpId"` // 回收操作的唯一id 跟后台回收表一一对应
	Currency    string `json:"currency"`    // 回收币种
	UID         string `json:"uid"`         // 回收的用户id
	AwardOpId   string `json:"awardOpId"`   // 体验金领取的唯一id
}

type (
	TrialList interface {
		sort.Interface
	}
	TrialBase struct {
		AwardOpId        string          `json:"awardOpId"`        // 领取操作的唯一id 必传 不为“”
		Type             int8            `json:"type"`             // 体验金类型 1: 循环，2: 一次性
		MaxLeverage      int16           `json:"maxLeverage"`      // 最大杠杆
		MinHoldTime      int64           `json:"minHoldTime"`      // 最小持仓时间
		Currency         string          `json:"currency"`         // 币种			必传 不为“”
		UID              string          `json:"uid"`              // 用户id			必传 不为“”
		AwardAmount      decimal.Decimal `json:"awardAmount"`      // 领取数量		必传 >0
		ActivityId       int64           `json:"activityId"`       // 活动id 记账用	必传 >0
		InvalidTime      int64           `json:"invalidTime"`      // 失效时间 		必传 >0
		EffectiveTime    int64           `json:"effectiveTime"`    // 生效时间		必传 >0
		WarningTime      int64           `json:"warningTime"`      // 预警时间		必传 >0
		AwardTime        int64           `json:"awardTime"`        // 领取时间		必传 >0
		TrialAssetStatus int             `json:"trialAssetStatus"` // 体验金状态
		RecycleOpId      string          `json:"recycleOpId"`      // 回收操作的唯一id
	}

	// Deprecated: old format, replaced by TrialTimeList
	TrialAsset struct {
		TrialBalance decimal.Decimal      `json:"trialBalance"` // 当前体验金总的可用
		TrialList    []*entity.TrialAsset `json:"trialList"`    // 用户当前未失效的体验金资产
	}

	TrialTimeList []*entity.TrialAsset
)

func (t TrialTimeList) Len() int {
	return len(t)
}

func (t TrialTimeList) Less(i, j int) bool {
	if t[i].Type != t[j].Type {
		return t[i].Type > t[j].Type // 一次性体验金排在前面
	}
	return t[i].InvalidTime < t[j].InvalidTime
}

func (t TrialTimeList) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}

// GetLeverage 获取体验金列表中可以开仓的最大杠杆
func (ts TrialTimeList) GetLeverage() int16 {
	minLeverage := int16(0)
	for _, t := range ts {
		if t.MaxLeverage < minLeverage || minLeverage == 0 {
			minLeverage = t.MaxLeverage
		}
	}
	return minLeverage
}

// GetHoldTime 获取体验金列表中可以开仓的最大持仓时间
func (ts TrialTimeList) GetHoldTime() int64 {
	minHoldTime := int64(0)
	for _, t := range ts {
		if t.MinHoldTime > minHoldTime {
			minHoldTime = t.MinHoldTime
		}
	}
	return minHoldTime
}

// GetLockAmount 获取体验金列表中锁仓的体验金数量
func (ts TrialTimeList) GetLockAmount() (lockAmount decimal.Decimal) {
	for _, t := range ts {
		lockAmount = lockAmount.Add(t.LockAmount)
	}
	return
}

// Gets 获取体验金列表中指定的体验金
func (ts TrialTimeList) Gets(ids ...string) (newTs TrialTimeList) {
	for _, t := range ts {
		if lo.Contains(ids, t.AwardOpId) {
			newTs = append(newTs, t)
		}
	}
	if len(newTs) > 1 {
		sort.Sort(newTs)
	}
	return
}

// GetAvailableAmount 获取体验金列表中可用的体验金数量
func (ts TrialTimeList) GetAvailableAmount() (avilable decimal.Decimal) {
	for _, t := range ts {
		avilable = avilable.Add(t.RecycleAmount)
	}
	return
}

// 回收一次性体验金
func (ts TrialTimeList) Recovery() (total decimal.Decimal) {
	for idx, t := range ts {
		if t.Type == domain.TrialAssetTypeOnce {
			if t.RecycleAmount.IsPositive() {
				ts[idx].RecoveryAmount = t.RecoveryAmount.Add(t.RecycleAmount)
				// ts[idx].AmountUsed = t.AmountUsed.Add(t.RecycleAmount),
				// 回收不计算使用
				ts[idx].RecycleAmount = decimal.Zero
				ts[idx].StatusTime = time.Now().Unix()

				ts[idx].OpType = int(domain.BillTrialAssetRecycle)
				// ts[idx].RecycleOpId = util.GenerateId() // TODO snowflakeID

				total = total.Add(t.RecycleAmount)
			}
		}
	}
	return
}

// SubAvailableAmount 扣除可用体验金
func (ts TrialTimeList) SubAvailableAmount(amount decimal.Decimal) {
	for idx, t := range ts {
		min := decimal.Min(t.RecycleAmount, amount)
		amount = amount.Sub(min)

		ts[idx].RecycleAmount = t.RecycleAmount.Sub(min)
		ts[idx].AmountUsed = t.AmountUsed.Add(min)
		if amount.IsZero() {
			break
		}
	}
}

// 记录回收金额
func (ts TrialTimeList) RecoveryAmount(amount decimal.Decimal) {
	for idx, t := range ts {
		min := decimal.Min(t.RecycleAmount, amount)
		amount = amount.Sub(min)

		ts[idx].RecycleAmount = t.RecycleAmount.Sub(min)
		ts[idx].AmountUsed = t.AmountUsed.Add(min)
		ts[idx].RecoveryAmount = t.RecoveryAmount.Add(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

// 手续费金额
func (ts TrialTimeList) FeeAmount(amount decimal.Decimal) (res map[string]decimal.Decimal) {
	res = make(map[string]decimal.Decimal)
	for idx, t := range ts {
		min := decimal.Min(t.OpenAmount, amount)

		ts[idx].OpenAmount = t.OpenAmount.Sub(min)
		res[t.AwardOpId] = min.Neg() // 负数

		amount = amount.Sub(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

// AddOpenCount 增加开仓次数
func (ts TrialTimeList) AddOpenCount(delta int32) {
	for idx := range ts {
		ts[idx].OpenCount += delta
	}
	return
}

// 开仓金额
func (ts TrialTimeList) AddOpenAmount(amount decimal.Decimal) {
	for idx, t := range ts {
		min := decimal.Min(t.RecycleAmount, amount)
		amount = amount.Sub(min)

		ts[idx].RecycleAmount = t.RecycleAmount.Sub(min)
		ts[idx].OpenAmount = t.OpenAmount.Add(min)
		ts[idx].AmountUsed = t.AmountUsed.Add(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

func (ts TrialTimeList) GetOpenAmount() (openAmount decimal.Decimal) {
	for _, t := range ts {
		openAmount = openAmount.Add(t.OpenAmount)
	}
	return
}

func (ts TrialTimeList) SubOpenAmount(amount decimal.Decimal) {
	for idx, t := range ts {
		min := decimal.Min(t.OpenAmount, amount)
		amount = amount.Sub(min)

		ts[idx].RecycleAmount = t.RecycleAmount.Add(min)
		ts[idx].OpenAmount = t.OpenAmount.Sub(min)
		ts[idx].AmountUsed = t.AmountUsed.Sub(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

// 亏损金额
func (ts TrialTimeList) LossAmount(amount decimal.Decimal) (res map[string]decimal.Decimal, total decimal.Decimal) {
	res = make(map[string]decimal.Decimal)

	for idx, t := range ts {
		min := decimal.Min(t.OpenAmount, amount)

		ts[idx].OpenAmount = t.OpenAmount.Sub(min)
		// ts[idx].AmountUsed = t.AmountUsed.Add(min)
		ts[idx].LossAmount = t.LossAmount.Add(min)
		res[t.AwardOpId] = min.Neg()
		total = total.Sub(min)

		amount = amount.Sub(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

// Lock 锁定体验金
// 每次锁定所有的体验金
func (ts TrialTimeList) Lock() (newTs TrialTimeList) {
	for idx, t := range ts {
		ts[idx].LockAmount = t.RecycleAmount
		ts[idx].RecycleAmount = decimal.Zero
		ts[idx].AmountUsed = t.AmountUsed.Add(t.LockAmount)
	}
	return
}

// UnlockAmount 解锁指定数量的体验金
func (ts TrialTimeList) UnlockAmount(amount decimal.Decimal) {
	for idx, t := range ts {
		min := decimal.Min(t.LockAmount, amount)
		ts[idx].LockAmount = t.LockAmount.Sub(min)
		ts[idx].RecycleAmount = t.RecycleAmount.Add(min)
		ts[idx].AmountUsed = t.AmountUsed.Sub(min)
		amount = amount.Sub(min)
		if amount.IsZero() {
			break
		}
	}
	return
}

// SubTrial 从体验金列表中扣除体验金
func (slf *TrialAsset) SubTrial(fee decimal.Decimal, mqTrial *MqCmsAsset, _changeTrial *[]*entity.TrialAsset, _billAsset *[]BillAssetSync, _billType domain.BillType) {
	diff := decimal.Zero
	logrus.Info(fmt.Sprintf("SubTrial fee:%+v,slf.TrialBalance:%+v", fee, slf.TrialBalance))
	for i := 0; i < len(slf.TrialList); i++ {
		trial := slf.TrialList[i]
		logrus.Info(fmt.Sprintf("i:%+v, slf.TrialList[i]:%+v", i, trial))
		// 体验金在生效时间内
		if trial.TrialAssetStatus < domain.TrialAssetStatusInvalid {
			trial.OpType = mqTrial.OperateType
			if trial.RecycleAmount.LessThan(fee) {
				diff = diff.Add(trial.RecycleAmount)
				fee = fee.Sub(trial.RecycleAmount)
				trial.AmountUsed = trial.AmountUsed.Add(trial.RecycleAmount)
				trial.RecycleAmount = decimal.Zero
				mqTrial.TAsset.TDetail = append(mqTrial.TAsset.TDetail, &entity.TrialAsset{
					AwardOpId: trial.AwardOpId,
					Amount:    trial.RecycleAmount,
				})
				*_changeTrial = append(*_changeTrial, trial)
				continue
			}
			diff = diff.Add(fee)
			trial.AmountUsed = trial.AmountUsed.Add(fee)
			trial.RecycleAmount = trial.RecycleAmount.Sub(fee)
			mqTrial.TAsset.TDetail = append(mqTrial.TAsset.TDetail, &entity.TrialAsset{
				AwardOpId: trial.AwardOpId,
				Amount:    trial.RecycleAmount.Sub(fee),
			})
			*_changeTrial = append(*_changeTrial, trial)
			break
		}
	}

	slf.TrialBalance = slf.TrialBalance.Sub(diff)
	mqTrial.TAsset.TAmount = diff

	billType := _billType
	switch _billType {
	case domain.BillTypeFee:
		billType = domain.BillTypeFeeTrial
	case domain.BillTypeFunding:
		billType = domain.BillTypeFundingTrial
	case domain.BillTypePlatFee:
		billType = domain.BillTypePlatFeeTrial
	case domain.BillTypeReal:
		billType = domain.BillTypeRealTrial
	}

	billAsset := NewBillAssetSync(mqTrial, billType, diff.Neg())
	*_billAsset = append(*_billAsset, *billAsset)
}

func (slf *TrialBase) CheckSlf() bool {
	if slf.AwardOpId == "" || slf.Currency == "" || slf.UID == "" {
		return false
	}
	if !slf.AwardAmount.IsPositive() {
		return false
	}
	if slf.ActivityId <= 0 || slf.InvalidTime <= 0 || slf.EffectiveTime <= 0 || slf.AwardTime <= 0 || slf.WarningTime <= 0 {
		return false
	}
	return true
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf TrialAsset) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf TrialAsset) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
