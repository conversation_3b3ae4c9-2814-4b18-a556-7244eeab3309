package repository

import (
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

// UserHoldPos 结算相关结构体
type UserHoldPos struct {
	UID        string `json:"uid"`        // 用户ID
	UserType   int32  `json:"userType"`   // 用户类型
	Symbol     string `json:"symbol"`     // 合约代码
	MarginMode int32  `json:"marginMode"` // 仓位模式 (1:全仓 2:逐仓)
	Currency   string `json:"currency"`   // 资产币种

	LongPos            decimal.Decimal `json:"lPos"`      // 多仓仓位
	LongOpenTime       int64           `json:"lOpenTime"` // 多仓开仓时间
	LongPosId          string          `json:"lPosId"`    // 持仓id
	LongPriceAvg       decimal.Decimal `json:"lAvg"`      // 开仓均价
	LongIsolatedMargin decimal.Decimal `json:"lMargin"`   // 逐仓仓位保证金 全仓现算
	LongTrialMargin    decimal.Decimal `json:"lTMargin"`  // 逐仓仓位体验金保证金

	ShortPos            decimal.Decimal `json:"sPos"`      // 空仓仓位
	ShortOpenTime       int64           `json:"sOpenTime"` // 多仓开仓时间
	ShortPosId          string          `json:"sPosId"`    // 持仓id
	ShortPriceAvg       decimal.Decimal `json:"sAvg"`      // 开仓均价
	ShortIsolatedMargin decimal.Decimal `json:"sMargin"`   // 逐仓仓位保证金 全仓现算
	ShortTrialMargin    decimal.Decimal `json:"sTMargin"`  // 逐仓仓位体验金保证金

	BothPos            decimal.Decimal `json:"bPos"`      // 单向持仓仓位
	BothOpenTime       int64           `json:"bOpenTime"` // 单向持仓开仓时间
	BothPosId          string          `json:"bPosId"`    // 单向持仓id
	BothPriceAvg       decimal.Decimal `json:"bAvg"`      // 单向持仓开仓均价
	BothIsolatedMargin decimal.Decimal `json:"bMargin"`   // 持仓成本：逐仓直接用 全仓现算
	BothTrialMargin    decimal.Decimal `json:"bTMargin"`  // 持仓成本：逐仓体验金
}

// SettlementPos 通过结算快照获取的结算基本数据。
// 如果用户持全仓，结构体中方向为净持仓方向，仓位为净仓位
// 如果用户持逐仓，则 多仓是多仓，空仓是空仓的数据
type SettlementPos struct {
	UserHoldPos
	Base    string          `json:"base"`    // 交易币
	Quote   string          `json:"quote"`   // 计价币
	PosSide int32           `json:"posSide"` // 方向 (1:多仓 2:空仓)
	Pos     decimal.Decimal `json:"pos"`     // 仓位数
	IsTrial bool            `json:"isTrial"` // 是否体验金仓位
}

func (slf SettlementPos) Symbol() string {
	return util.ContractCode(slf.Base, slf.Quote)
}
