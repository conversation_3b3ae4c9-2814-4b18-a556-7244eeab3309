package repository

import (
	"context"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"
)

//nolint:interfacebloat
type OrderRepository interface {
	Create(ctx context.Context, order *entity.Order) error

	UpdateOrder(ctx context.Context, order *entity.Order) error

	Page(ctx context.Context, param OrderPageParam) ([]entity.Order, int64, error)
	GetByOrderId(ctx context.Context, orderId string) (*entity.Order, error)
}

type OrderPageParam struct {
	UID          string
	OrderID      string
	ContractType int
	Symbol       string
	Side         int
	Status       int
	CloseType    int
	StartTime    int64
	EndTime      int64
	PositionSide int

	MarginMode int
	NotCount   int
	pager.Condition
}
