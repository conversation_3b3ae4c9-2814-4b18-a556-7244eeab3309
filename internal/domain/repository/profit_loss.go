package repository

import (
	"context"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
)

type ProfitLossRepository interface {
	GetPLRecord(ctx context.Context, req *ReqPLRecord) (ResPLRecord, error)
	GetPLTrade(ctx context.Context, req *ReqPLRecord) ([]entity.ProfitLoss, error)
	GetTotalProfit(ctx context.Context, uid string) decimal.Decimal
	GetTotalSubsidy(ctx context.Context) (entity.TotalProfit, error)

	GetTotalProfitData(ctx context.Context, uid, currency string) (entity.TotalProfit, error)
	UpsertTotalProfit(ctx context.Context, totalProfit *entity.TotalProfit) (err error)
	TotalSubsidy(ctx context.Context) (err error)

	UpsertProfitLoss(ctx context.Context, profitLoss *entity.ProfitLoss) (err error)
	GetProfitLossData(ctx context.Context, uid, currency string, operateTime int64) (entity.ProfitLoss, error)
}

type (
	ResPLRecord struct {
		TotalProfitLoss   decimal.Decimal    `json:"totalProfitLoss"`   // 自开通合约的累计盈亏
		Valuation         decimal.Decimal    `json:"valuation"`         // 折合
		DaysAnalysis      ProfitLossAnalysis `json:"daysAnalysis"`      // 盈亏分析
		ProfitLossRecords []ProfitLossRecord `json:"profitLossRecords"` // 盈亏数据
	}
	ProfitLossAnalysis struct {
		DaysNum          int             `json:"daysNum"`          // 统计天数
		CumulativeProfit decimal.Decimal `json:"cumulativeProfit"` // 累计盈利
		CumulativeLoss   decimal.Decimal `json:"cumulativeLoss"`   // 累计亏损
		TotalProfitLoss  decimal.Decimal `json:"totalProfitLoss"`  // 净盈亏
		ProfitDays       int64           `json:"profitDays"`       // 盈利天数
		LossDays         int64           `json:"lossDays"`         // 亏损天数
		EqualDays        int64           `json:"equalDays"`        // 盈利和亏损相等的天数
		AverageProfit    decimal.Decimal `json:"averageProfit"`    // 平均盈利
		AverageLoss      decimal.Decimal `json:"averageLoss"`      // 平均亏损
	}
	ProfitLossRecord struct {
		DayTime          int64           `json:"dayTime"`          // 日期时间戳
		NetIn            decimal.Decimal `json:"netIn"`            // 当日净转入
		TodayProfitLoss  decimal.Decimal `json:"todayProfitLoss"`  // 当日盈亏
		CumulativeProfit decimal.Decimal `json:"cumulativeProfit"` // 累计盈亏
	}
)

type TotalProfit struct {
	UID          string          `json:"uid"`          // 用户ID
	ContractType int32           `json:"contractType"` // 合约类型 (1:当周 2:下周 3:当季 4:次季)
	Symbol       string          `json:"symbol"`       // 合约代码
	Currency     string          `json:"currency"`     // 资产币种
	TotalProfit  decimal.Decimal `json:"totalProfit"`  // 累计盈亏
}
