package repository

import (
	"context"
	"encoding/json"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// UserRepository 定义了用户仓储层接口
type UserRepository interface {
	// SwapInit 初始化用户合约账户
	SwapInit(ctx context.Context, param *UIDParam) domain.Code

	// UserBasicConfig 获取用户基础合约配置
	UserBasicConfig(ctx context.Context, param *UIDParam) (*UserBasicConfig, error)

	// UserLeverage 获取用户配置的合约币对杠杆倍数
	UserLeverage(ctx context.Context, param *CommonParam) ([]*Leverage, error)

	// AdjustLeverageMargin 调整杠杆和保证金
	// AdjustLeverageMargin(ctx context.Context, p *LeverageMarginAdAdjust) (domain.Code, error)

	// AdjustLeverage 调整杠杆倍数
	AdjustLeverage(ctx context.Context, p *LeverageAdjust) (domain.Code, *Leverage, error)

	// UpdateMarginMode 调整保证金配置
	UpdateMarginMode(ctx context.Context, p *MarginAdjust) (domain.Code, *Leverage, error)

	// AdjustMargin 调整保证金
	AdjustMargin(ctx context.Context, p *MarginParam) (domain.Code, error)

	// GetUserAsset 获取用户资产
	GetUserAsset(ctx context.Context, param *ReqAsset) (domain.Code, []Asset)

	// UpdatePositionMode 调整持仓模式
	UpdatePositionMode(ctx context.Context, p *HoldModeParam) (domain.Code, error)

	// LoadJoinMargin 获取参与保证金
	LoadJoinMargin(ctx context.Context, param *CommonParam) (JoinMarginRes, error)

	// UpdateAssetMode 修改参与保证金
	UpdateAssetMode(ctx context.Context, p *ChangeJoinMarginParam) (domain.Code, error)

	// UpdateOrderConfirm 修改下单确认
	UpdateOrderConfirm(ctx context.Context, p *ChangeOrderConfirmParam) (domain.Code, error)

	// ChangeOrderConfig 修改订单配置
	ChangeOrderConfig(ctx context.Context, p *ChangeOrderConfigParam) (domain.Code, error)

	// AdjustCross 一键切换到全仓模式
	AdjustCross(ctx context.Context, p *AdjustCrossParam) ([]string, error)

	GetUserOpenCloseTimes(ctx context.Context, req *OpenCloseTimesReq) (*OpenCloseTimesRes, error)

	GetUserStatistics(ctx context.Context, param *UserStatistics) ([]UserStatisticsReply, error)
}

type (
	OpenCloseTimesReq struct {
		UID string `json:"uid"`
	}

	OpenCloseTimesRes struct {
		UID        string `json:"uid"`
		OpenTimes  int    `json:"openTimes"`
		CloseTimes int    `json:"closeTimes"`
	}
)

type (
	UserStatistics struct {
		AccountType string `json:"accountType"` // 合约类型
	}

	UserStatisticsReply struct {
		UID              string          `json:"uid"`              // 用户ID
		TotalCloseMargin decimal.Decimal `json:"totalCloseMargin"` // 总平仓仓位保证金
		WinRate          decimal.Decimal `json:"winRate"`          // 交易胜率
		TxDays           int             `json:"txDays"`           // 交易时长
		Balance          decimal.Decimal `json:"balance"`          // 当前合约账户余额
		HoldPosValue     decimal.Decimal `json:"holdPosValue"`     // 持仓仓位价值总额
		HoldPosTimes     int             `json:"holdPosTimes"`     // 持仓总次数
	}
)

type UserBasicConfig struct {
	UID          string                      `json:"uid"`
	AssetMode    futuresassetpb.AssetMode    `json:"asset_mode" redis:"asset:mode"`       // 保证金模式 1.单币保证金 2.联合保证金
	PositionMode futuresassetpb.PositionMode `json:"position_mode" redis:"position:mode"` // 持仓模式 1.双向 2.单向
	Confirmation OrderConfirm                `json:"confirmation" redis:"order:confirm"`  // 下单确认
	Configs      OrderConfig                 `json:"configs" redis:"order:config"`        // 交易配置
}

type OrderConfirm struct {
	Limit    int `json:"limit"`     // 限价二次确认
	Market   int `json:"market"`    // 市价二次确认
	Trigger  int `json:"trigger"`   // 止盈止损二次确认
	PostOnly int `json:"post_only"` // 只做Maker二次确认
	Backhand int `json:"backhand"`  // 反手二次确认
}

type OrderConfig struct {
	RebornCard int `json:"reborn_card"` // 复活卡
	Trial      int `json:"trial"`       // 体验金
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *OrderConfirm) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *OrderConfirm) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *UserBasicConfig) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, slf)
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *UserBasicConfig) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf *OrderConfig) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, slf)
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf *OrderConfig) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}
