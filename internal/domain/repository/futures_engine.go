package repository

import (
	"context"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
)

type FuturesEngineRepository interface {
	GetDepthFirstPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal, error)
	SwapDepth(ctx context.Context, symbol string) DepthInfo
	SwapOrders(ctx context.Context, uid, symbol string) SwapOrderPage

	BothReducePositions(ctx context.Context, params *ReducePositionsParams) (*BaseReply, error)
	ClosePositions(ctx context.Context, params *ClosePositionParams) (*ClosePositionReply, error)
	// GetSpotLastPrice 获取现货最新成交价
	GetSpotLastPrice(ctx context.Context, base, quote string) (price decimal.Decimal, err error)
	GetContractLastPrice(ctx context.Context, base, quote string) (price decimal.Decimal, err error)
}

type (
	ReducePositionsParams struct {
		UserId              string   `json:"userId"`
		UserSide            int      `json:"userSide"`            // 委托方向 buy-买 sell-卖
		UserLeverage        int      `json:"userLeverage"`        // 杠杆倍数
		UserMarginMode      int      `json:"userMarginMode"`      // 用户保证金模式
		UserLiquidationType int      `json:"userLiquidationType"` // 用户强平类型
		UserHoldMode        int      `json:"userHoldMode"`
		UserIsMixMargin     int      `json:"userIsMixMargin"`
		UserTimeInForce     string   `json:"userTimeInForce"`
		UserAwardOpIds      []string `json:"UserAwardOpIds"` // 用户奖励操作id

		TargetUserId              string   `json:"targetUserId"`
		TargetUserLeverage        int      `json:"targetUserLeverage"`        // 杠杆倍数
		TargetUserMarginMode      int      `json:"targetUserMarginMode"`      // 被减仓用户保证金模式
		TargetUserLiquidationType int      `json:"targetUserLiquidationType"` // 被减仓用户强平类型
		TargetUserHoldMode        int      `json:"targetUserHoldMode"`
		TargetUserIsMixMargin     int      `json:"targetUserIsMixMargin"`
		TargetUserTimeInForce     string   `json:"targetUserTimeInForce"`
		TargetUserAwardOpIds      []string `json:"targetUserAwardOpIds"` // 被减仓用户奖励操作id

		AccountType string          `json:"accountType"`
		Base        string          `json:"base"`
		Quote       string          `json:"quote"`
		Price       decimal.Decimal `json:"price"`     // 平仓价格
		Amount      decimal.Decimal `json:"amount"`    // 平仓数量
		Platform    string          `json:"platform"`  // 平台来源
		BurstId     string          `json:"burstId"`   // 爆仓ID
		BurstTime   int64           `json:"burstTime"` // 爆仓时间
	}

	BaseReply struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
)

type (
	PositionParams struct {
		Side         int             `json:"side"`      // 委托方向 buy-买 sell-卖
		Offset       int             `json:"offset"`    // 开平方向 open-开 close-平
		OrderType    int             `json:"orderType"` // 委托类型
		IsLimitOrder int             `json:"isLimitOrder"`
		Price        decimal.Decimal `json:"price"`        // 委托价格
		Amount       decimal.Decimal `json:"amount"`       // 委托数量(市价买为交易额)
		TriggerPrice decimal.Decimal `json:"triggerPrice"` // 触发价格
	}
	ClosePositionParams struct {
		PositionParams
		UserId          string   `json:"userId"`          // 用户ID
		AccountType     string   `json:"accountType"`     // 账户类型
		Base            string   `json:"base"`            // 交易币
		Quote           string   `json:"quote"`           // 计价币
		Leverage        int      `json:"leverage"`        // 杠杆倍数
		Platform        string   `json:"platform"`        // 平台来源
		LiquidationType int      `json:"liquidationType"` // 强平类型(爆仓或减仓)
		MarginMode      int      `json:"marginMode"`      // 保证金模式
		HoldMode        int      `json:"holdMode"`
		IsMixMargin     int      `json:"isMixMargin"`
		TimeInForce     string   `json:"timeInForce"`
		Depth           int      `json:"depth"`     // 深度
		BurstId         string   `json:"burstId"`   // 爆仓ID
		BurstTime       int64    `json:"burstTime"` // 爆仓时间
		AwardOpIds      []string `json:"AwardIds"`  // 用户奖励操作id
	}

	ClosePositionReply struct {
		BaseResponse
		Data struct {
			OrderId string `json:"orderId"`
		} `json:"data"`
	}

	BaseResponse struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
)

type (
	SwapOrder     struct{}
	SwapOrderPage struct {
		PageNum  int             `json:"pageNum"`
		PageSize int             `json:"pageSize"`
		Total    int64           `json:"total"`
		Orders   []*entity.Order `json:"orders"`
	}
	SwapOrdersReply struct {
		Code int           `json:"code"`
		Msg  string        `json:"msg"`
		Data SwapOrderPage `json:"data"`
	}
	DepthInfo struct {
		Time int        `json:"time"`
		Bids [][]string `json:"bids"`
		Asks [][]string `json:"asks"`
	}
	DepthReply struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data DepthInfo `json:"data"`
	}
)
