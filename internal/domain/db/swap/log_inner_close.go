package swap

// import (
// 	"futures-asset/pkg/sqllib"
// 	"futures-asset/util"

// 	"gorm.io/gorm"
// 	"github.com/shopspring/decimal"
// )

// // LogInnerClose 内部暗成交减仓记录
// type LogInnerClose struct {
// 	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
// 	UID         string          `gorm:"uid;type:varchar(20);not null"`       // 用户ID
// 	Base        string          `gorm:"base;type:varchar(30);not null"` // 交易币
// 	Quote       string          `gorm:"quote;type:varchar(30);not null"` // 计价币
// 	Price       decimal.Decimal `gorm:"price" sql:"type:decimal(30,15);"`        // 成交价格
// 	Amount      decimal.Decimal `gorm:"amount" sql:"type:decimal(30,15);"`       // 自身抵消仓位数量
// 	OperateTime int64           `gorm:"operate_time;not null"`                   // 操作时间 (撮合提供)
// }

// func (slf *LogInnerClose) TableName() string {
// 	return "log_inner_close" + util.MonthLayout(slf.OperateTime, util.EnumNanosecond)
// }

// // Insert insert inner close pos log
// func (slf *LogInnerClose) Insert(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	tableName := slf.TableName()
// 	if ok := util.TableIsExit(tableName); !ok {
// 		if !db.HasTable(tableName) {
// 			db.Table(tableName).CreateTable(slf)
// 			util.SetNewTableName(tableName)
// 		}
// 	}

// 	if err := db.Table(tableName).Create(slf).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }
