package usecase

import (
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
)

type ProducerUseCase interface {
	SendAssetsBill(message []*entity.BillAsset) error
	SendFundingFee(message []*entity.LogFundingFee) error
	SendFundingRate(message *commonpb.KafkaFundingRate) error
	SendFundingChange(message []*commonpb.KafkaFundingChange) error
	SendAccount(message any) error // 账户相关推送
	SendAccountPosition(message *repository.PosSwap) error
	SendAccountBill(message *entity.BillAsset) error
}

type Account[T any] struct {
	Event commonpb.AccountEvent `json:"event"`
	Data  T                     `json:"data"`
}
