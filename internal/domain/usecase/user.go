package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
)

// mockgen -source./user.go -destination=../../mock/usecase/
//
//nolint:interfacebloat
type UserUseCase interface {
	// AdjustCross 一键切换到全仓模式
	AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error)

	// AdjustLeverage 调整杠杆倍数
	AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, *repository.Leverage, error)

	// AdjustLeverageMargin 调整杠杆和保证金
	// AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error)

	// AdjustMargin 调整保证金
	AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error)

	// UpdateMarginMode 调整保证金配置
	UpdateMarginMode(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error)

	// UpdatePositionMode 更新持仓模式
	UpdatePositionMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error)

	// UpdateAssetMode 修改参与保证金
	UpdateAssetMode(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error)

	// UpdateOrderConfirm 修改下单确认
	UpdateOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error)

	// UpdateOrderConfig 修改订单配置
	UpdateOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error)

	// LoadJoinMargin 获取参与保证金
	LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error)

	// GetUserAsset 获取用户资产
	GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset)

	// SwapInit 初始化用户合约账户
	SwapInit(ctx context.Context, param *repository.UIDParam) domain.Code

	// UserLeverage 获取用户配置的合约币对杠杆倍数
	UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error)

	// UserBasicConfig 获取用户配置的合约基础配置
	UserBasicConfig(ctx context.Context, param *repository.UIDParam) (*repository.UserBasicConfig, error)

	GetUserOpenCloseTimes(ctx context.Context, req *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error)

	GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error)
}
