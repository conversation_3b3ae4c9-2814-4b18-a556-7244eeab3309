package usecase

import (
	"context"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"
)

type TradeUseCase interface {
	TradeList(ctx context.Context, req *TradeListParam) (TradeListResponse, error)
}

type TradeListParam struct {
	pager.Condition
	UID     string `json:"uid"`      // 用户ID
	Symbol  string `json:"symbol"`   // 交易对，如 BTC-USDT
	OrderID string `json:"order_id"` // 订单ID

	StartTime int64 `json:"start_time"` // 开始时间
	EndTime   int64 `json:"end_time"`   // 结束时间
	Side      int   `json:"side"`       // 订单方向
}

type TradeListResponse struct {
	Trades     []entity.Trade `json:"list"`
	pager.Page `json:"page"`
}
