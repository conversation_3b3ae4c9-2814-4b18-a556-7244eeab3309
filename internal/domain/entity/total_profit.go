package entity

import (
	"github.com/shopspring/decimal"
)

type TotalProfit struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	UID         string          `gorm:"uid;type:varchar(20);not null"`           // 用户ID
	Currency    string          `gorm:"currency;type:varchar(25);not null"`      // 资产币种
	TotalProfit decimal.Decimal `gorm:"total_profit" sql:"type:decimal(30,15);"` // 累计盈亏
	Subsidy     decimal.Decimal `gorm:"subsidy" sql:"type:decimal(30,15);"`      // 穿仓补贴数量
	CreateTime  int64           `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime  int64           `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *TotalProfit) TableName() string {
	return "total_profit"
}
