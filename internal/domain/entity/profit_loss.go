package entity

import (
	"github.com/shopspring/decimal"
)

type ProfitLoss struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT"`
	UID         string          `gorm:"uid;type:varchar(20);not null"`          // 用户ID
	Currency    string          `gorm:"currency;type:varchar(25);not null"`     // 资产币种
	NetIn       decimal.Decimal `gorm:"net_in" sql:"type:decimal(30,15);"`      // 单日净转入
	ProfitLoss  decimal.Decimal `gorm:"profit_loss" sql:"type:decimal(30,15);"` // 当日盈亏
	OperateTime int64           `gorm:"operate_time"`                           // 统计日的0：0：0 的时间戳

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *ProfitLoss) TableName() string {
	return "profit_loss"
}
