package entity

import (
	"github.com/shopspring/decimal"
)

type LogFundingRate struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	Symbol      string          `gorm:"symbol;type:varchar(50);not null" json:"symbol"`              // 币对
	FundingRate decimal.Decimal `gorm:"funding_rate" sql:"type:decimal(30,15);" json:"funding_rate"` // 资金费率
	MarkPrice   decimal.Decimal `gorm:"mark_price" sql:"type:decimal(30,15);" json:"mark_price"`     // 标记价格
	IndexPrice  decimal.Decimal `gorm:"index_price;" sql:"type:decimal(30,15);" json:"index_price"`  // 指数价格
	FundingTime int64           `gorm:"funding_time;not null" json:"funding_time"`                   // 结算时间
}

func (slf *LogFundingRate) TableName() string {
	return "funding_rate"
}
