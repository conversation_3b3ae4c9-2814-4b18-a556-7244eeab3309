package entity

// import (
// 	"encoding/json"
// 	"fmt"
// 	"futures-asset/util"

// 	"gorm.io/gorm"
// 	"github.com/shopspring/decimal"
// )

// // UserWinRate 用户平仓胜率
// type UserWinRate struct {
// 	Id               string          `gorm:"id;PRIMARY_KEY;" json:"id"`
// 	UID              string          `gorm:"uid;type:varchar(20);not null" json:"uid"`              // 用户ID
// 	AccountType      string          `gorm:"account_type;type:varchar(20);not null" json:"accountType"` // 账户类型
// 	Symbol     string          `gorm:"symbol;type:varchar(30);" json:"contractCode"`       // 合约币对
// 	Times            int64           `gorm:"times;type:int(11);comment:'平仓次数'" json:"times"`
// 	WinTimes         int64           `gorm:"win_times;type:int(11);comment:'平仓盈利次数'" json:"winTimes"`
// 	WinRate          decimal.Decimal `gorm:"win_rate;comment:'胜率(小数)'" sql:"type:decimal(20,6);" json:"winRate"`
// 	TotalProfit      decimal.Decimal `gorm:"total_profit;comment:'总收益'" sql:"type:decimal(30,10);" json:"totalProfit"`
// 	TotalClose       decimal.Decimal `gorm:"total_close;comment:'总平仓仓位价值'" sql:"type:decimal(35,10);" json:"totalClose"`
// 	TotalProfitRate  decimal.Decimal `gorm:"total_profit_rate;comment:'总收益率(小数)'" sql:"type:decimal(20,6);" json:"totalProfitRate"`
// 	TotalCloseMargin decimal.Decimal `gorm:"total_close_margin;comment:'总平仓保证金合'" sql:"type:decimal(35,10);" json:"totalCloseMargin"`
// }

// // MarshalBinary implement encoding.BinaryMarshaler for redis
// func (slf *UserWinRate) MarshalBinary() ([]byte, error) {
// 	return json.Marshal(slf)
// }

// // UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
// func (slf *UserWinRate) UnmarshalBinary(data []byte) error {
// 	return json.Unmarshal(data, &slf)
// }

// func NewUserWinRate() *UserWinRate {
// 	return &UserWinRate{}
// }

// func (slf *UserWinRate) CreateTable(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	tableName := slf.TableName()
// 	if !db.HasTable(tableName) {
// 		err := db.Table(tableName).CreateTable(slf).Error
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	return nil
// }

// func (slf *UserWinRate) TableName() string {
// 	return "user_win_rate"
// }

// func (slf *UserWinRate) GetId() string {
// 	slf.Id = util.RateId(slf.UID, slf.AccountType, slf.ContractCode)
// 	return slf.Id
// }

// // Upsert update swap asset
// func (slf *UserWinRate) Upsert(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	insertColumns := "`id`, `user_id`,`account_type`,`symbol`,`times`,`win_times`,`win_rate`,`total_profit`,`total_close`,`total_profit_rate`,`total_close_margin`"
// 	updateColumns := "`times`=?,`win_times`=?,`win_rate`=?,`total_profit`=?,`total_close`=?,`total_profit_rate`=?,`total_close_margin`=?"
// 	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
// 		slf.TableName(), insertColumns, updateColumns)
// 	if err := db.Exec(sql,
// 		slf.GetId(), slf.UID, slf.AccountType, slf.ContractCode, slf.Times, slf.WinTimes, slf.WinRate, slf.TotalProfit, slf.TotalClose, slf.TotalProfitRate, slf.TotalCloseMargin,
// 		slf.Times, slf.WinTimes, slf.WinRate, slf.TotalProfit, slf.TotalClose, slf.TotalProfitRate, slf.TotalCloseMargin).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

// // GetWinRateByUser 以用户维度获取胜率数据
// func (slf *UserWinRate) GetWinRateByUser(db *gorm.DB) (result []UserWinRate, err error) {
// 	result = make([]UserWinRate, 0)
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return result, err
// 		}
// 	}

// 	sqlStr := `SELECT user_id, account_type, SUM(times) as times, SUM(win_times) as win_times, SUM(total_close_margin) as total_close_margin
// 				FROM user_win_rate WHERE account_type=? GROUP BY user_id, account_type;`
// 	if err := db.Raw(sqlStr, slf.AccountType).Find(&result).Error; err != nil {
// 		return result, err
// 	}

// 	return result, nil
// }

// // GetAll 获取所有用户胜率
// func (slf *UserWinRate) GetAll(db *gorm.DB) (result []UserWinRate, err error) {
// 	result = make([]UserWinRate, 0)
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return result, err
// 		}
// 	}

// 	if err := db.Model(slf).Find(&result).Error; err != nil {
// 		return result, err
// 	}

// 	return result, nil
// }
