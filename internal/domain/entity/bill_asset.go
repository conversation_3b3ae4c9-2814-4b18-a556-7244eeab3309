package entity

import (
	"github.com/shopspring/decimal"
)

// BillAsset contract asset bill log
type BillAsset struct {
	BillID         int64           `gorm:"bill_id;PRIMARY_KEY;type:bigint;" json:"bill_id"`              // 账单ID
	UID            string          `gorm:"uid;type:varchar(20);not null" json:"uid"`                     // 用户ID
	Symbol         string          `gorm:"symbol;type:varchar(50);" json:"symbol"`                       // 合约代码
	Currency       string          `gorm:"currency;type:varchar(25);not null" json:"currency"`           // 资产币种
	BillType       int             `gorm:"bill_type;" sql:"type:SMALLINT;" json:"bill_type"`             // 账单类型
	FlowType       int             `gorm:"flow_type;" sql:"type:SMALLINT;" json:"flow_type"`             // 流水方向（1-入账，2-出账）
	Amount         decimal.Decimal `gorm:"amount" sql:"type:decimal(30,15);" json:"amount"`              // 数量
	Balance        decimal.Decimal `gorm:"balance" sql:"type:decimal(30,15);" json:"balance"`            // 变动后余额
	FundingRate    decimal.Decimal `gorm:"funding_rate;" sql:"type:decimal(30,15);" json:"funding_rate"` // 资金费率
	MarkPrice      decimal.Decimal `gorm:"mark_price" sql:"type:decimal(30,15);" json:"mark_price"`      // 标记价格
	RefID          string          `gorm:"ref_id;type:varchar(50);not null" json:"ref_id"`               // 关联业务单号 (结算的时候是结算的唯一id， 爆仓的时候是爆仓操作的唯一id)
	FromPair       string          `gorm:"from_pair;type:varchar(20)" json:"from_pair"`                  // 转出币对
	ToPair         string          `gorm:"to_pair;type:varchar(20)" json:"to_pair"`                      // 转入币对
	FromWalletType int             `gorm:"from_wallet_type;" json:"from_wallet_type"`                    // 转出账户
	ToWalletType   int             `gorm:"to_wallet_type;" json:"to_wallet_type"`                        // 转入账户
	RecycleID      string          `gorm:"recycle_id;type:varchar(50);not null" json:"recycle_id"`       // 体验金回收的唯一id
	CreateTime     int64           `gorm:"create_time;not null" json:"create_time"`                      // 创建时间
}

// TableName get bill swap table name
func (slf *BillAsset) TableName() string {
	return "bill_asset"
}

// RobotTableName get bill swap table name
func (slf *BillAsset) RobotTableName(uid string) string {
	return "bill_swap_robot_" + uid
}
