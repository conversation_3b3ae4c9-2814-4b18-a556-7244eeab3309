package entity

import "github.com/shopspring/decimal"

type FundingFeePos struct {
	PosSide      int             `json:"posSide"`      // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
	Pos          decimal.Decimal `json:"pos"`          // 仓位数
	OpenPriceAvg decimal.Decimal `json:"openPriceAvg"` // 开仓均价
	Liquidation  decimal.Decimal `json:"liquidation"`  // 预估强评价
	Margin       decimal.Decimal `json:"margin"`       // 仓位保证金
	OpenTime     int64           `json:"openTime"`     // 开仓时间
	PosId        string          `json:"posId"`        // 持仓唯一ID
}

type LogFundingFee struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	RefID       string          `gorm:"ref_id;type:varchar(50);not null" json:"ref_id"`                         // 资金费用关联单号
	UID         string          `gorm:"uid;type:varchar(20);not null" json:"uid"`                               // 用户ID
	UserType    int32           `gorm:"user_type;type:varchar(20);not null" json:"user_type"`                   // 用户类型
	Symbol      string          `gorm:"symbol;type:varchar(50);not null" json:"symbol"`                         // 合约代码
	Currency    string          `gorm:"currency;type:varchar(50);not null" json:"currency"`                     // 结算币种
	FundingRate decimal.Decimal `gorm:"funding_rate;" sql:"type:decimal(30,15);" json:"funding_rate"`           // 资金费率
	MarkPrice   decimal.Decimal `gorm:"mark_price" sql:"type:decimal(30,15);" json:"mark_price"`                // 标记价格
	Amount      decimal.Decimal `gorm:"amount" sql:"type:decimal(30,15);" json:"amount"`                        // 资金费用金额
	NetPos      decimal.Decimal `gorm:"net_pos" sql:"type:decimal(30,15);" json:"net_pos"`                      // 净持仓
	PosSide     int             `gorm:"pos_side" json:"pos_side"`                                               // 净持仓方向
	Direction   int             `gorm:"direction" json:"direction"`                                             // 用户资金费用方向 1.收入，2.支出
	MarginMode  int             `gorm:"margin_mode" sql:"type:SMALLINT;" json:"margin_mode"`                    // 保证金模式 (1:全仓模式 2:追仓模式)
	Leverage    int             `gorm:"leverage;not null" json:"leverage"`                                      // 杠杆倍数
	OperateTime int64           `gorm:"operate_time;not null" sql:"index:idx_operate_time" json:"operate_time"` // 资金费用收支时间
	StrPos      string          `gorm:"column:str_pos;type:text" json:"str_pos"`                                // 仓位数据 逐仓只有单个仓位数据，全仓有多空 []FundPos{} Marshal 之后
	PosData     []FundingFeePos `gorm:"-" json:"pos_data"`
}

func (slf *LogFundingFee) TableName() string {
	return "log_funding_fee"
}
