package entity

import (
	"github.com/shopspring/decimal"
)

type Position struct {
	Id              string          `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`                        // 持仓ID
	UID             string          `gorm:"uid;type:varchar(20);not null" json:"uid"`                          // 用户ID
	UserType        int             `gorm:"user_type;type:SMALLINT;" json:"user_type"`                         // 用户类型
	PosSide         int32           `gorm:"pos_side;not null" json:"pos_side"`                                 // 方向 (1:多仓 2:空仓)
	Leverage        int             `gorm:"leverage;not null" json:"leverage"`                                 // 杠杆倍数
	AccountType     string          `gorm:"account_type;type:varchar(20);" json:"account_type"`                // 合约类型
	Symbol          string          `gorm:"symbol;type:varchar(50);not null" json:"symbol"`                    // 合约代码
	Currency        string          `gorm:"currency;type:varchar(25);not null" json:"currency"`                // 资产币种
	Pos             decimal.Decimal `gorm:"pos" sql:"type:decimal(30,15);" json:"pos"`                         // 仓位
	PosAvailable    decimal.Decimal `gorm:"pos_available" sql:"type:decimal(30,15);" json:"pos_available"`     // 可平仓位
	MarginMode      int32           `gorm:"margin_mode" sql:"type:SMALLINT;" json:"margin_mode"`               // 保证金模式 (1:全仓模式 2:追仓模式)
	IsolatedMargin  decimal.Decimal `gorm:"isolated_margin" sql:"type:decimal(30,15);" json:"isolated_margin"` // 逐仓位的保证金
	OpenPriceAvg    decimal.Decimal `gorm:"open_price_avg" sql:"type:decimal(30,15);" json:"open_price_avg"`   // 开仓均价
	OpenTime        int64           `gorm:"open_time" json:"open_time"`                                        // 开仓时间
	PosStatus       int32           `gorm:"pos_status" sql:"type:SMALLINT;" json:"pos_status"`                 // 持仓状态(1:持仓中 2:已结束)
	LiquidationType int32           `gorm:"liquidation_type" sql:"type:SMALLINT;" json:"liquidation_type"`     // 强平类型
	ProfitReal      decimal.Decimal `gorm:"profit_real" sql:"type:decimal(30,15);" json:"profit_real"`         // 持仓已实现盈亏
	Subsidy         decimal.Decimal `gorm:"subsidy" sql:"type:decimal(30,15);" json:"subsidy"`                 // 穿仓补贴金额
	RebornId        string          `gorm:"reborn_id;type:varchar(50);not null" json:"reborn_id"`              // 复活卡ID
	TrialMargin     decimal.Decimal `gorm:"trial_margin" sql:"type:decimal(30,15);" json:"trial_margin"`       // 体验金保证金
	AwardIds        string          `gorm:"award_ids;type:varchar(500);not null" json:"award_ids"`             // 奖励操作ID

	MaintenMarginRate decimal.Decimal `gorm:"-" json:"mainten_margin_rate"` // 维持保证金率
	MaintenMargin     decimal.Decimal `gorm:"-" json:"mainten_margin"`      // 维持保证金
	MarginRate        decimal.Decimal `gorm:"-" json:"margin_rate"`         // 保证金率
	MarginBalance     decimal.Decimal `gorm:"-" json:"margin_balance"`      // 保证金余额
	ReturnRate        decimal.Decimal `gorm:"-" json:"return_rate"`         // 回报率
	ProfitUnreal      decimal.Decimal `gorm:"-" json:"profit_unreal"`       // 未实现盈亏
	Liquidation       decimal.Decimal `gorm:"-" json:"liquidation"`         // 预估强评价
	MarkPrice         decimal.Decimal `gorm:"-" json:"mark_price"`          // 标记价格
	RivalScore        decimal.Decimal `gorm:"-" json:"rival_score"`         // 对手方强制减仓指数

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func NewPosSwap() *Position {
	return &Position{}
}

func (slf *Position) TableName() string {
	return "position"
}

type PosPartInfo struct {
	Id string `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`
}
