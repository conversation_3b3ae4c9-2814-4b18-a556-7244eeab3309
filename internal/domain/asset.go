package domain

import "fmt"

type RedisKey string

// contract asset redis key
const (
	AssetPrefix  RedisKey = "fasset:%s"     // fasset:{uid}
	PosFlag      RedisKey = "pos"           // pos flag
	PositionMode RedisKey = "position:mode" // 持仓模式 1.双向 2 单向
	AssetMode    RedisKey = "asset:mode"    // 保证金模式 1.单币保证金 2.联合保证金
	OrderConfirm RedisKey = "order:confirm" // 下单确认模式
	OrderConfig  RedisKey = "order:config"  // 交易设置
	Balance      RedisKey = "balance"       // swap asset balance suffix
	Leverage     RedisKey = "leverage"      // leverage suffix
	Frozen       RedisKey = "frozen"        // swap asset frozen suffix

	// 体验金
	TrialAssetPrefix RedisKey = "trial:%s%s"     // 体验金资产前缀
	TrialBalance     RedisKey = "trial:balance"  // 体验金余额
	TrialDetail      RedisKey = "trial:detail"   // 体验金详情
	TrialConsume     RedisKey = "trial:consume"  // 体验金消耗
	TrialRecovery    RedisKey = "trial:recovery" // 体验金恢复
	TrialLoss        RedisKey = "trial:loss"     // 体验金亏损

	RealSuffix = ":real" // swap asset real profit suffix
	// pos
	LongPosSuffix  = ":pos:long"  // swap asset long pos profit suffix
	ShortPosSuffix = ":pos:short" // swap asset short pos profit suffix
	BothPosSuffix  = ":pos:both"  // swap asset both pos profit suffix

	KLineTicker24hs RedisKey = "fkline:to:ticker24hs" // fasset:{uid}
)

const (
	UserPosBase      RedisKey = "fasset:pos:%s" // 用户仓位备份的key
	UserTrialPosBase RedisKey = "fasset:trial:pos:"
)

const (
	// 平台
	PlatformBinance = "binance"
	PlatformHuobi   = "huobi"
	PlatformOkex    = "okex"

	// K线类型
	KLineTypeLastPrice  = 1 // 最新成交价
	KLineTypeIndexPrice = 2 // 指数价格
	KLineTypeMarkPrice  = 3 // 标价价格
)

func (key RedisKey) Key(param ...interface{}) string {
	return fmt.Sprintf(key.String(), param...)
}

func (key RedisKey) String() string {
	return string(key)
}

func GetAllUserPosKey(symbol string) string {
	return UserPosBase.Key(symbol)
}

func GetAllUserTrialPosKey(symbol string) string {
	return UserTrialPosBase.Key(symbol)
}
