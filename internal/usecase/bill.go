package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"
)

type BillUseCaseParam struct {
	dig.In

	BillRepo repository.BillRepository
}

type BillUseCase struct {
	billRepo repository.BillRepository
}

func NewBillUseCase(param BillUseCaseParam) usecase.BillUseCase {
	return &BillUseCase{billRepo: param.BillRepo}
}

func (uc *BillUseCase) BillAsset(ctx context.Context, param *usecase.BillAssetParam) (*usecase.BillAssetReply, error) {
	reply := &usecase.BillAssetReply{Page: pager.Page{PageIndex: param.PageIndex, PageSize: param.PageSize, Total: 0}, List: make([]usecase.BillAsset, 0)}

	total, bills, err := uc.billRepo.GetBillAsset(ctx, &repository.AssetBillParam{
		Condition: pager.Condition{
			PageIndex: param.PageIndex,
			PageSize:  param.PageSize,
		},
		Symbol:    param.Symbol,
		Currency:  param.Currency,
		BillType:  param.BillType,
		StartTime: param.StartTime,
		EndTime:   param.EndTime,
		UID:       param.UID,
		Plat:      param.Plat,
		IsTrial:   param.IsTrial,
	})
	if err != nil {
		return reply, err
	}

	reply.Total = total
	reply.List = make([]usecase.BillAsset, 0, len(bills))
	for _, bill := range bills {
		reply.List = append(reply.List, usecase.BillAsset{
			BillID:         bill.BillID,
			UID:            bill.UID,
			Symbol:         bill.Symbol,
			Currency:       bill.Currency,
			BillType:       bill.BillType,
			FlowType:       bill.FlowType,
			Amount:         bill.Amount,
			Balance:        bill.Balance,
			FundingRate:    bill.FundingRate.Mul(decimal.NewFromFloat(100)),
			MarkPrice:      bill.MarkPrice.Truncate(domain.CurrencyPrecision),
			RefID:          bill.RefID,
			FromPair:       bill.FromPair,
			ToPair:         bill.ToPair,
			FromWalletType: bill.FromWalletType,
			ToWalletType:   bill.ToWalletType,
			RecycleID:      bill.RecycleID,
			CreateTime:     bill.CreateTime / 1e9,
		})
	}

	return reply, nil
}
