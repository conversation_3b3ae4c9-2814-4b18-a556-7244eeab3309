package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// TrialUseCase 资产用例实现
type TrialUseCase struct {
	config    *cfg.Config[configs.Config]
	trialRepo repository.TrialRepository
}

// TrialUseCaseParam 资产用例参数
type TrialUseCaseParam struct {
	dig.In

	Config    *cfg.Config[configs.Config] `name:"config"`
	TrialRepo repository.TrialRepository
}

// NewTrialUseCase 创建资产用例实例
func NewTrialUseCase(param TrialUseCaseParam) usecase.TrialUseCase {
	return &TrialUseCase{
		config:    param.Config,
		trialRepo: param.TrialRepo,
	}
}

// GetNoInvalidTrial implements usecase.TrialUseCase.
func (t *TrialUseCase) GetNoInvalidTrial(ctx context.Context, req *repository.GetNoInvalidTrialReq) ([]*entity.TrialAsset, error) {
	return t.trialRepo.GetNoInvalidTrial(ctx, req)
}

// GetTrialAssetDetailList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetDetailList(ctx context.Context, req *repository.GetTrialAssetDetailListReq) ([]*entity.TrialAssetDetailInfo, int64, error) {
	return t.trialRepo.GetTrialAssetDetailList(ctx, req)
}

// GetTrialAssetList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetList(ctx context.Context, req *repository.TAListReq) (repository.TAListRes, error) {
	return t.trialRepo.GetTrialAssetList(ctx, req)
}

// GetTrialAssetSummaryList implements usecase.TrialUseCase.
func (t *TrialUseCase) GetTrialAssetSummaryList(ctx context.Context, req *repository.GetTrialAssetSummaryListReq) ([]*entity.TrialAssetSummaryInfo, int64, error) {
	return t.trialRepo.GetTrialAssetSummaryList(ctx, req)
}

// TrialAssetList implements usecase.TrialUseCase.
func (t *TrialUseCase) TrialAssetList(ctx context.Context, param *repository.TAListReq) ([]*entity.TrialAsset, int64, error) {
	return t.trialRepo.TrialAssetList(ctx, param)
}

func (t *TrialUseCase) GetLastUpdateTime(ctx context.Context, uid string) (int64, error) {
	return t.trialRepo.GetLastUpdateTime(ctx, uid)
}

func (t *TrialUseCase) AddTrialAsset(ctx context.Context, req *repository.TrialBase) error {
	return t.trialRepo.AddTrialAsset(ctx, req)
}

func (t *TrialUseCase) RecycleTrialAsset(ctx context.Context, req *repository.OperateRecycleTrialAsset) error {
	return t.trialRepo.RecycleTrialAsset(ctx, req)
}

// OpenTrialLongPos 开体验金多仓
// func (slf *PosCache) OpenTrialLongPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (reply payload.PosReply, err error) {
// 	pos := &asset.TrialLongPos
// 	// 体验金只能开逐仓
// 	if trade.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
// 		err = errors.New("OpenTrialLongPos MarginMode must be isolated")
// 		return
// 	}

// 	if pos.HasPosStatus(domain.PosStatusNone, domain.PosStatusEnd) {
// 		pos.NewPos(trade.UID, trade.UserType, trade.MarginMode, trade.Leverage)
// 	}

// 	// 获取体验金, 可以放到外面判断错误
// 	trialList := asset.TrialDetail.Gets(trade.AwardIds...)
// 	if len(trialList) == 0 {
// 		log.Printf("OpenTrialLongPos trialList is empty, uid:%s, awardOpIds:%v", slf.UID, trade.AwardIds)
// 		return reply, errors.New("trialList is empty")
// 	}
// 	lockAmount := trialList.GetLockAmount()

// 	// 计算总的保证金
// 	pos.AddAwardOpIds(trade.AwardIds...)
// 	pos.UpdateOpenPriceAvg(slf.Amount, slf.Price)
// 	pos.UpdatePos(slf.Amount, true)

// 	posMargin := pos.CalcPosMargin(slf.Amount, slf.Price, trade.Fee) // 仓位保证金
// 	// 需要的基础保证金
// 	if posMargin.GreaterThan(lockAmount) { // 保证金余额不够
// 		err = fmt.Errorf("trialAvailableAmount not enough, trialAvailableAmount:%s, posMargin:%s", lockAmount, posMargin)
// 		return
// 	}
// 	if lockAmount.LessThan(trade.UnfrozenMargin) {
// 		err = fmt.Errorf("trialAvailableAmount not enough, lockAmount:%s, unfrozenMargin:%s", lockAmount, trade.UnfrozenMargin)
// 		return
// 	}
// 	trialList.UnlockAmount(trade.UnfrozenMargin)  // 解锁体验金
// 	trialList.AddOpenAmount(trade.UnfrozenMargin) // 添加保证金金额
// 	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), trade.UnfrozenMargin, true) // 解冻
// 	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), trade.UnfrozenMargin, true) // 解冻
// 	pos.UpdateTrialMargin(posMargin)

// 	// 生成流水
// 	bParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
// 	res := payload.BalanceRes{
// 		AssetLogs:      make([]*repository.MqCmsAsset, 0),
// 		BillAssetLogs:  make([]repository.BillAssetSync, 0),
// 		TrialAssetLogs: make([]*entity.TrialAsset, 0),
// 	}
// 	feeRes := trialList.FeeAmount(trade.Fee)
// 	asset.AddTrialBalance(bParam.Currency, trade.Fee.Neg())
// 	asset.AddTrialConsume(bParam.Currency, trade.Fee)
// 	slf.OnTrialChange(bParam, &res, feeRes, bParam.Currency)
// 	feeDetails := make([]payload.FeeDetail, 0)
// 	for _, detail := range res.AssetLogs {
// 		feeDetails = append(feeDetails, payload.FeeDetail{
// 			Currency: detail.Currency,
// 			Amount:   detail.Amount,
// 			Price:    pCache.SpotURate(detail.Currency),
// 		})
// 	}

// 	reply.AssetLogs = append(reply.AssetLogs, res.AssetLogs...)
// 	reply.TrialLogs = append(reply.TrialLogs, trialList...) // 更新体验金持久化
// 	reply.BillAssetLogs = append(reply.BillAssetLogs, res.BillAssetLogs...)

// 	asset.TrialLongPos = *pos
// 	// update asset cache
// 	err = slf.UpdateTrialLongPos(asset)
// 	if err != nil {
// 		msg := fmt.Sprintf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
// 		logrus.Errorf("revert hmset hash: %s with err: %v", slf.HashKey, err)
// 		return reply, errors.New(msg)
// 	}

// 	// 统计用户开仓数据
// 	if !domain.RobotUsers.HasKey(slf.UID) {
// 		go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
// 			UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: pos.OpenTime,
// 			HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: pos.PosId,
// 		})
// 	}

// 	reply.Reply = payload.Reply{
// 		UID:          asset.UID,
// 		PosId:        pos.PosId,
// 		Pos:          pos.Pos,
// 		OpenPriceAvg: pos.OpenPriceAvg,
// 		PosSide:      pos.PosSide,
// 		FeeDetail:    feeDetails,
// 		HaveTrial:    1,
// 	}

// 	return
// }

// // CloseTrialLongPos 平体验金多仓
// func (slf *PosCache) CloseTrialLongPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (reply payload.PosReply, code domain.Code, err error) {
// 	pos := &asset.TrialLongPos
// 	if !pos.Isolated() {
// 		code = domain.TrialMarginModeErr
// 		err = errors.New("not isolated margin")
// 		return
// 	}

// 	haveTrial := 1
// 	profitReal := pos.CalcProfitReal(slf.Price, slf.Amount)
// 	compareProfitReal := profitReal
// 	compareFee := trade.Fee
// 	billProfitReal := profitReal

// 	originPosAmount := pos.Pos
// 	originPosIsolateMargin := pos.IsolatedMargin
// 	leftMargin := pos.IsolatedMargin.Sub(trade.Fee)
// 	pos.UpdatePos(slf.Amount.Neg(), false)
// 	if pos.Pos.Sign() < 0 {
// 		logrus.Errorf("asset.TrialLongPos.Pos is negative after closing long, %+v", asset.TrialLongPos.Pos)
// 		return reply, domain.Code251111, errors.New("pos is negative after closing long")
// 	}
// 	if pos.Leverage < 1 {
// 		logrus.Errorf("asset.LongPos.Leverage < 1, %+v", asset.LongPos.Leverage)
// 		return reply, domain.Code251114, errors.New("251114 long pos is leverage < 1")
// 	}
// 	trialList := asset.TrialDetail.Gets(pos.AwardIds...)

// 	// TODO: 此处是不是要考虑已经爆仓了的情况
// 	pos.ProfitReal = pos.ProfitReal.Add(profitReal)
// 	fmt.Println("=======profitReal", "uid", pos.UID, "profitReal", profitReal)

// 	// 如果发生了亏损，统计亏损
// 	if billProfitReal.IsNegative() {
// 		pos.UpdateTrialMargin(billProfitReal)
// 		lossRes, profitTotal := trialList.LossAmount(profitReal.Abs())
// 		asset.TrialLoss.Add(slf.Quote, profitReal.Abs())
// 		fmt.Println("===========lossRes", lossRes, pos.IsolatedMargin, "profitTotal", profitTotal, "profitReal", profitReal)

// 		tProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeRealTrial, trade)
// 		tProfitBillList := payload.BalanceRes{
// 			AssetLogs:      make([]*repository.MqCmsAsset, 0),
// 			BillAssetLogs:  make([]repository.BillAssetSync, 0),
// 			TrialAssetLogs: make([]*entity.TrialAsset, 0),
// 		}
// 		asset.AddTrialBalance(slf.Quote, profitReal)
// 		asset.AddTrialConsume(slf.Quote, profitReal.Abs()) // 亏损
// 		slf.OnTrialChange(tProfitParam, &tProfitBillList, lossRes, tProfitParam.Currency)
// 		reply.AssetLogs = append(reply.AssetLogs, tProfitBillList.AssetLogs...)
// 		reply.BillAssetLogs = append(reply.BillAssetLogs, tProfitBillList.BillAssetLogs...)

// 		leftMargin = leftMargin.Add(billProfitReal)

// 		billProfitReal = decimal.Zero

// 		// profitReal = profitTotal
// 	} else if billProfitReal.IsPositive() { // 如果是盈利
// 		bProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeRealTrial, trade)
// 		if domain.IsAgentUser(trade.UserType) {
// 			profitBillList := slf.BalanceAdd(bProfitParam, asset, pCache, true)
// 			reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
// 			reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
// 		} else { // 增加抵扣金
// 			billSyncs := slf.OnDeductProfitReal(trade.UID, slf.Quote, billProfitReal)
// 			reply.BillAssetLogs = append(reply.BillAssetLogs, billSyncs...)

// 			// 发送抵扣金, 如果失败废了怎么办?
// 			go match.Service.DeliverAwardHandler(trade.UID, "", slf.Base+"-"+slf.Quote, billProfitReal)
// 		}
// 	}

// 	// 计算手续费流水
// 	bFeeParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
// 	var feeBillList payload.BalanceRes
// 	feeRes := trialList.FeeAmount(trade.Fee)
// 	asset.AddTrialBalance(bFeeParam.Currency, trade.Fee.Neg())
// 	asset.AddTrialConsume(bFeeParam.Currency, trade.Fee)
// 	slf.OnTrialChange(bFeeParam, &feeBillList, feeRes, bFeeParam.Currency)
// 	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
// 	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
// 	// 手续费明细
// 	feeDetails := make([]payload.FeeDetail, 0)
// 	for _, detail := range feeBillList.AssetLogs {
// 		feeDetails = append(feeDetails, payload.FeeDetail{
// 			Currency: detail.Currency,
// 			Amount:   detail.Amount,
// 			Price:    pCache.SpotURate(detail.Currency),
// 		})
// 	}

// 	// 暗成交，需要主动扣减posAvailable
// 	if slf.TradeType == domain.TradeTypeDirect {
// 		if pos.PosAvailable.LessThan(slf.Amount) {
// 			log.Printf("dark deal close long pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
// 				slf.UID, pos.PosAvailable, slf.Amount)
// 			return reply, domain.Code251119, errors.New("dark deal close long pos pos available less than slf.amount")
// 		}
// 		pos.PosAvailable = pos.PosAvailable.Sub(slf.Amount)
// 	}
// 	pos.LiquidationType = trade.LiquidationType

// 	// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
// 	closePosMargin := slf.Amount.Div(originPosAmount).Mul(leftMargin).Truncate(domain.CurrencyPrecision)
// 	subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
// 	fmt.Println("=======closePosMargin", closePosMargin, "subDeltaAmount", subDeltaAmount, "originPosIsolateMargin", originPosIsolateMargin, "originPosAmount", originPosAmount, "slf.Amount", slf.Amount, "trade.Fee", trade.Fee)
// 	if closePosMargin.IsPositive() {
// 		pos.UpdateTrialMargin(closePosMargin.Neg())
// 		trialList.SubOpenAmount(closePosMargin.Abs()) // 上面已经扣除了手续费
// 	}

// 	if pos.LiquidationType != domain.LiquidationTypeBurst {
// 		// 平穿逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
// 		if pos.IsolatedMargin.IsNegative() {
// 			bankruptAmount := pos.IsolatedMargin.Abs()

// 			minAmount := decimal.Min(bankruptAmount, trialList.GetOpenAmount())
// 			if minAmount.IsPositive() {
// 				trialList.SubOpenAmount(minAmount) // 亏损金额
// 			}

// 			bankruptAmount = bankruptAmount.Sub(minAmount)
// 			if bankruptAmount.IsPositive() {
// 				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs, true)

// 				pos.IsolatedMargin = decimal.Zero
// 				pos.TrialMargin = decimal.Zero
// 				asset.AddTrialBalance(slf.Quote, bankruptAmount) // TODO: 是否需要添加余额
// 				pos.Subsidy = pos.Subsidy.Add(bankruptAmount)
// 				logrus.Infof("close long pos bankrupt uid:%s amount:%s", slf.UID, bankruptAmount)
// 			}
// 		}
// 	}

// 	// 平仓后如果仓位归0, 生成历史持仓记录
// 	if pos.Pos.Sign() <= 0 {
// 		if trialList.GetOpenAmount().IsPositive() { // 如果还有剩余
// 			trialList.SubOpenAmount(trialList.GetOpenAmount())
// 			fmt.Println("==========sub open amount", trialList.GetOpenAmount())
// 		}
// 		reply.ClearPos = *pos
// 		pos.Clear()
// 		trialList.AddOpenCount(1)

// 		// 回收一次性体验金流水
// 		recoveryAmount := trialList.Recovery()
// 		if recoveryAmount.IsPositive() { // 如果回收金额存在
// 			asset.TrialRecovery.Add(slf.Quote, recoveryAmount)
// 			asset.AddTrialBalance(slf.Quote, recoveryAmount.Neg())
// 			asset.AddTrialConsume(slf.Quote, recoveryAmount) // 回收体验金
// 			fmt.Println("=====Recovery", asset.UID, recoveryAmount, asset.TrialBalance)
// 		}
// 	}

// 	// 更新体验券
// 	reply.TrialLogs = append(reply.TrialLogs, trialList...)
// 	asset.TrialLongPos = *pos
// 	err = slf.UpdateTrialLongPos(asset)
// 	if err != nil {
// 		log.Printf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
// 		return reply, domain.Code251109, err
// 	}

// 	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
// 	if !domain.RobotUsers.HasKey(slf.UID) {
// 		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
// 		if tempProfit.Sign() > 0 {
// 			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
// 				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
// 				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
// 			})
// 		} else {
// 			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
// 				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
// 				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
// 			})
// 		}
// 	}

// 	go func() {
// 		// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
// 		if pos.Pos.Sign() <= 0 {
// 			RemoveRivalScore(pos.UID, pos.ContractCode, pos.PosSide, true) // 删除对手方评分
// 			match.Service.ConditionCancel2(pos.UID, slf.Base, slf.Quote, pos.PosSide, domain.Sell, pos.MarginMode, int32(domain.CancelTypeClosePosAll), 0, 1)
// 		}
// 	}()

// 	reply.Reply = payload.Reply{
// 		UID:          asset.UID,
// 		PosId:        pos.PosId,
// 		Pos:          pos.Pos,
// 		OpenPriceAvg: pos.OpenPriceAvg,
// 		PosSide:      pos.PosSide,
// 		ProfitReal:   profitReal,
// 		FeeDetail:    feeDetails,
// 		HaveTrial:    haveTrial,
// 	}

// 	return reply, http.StatusOK, nil
// }

// func (slf *PosCache) OpenTrialShortPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (reply payload.PosReply, err error) {
// 	pos := &asset.TrialShortPos
// 	// 体验金只能开逐仓
// 	if trade.MarginMode != int32(domain.MarginModeIsolated) {
// 		err = errors.New("OpenTrialShortPos MarginMode must be isolated")
// 		return
// 	}

// 	if pos.HasPosStatus(domain.PosStatusNone, domain.PosStatusEnd) {
// 		pos.NewPos(trade.UID, trade.UserType, trade.MarginMode, trade.Leverage)
// 	}

// 	// 获取体验金, 可以放到外面判断错误
// 	trialList := asset.TrialDetail.Gets(trade.AwardIds...)
// 	if len(trialList) == 0 {
// 		log.Printf("OpenTrialShortPos trialList is empty, uid:%s, awardOpIds:%v", slf.UID, trade.AwardIds)
// 		return reply, errors.New("trialList is empty")
// 	}
// 	lockAmount := trialList.GetLockAmount()

// 	// 计算总的保证金
// 	pos.AddAwardOpIds(trade.AwardIds...)
// 	pos.UpdateOpenPriceAvg(slf.Amount, slf.Price)
// 	pos.UpdatePos(slf.Amount, true)

// 	posMargin := pos.CalcPosMargin(slf.Amount, slf.Price, trade.Fee) // 仓位保证金
// 	// 需要的基础保证金
// 	if posMargin.GreaterThan(lockAmount) { // 保证金余额不够
// 		err = fmt.Errorf("trialAvailableAmount not enough, trialAvailableAmount:%s, posMargin:%s", lockAmount, posMargin)
// 		return
// 	}

// 	if lockAmount.LessThan(trade.UnfrozenMargin) {
// 		err = fmt.Errorf("trialAvailableAmount not enough, lockAmount:%s, unfrozenMargin:%s", lockAmount, trade.UnfrozenMargin)
// 		return
// 	}
// 	trialList.UnlockAmount(trade.UnfrozenMargin)  // 解锁体验金
// 	trialList.AddOpenAmount(trade.UnfrozenMargin) // 添加保证金金额
// 	// asset.DecrFrozen(util.PosSide(trade.Side, trade.Offset, int32(asset.PositionMode)), slf.ContractCode(), trade.UnfrozenMargin, true) // 解冻
// 	asset.DecrFrozen(trade.PosSide, slf.ContractCode(), trade.UnfrozenMargin, true) // 解冻
// 	pos.UpdateTrialMargin(posMargin)

// 	// 生成流水
// 	bParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
// 	res := payload.BalanceRes{
// 		AssetLogs:      make([]*repository.MqCmsAsset, 0),
// 		BillAssetLogs:  make([]repository.BillAssetSync, 0),
// 		TrialAssetLogs: make([]*entity.TrialAsset, 0),
// 	}
// 	feeRes := trialList.FeeAmount(trade.Fee)
// 	asset.AddTrialBalance(bParam.Currency, trade.Fee.Neg())
// 	asset.AddTrialConsume(bParam.Currency, trade.Fee)
// 	slf.OnTrialChange(bParam, &res, feeRes, bParam.Currency)
// 	feeDetails := make([]payload.FeeDetail, 0)
// 	for _, detail := range res.AssetLogs {
// 		feeDetails = append(feeDetails, payload.FeeDetail{
// 			Currency: detail.Currency,
// 			Amount:   detail.Amount,
// 			Price:    pCache.SpotURate(detail.Currency),
// 		})
// 	}

// 	reply.AssetLogs = append(reply.AssetLogs, res.AssetLogs...)
// 	reply.TrialLogs = append(reply.TrialLogs, trialList...) // 更新体验金持久化
// 	reply.BillAssetLogs = append(reply.BillAssetLogs, res.BillAssetLogs...)

// 	asset.TrialShortPos = *pos
// 	// update asset cache
// 	err = slf.UpdateTrialShortPos(asset)
// 	if err != nil {
// 		msg := fmt.Sprintf("hmset user %v %v err: %v", slf.UID, slf.Quote, err)
// 		logrus.Errorf("revert hmset hash: %s with err: %v", slf.HashKey, err)
// 		return reply, errors.New(msg)
// 	}

// 	// 统计用户开仓数据
// 	if !domain.RobotUsers.HasKey(slf.UID) {
// 		go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
// 			UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: pos.OpenTime,
// 			HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: pos.PosId,
// 		})
// 	}

// 	reply.Reply = payload.Reply{
// 		UID:          asset.UID,
// 		PosId:        pos.PosId,
// 		Pos:          pos.Pos,
// 		OpenPriceAvg: pos.OpenPriceAvg,
// 		PosSide:      pos.PosSide,
// 		FeeDetail:    feeDetails,
// 		HaveTrial:    1,
// 	}

// 	return
// }

// // CloseTrialShortPos 平体验金空仓
// func (slf *PosCache) CloseTrialShortPos(asset *repository.AssetSwap, pCache *price.PCache, trade payload.Trade) (reply payload.PosReply, code domain.Code, err error) {
// 	pos := &asset.TrialShortPos
// 	if !pos.Isolated() {
// 		code = domain.TrialMarginModeErr
// 		err = errors.New("not isolated margin")
// 		return
// 	}

// 	haveTrial := 1
// 	profitReal := pos.CalcProfitReal(slf.Price, slf.Amount)
// 	compareProfitReal := profitReal
// 	compareFee := trade.Fee
// 	billProfitReal := profitReal

// 	originPosAmount := pos.Pos
// 	originPosIsolateMargin := pos.IsolatedMargin
// 	leftMargin := pos.IsolatedMargin.Sub(trade.Fee)
// 	pos.UpdatePos(slf.Amount.Neg(), false)
// 	if pos.Pos.Sign() < 0 {
// 		logrus.Errorf("asset.TrialShortPos.Pos is negative after closing long, %+v", asset.TrialShortPos.Pos)
// 		return reply, domain.Code251111, errors.New("pos is negative after closing long")
// 	}
// 	if pos.Leverage < 1 {
// 		logrus.Errorf("asset.LongPos.Leverage < 1, %+v", asset.LongPos.Leverage)
// 		return reply, domain.Code251114, errors.New("251114 long pos is leverage < 1")
// 	}
// 	trialList := asset.TrialDetail.Gets(pos.AwardIds...)

// 	// TODO: 此处是不是要考虑已经爆仓了的情况
// 	pos.ProfitReal = pos.ProfitReal.Add(profitReal)
// 	fmt.Println("=======profitReal", "uid", pos.UID, "profitReal", profitReal)

// 	// 如果发生了亏损，统计亏损
// 	if billProfitReal.IsNegative() {
// 		pos.UpdateTrialMargin(billProfitReal)
// 		lossRes, profitTotal := trialList.LossAmount(profitReal.Abs())
// 		asset.TrialLoss.Add(slf.Quote, profitReal.Abs())
// 		fmt.Println("===========lossRes", lossRes, pos.IsolatedMargin, "profitTotal", profitTotal, "profitReal", profitReal)

// 		tProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeRealTrial, trade)
// 		tProfitBillList := payload.BalanceRes{
// 			AssetLogs:      make([]*repository.MqCmsAsset, 0),
// 			BillAssetLogs:  make([]repository.BillAssetSync, 0),
// 			TrialAssetLogs: make([]*entity.TrialAsset, 0),
// 		}
// 		asset.AddTrialBalance(slf.Quote, profitReal)
// 		asset.AddTrialConsume(slf.Quote, profitReal.Abs()) // 亏损
// 		slf.OnTrialChange(tProfitParam, &tProfitBillList, lossRes, tProfitParam.Currency)
// 		reply.AssetLogs = append(reply.AssetLogs, tProfitBillList.AssetLogs...)
// 		reply.BillAssetLogs = append(reply.BillAssetLogs, tProfitBillList.BillAssetLogs...)
// 		leftMargin = leftMargin.Add(billProfitReal)
// 		billProfitReal = decimal.Zero

// 		// profitReal = profitTotal
// 	} else if billProfitReal.IsPositive() { // 如果是盈利
// 		bProfitParam := slf.TradeBalanceParam(billProfitReal, domain.BillTypeRealTrial, trade)
// 		if domain.IsAgentUser(trade.UserType) {
// 			profitBillList := slf.BalanceAdd(bProfitParam, asset, pCache, true)
// 			reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
// 			reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
// 		} else { // 增加抵扣金
// 			billSyncs := slf.OnDeductProfitReal(trade.UID, slf.Quote, billProfitReal)
// 			reply.BillAssetLogs = append(reply.BillAssetLogs, billSyncs...)

// 			// 发送抵扣金, 如果失败废了怎么办?
// 			go match.Service.DeliverAwardHandler(trade.UID, "", slf.Base+"-"+slf.Quote, billProfitReal)
// 		}
// 	}

// 	// 计算手续费流水
// 	bFeeParam := slf.TradeBalanceParam(trade.Fee.Neg(), domain.BillTypeFee, trade)
// 	var feeBillList payload.BalanceRes
// 	feeRes := trialList.FeeAmount(trade.Fee)
// 	asset.AddTrialBalance(bFeeParam.Currency, trade.Fee.Neg())
// 	asset.AddTrialConsume(bFeeParam.Currency, trade.Fee)
// 	slf.OnTrialChange(bFeeParam, &feeBillList, feeRes, bFeeParam.Currency)
// 	reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
// 	reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
// 	// 手续费明细
// 	feeDetails := make([]payload.FeeDetail, 0)
// 	for _, detail := range feeBillList.AssetLogs {
// 		feeDetails = append(feeDetails, payload.FeeDetail{
// 			Currency: detail.Currency,
// 			Amount:   detail.Amount,
// 			Price:    pCache.SpotURate(detail.Currency),
// 		})
// 	}

// 	// 暗成交，需要主动扣减posAvailable
// 	if slf.TradeType == domain.TradeTypeDirect {
// 		if pos.PosAvailable.LessThan(slf.Amount) {
// 			log.Printf("dark deal close long pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
// 				slf.UID, pos.PosAvailable, slf.Amount)
// 			return reply, domain.Code251119, errors.New("dark deal close long pos pos available less than slf.amount")
// 		}
// 		pos.PosAvailable = pos.PosAvailable.Sub(slf.Amount)
// 	}
// 	pos.LiquidationType = trade.LiquidationType

// 	// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
// 	closePosMargin := slf.Amount.Div(originPosAmount).Mul(leftMargin).Truncate(domain.CurrencyPrecision)
// 	subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
// 	fmt.Println("=======closePosMargin", closePosMargin, "subDeltaAmount", subDeltaAmount, "originPosIsolateMargin", originPosIsolateMargin, "originPosAmount", originPosAmount, "slf.Amount", slf.Amount, "trade.Fee", trade.Fee)
// 	if closePosMargin.IsPositive() {
// 		pos.UpdateTrialMargin(closePosMargin.Neg())
// 		trialList.SubOpenAmount(closePosMargin.Abs()) // 上面已经扣除了手续费
// 	}

// 	if pos.LiquidationType != domain.LiquidationTypeBurst {
// 		// 平穿逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
// 		if pos.IsolatedMargin.IsNegative() {
// 			bankruptAmount := pos.IsolatedMargin.Abs()

// 			minAmount := decimal.Min(bankruptAmount, trialList.GetOpenAmount())
// 			if minAmount.IsPositive() {
// 				trialList.SubOpenAmount(minAmount) // 亏损金额
// 			}

// 			bankruptAmount = bankruptAmount.Sub(minAmount)
// 			if bankruptAmount.IsPositive() {
// 				slf.bankruptSubsidy(bankruptAmount, &reply.AssetLogs, &reply.BillAssetLogs, true)

// 				pos.IsolatedMargin = decimal.Zero
// 				pos.TrialMargin = decimal.Zero
// 				asset.AddTrialBalance(slf.Quote, bankruptAmount) // TODO: 是否需要添加余额
// 				pos.Subsidy = pos.Subsidy.Add(bankruptAmount)
// 				logrus.Infof("close long pos bankrupt uid:%s amount:%s", slf.UID, bankruptAmount)
// 			}
// 		}
// 	}

// 	// 平仓后如果仓位归0, 生成历史持仓记录
// 	if pos.Pos.Sign() <= 0 {
// 		if trialList.GetOpenAmount().IsPositive() { // 如果还有剩余
// 			trialList.SubOpenAmount(trialList.GetOpenAmount())
// 			fmt.Println("==========sub open amount", trialList.GetOpenAmount())
// 		}
// 		trialList.AddOpenCount(1)
// 		reply.ClearPos = *pos
// 		pos.Clear()

// 		recoveryAmount := trialList.Recovery()
// 		if recoveryAmount.IsPositive() { // 如果回收金额存在
// 			asset.TrialRecovery.Add(slf.Quote, recoveryAmount)
// 			asset.AddTrialBalance(slf.Quote, recoveryAmount.Neg())
// 			asset.AddTrialConsume(slf.Quote, recoveryAmount) // 回收体验金
// 			fmt.Println("=====Recovery", asset.UID, recoveryAmount, asset.TrialBalance)
// 		}
// 	}

// 	// 更新体验券
// 	reply.TrialLogs = append(reply.TrialLogs, trialList...)
// 	asset.TrialShortPos = *pos
// 	err = slf.UpdateTrialShortPos(asset)
// 	if err != nil {
// 		log.Printf("hmset hash %s err: %v", slf.HashKey, err)
// 		return reply, domain.Code251108, err
// 	}

// 	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
// 	if !domain.RobotUsers.HasKey(slf.UID) {
// 		tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
// 		if tempProfit.Sign() > 0 {
// 			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
// 				UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
// 				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
// 			})
// 		} else {
// 			go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
// 				UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
// 				TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
// 			})
// 		}
// 	}

// 	go func() {
// 		// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
// 		if pos.Pos.Sign() <= 0 {
// 			RemoveRivalScore(pos.UID, pos.ContractCode, pos.PosSide, true) // 删除对手方评分
// 			match.Service.ConditionCancel2(pos.UID, slf.Base, slf.Quote, pos.PosSide, domain.Buy, pos.MarginMode, int32(domain.CancelTypeClosePosAll), 0, 1)
// 		}
// 	}()

// 	reply.Reply = payload.Reply{
// 		UID:          asset.UID,
// 		PosId:        pos.PosId,
// 		Pos:          pos.Pos,
// 		OpenPriceAvg: pos.OpenPriceAvg,
// 		PosSide:      pos.PosSide,
// 		ProfitReal:   profitReal,
// 		FeeDetail:    feeDetails,
// 		HaveTrial:    haveTrial,
// 	}

// 	return reply, http.StatusOK, nil
// }

// func (slf *PosCache) OnDeductProfitReal(uid, currency string, amount decimal.Decimal) (billSyncs []repository.BillAssetSync) {
// 	currency = strings.ToUpper(currency)
// 	billSync := repository.BillAssetSync{
// 		BillAsset: entity.BillAsset{
// 			UID:          uid,
// 			BillId:       util.GenerateId(),
// 			ContractCode: slf.ContractCode(),
// 			OperateId:    slf.TradeId,
// 			Currency:     currency,
// 			BillType:     domain.BillTypeDeductProfitReal,
// 			Amount:       amount,
// 			OperateTime:  time.Now().UnixNano(),
// 		},
// 	}
// 	billSyncs = append(billSyncs, billSync)

// 	billSync.BillType = domain.BillTypeDeductAdd
// 	billSync.ContractCode = ""
// 	billSyncs = append(billSyncs, billSync)

// 	return
// }

// // OnTrialChange 体验金变动
// func (slf *PosCache) OnTrialChange(p payload.BalanceUpdate, res *payload.BalanceRes, amounts map[string]decimal.Decimal, currency string) {
// 	if len(amounts) <= 0 {
// 		return
// 	}
// 	for awardOpId, amount := range amounts {
// 		slf.OnTrialBalanceAdd(p, res, amount, currency)
// 		if len(res.BillAssetLogs) > 0 { // 修改体验金ID
// 			last := len(res.BillAssetLogs) - 1
// 			res.BillAssetLogs[last].OperateId = awardOpId
// 		}
// 	}
// }
