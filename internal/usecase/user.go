package usecase

import (
	"context"
	"net/http"

	"futures-asset/configs"
	"futures-asset/internal/delivery/event/message"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
	cfg "yt.com/backend/common.git/config"
)

// UserUseCaseParam 用户用例参数
type UserUseCaseParam struct {
	dig.In

	Config          *cfg.Config[configs.Config] `name:"config"`
	UserRepo        repository.UserRepository
	ProducerUsecase usecase.ProducerUseCase
}

// UserUseCase 用户用例实现
type UserUseCase struct {
	config          *cfg.Config[configs.Config]
	userRepo        repository.UserRepository
	producerUseCase usecase.ProducerUseCase
}

// NewUserUseCase 创建用户用例实例
func NewUserUseCase(param UserUseCaseParam) usecase.UserUseCase {
	return &UserUseCase{
		config:          param.Config,
		userRepo:        param.UserRepo,
		producerUseCase: param.ProducerUsecase,
	}
}

// AdjustCross implements usecase.UserUseCase.
func (use *UserUseCase) AdjustCross(ctx context.Context, p *repository.AdjustCrossParam) ([]string, error) {
	return use.userRepo.AdjustCross(ctx, p)
}

// UpdatePositionMode implements usecase.UserUseCase.
func (use *UserUseCase) UpdatePositionMode(ctx context.Context, p *repository.HoldModeParam) (domain.Code, error) {
	code, err := use.userRepo.UpdatePositionMode(ctx, p)
	if err != nil {
		return code, err
	}

	// 推送配置变化
	assetData := &commonpb.KafkaAccountPositionMode{
		Uid:          p.UID,
		PositionMode: int32(p.PositionMode),
	}
	// 推送 position_mode 配置
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountPositionMode]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountPositionMode]]{
				Uid: assetData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountPositionMode]{
					Event: commonpb.AccountEvent_TYPE_POSITION_MODE,
					Data:  assetData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}

	return http.StatusOK, nil
}

// UpdateAssetMode implements usecase.UserUseCase.
func (use *UserUseCase) UpdateAssetMode(ctx context.Context, p *repository.ChangeJoinMarginParam) (domain.Code, error) {
	code, err := use.userRepo.UpdateAssetMode(ctx, p)
	if err != nil {
		return code, err
	}

	// 推送配置变化
	assetData := &commonpb.KafkaAccountAssetMode{
		Uid:       p.UID,
		AssetMode: int32(p.AssetMode),
	}
	// 推送 asset_mode 配置
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountAssetMode]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountAssetMode]]{
				Uid: assetData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountAssetMode]{
					Event: commonpb.AccountEvent_TYPE_ASSET_MODE,
					Data:  assetData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}

	return http.StatusOK, nil
}

// AdjustLeverage implements usecase.UserUseCase.
func (use *UserUseCase) AdjustLeverage(ctx context.Context, p *repository.LeverageAdjust) (domain.Code, *repository.Leverage, error) {
	code, leverage, err := use.userRepo.AdjustLeverage(ctx, p)
	if err != nil {
		return code, leverage, err
	}

	// 推送配置变化
	leverageData := &commonpb.KafkaAccountLeverage{
		Uid:        p.UID,
		Symbol:     p.Symbol,
		Leverage:   int32(leverage.Leverage),
		LLeverage:  int32(leverage.LLeverage),
		SLeverage:  int32(leverage.SLeverage),
		BLeverage:  int32(leverage.BLeverage),
		MarginMode: int32(leverage.MarginMode),
	}
	// 推送 leverage 配置
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountLeverage]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountLeverage]]{
				Uid: leverageData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountLeverage]{
					Event: commonpb.AccountEvent_TYPE_LEVERAGE,
					Data:  leverageData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}

	return http.StatusOK, leverage, nil
}

// AdjustLeverageMargin implements usecase.UserUseCase.
// func (use *UserUseCase) AdjustLeverageMargin(ctx context.Context, p *repository.LeverageMarginAdAdjust) (domain.Code, error) {
// return use.userRepo.AdjustLeverageMargin(ctx, p)
// }

// AdjustMargin implements usecase.UserUseCase.
func (use *UserUseCase) AdjustMargin(ctx context.Context, p *repository.MarginParam) (domain.Code, error) {
	return use.userRepo.AdjustMargin(ctx, p)
}

// UpdateMarginMode implements usecase.UserUseCase.
func (use *UserUseCase) UpdateMarginMode(ctx context.Context, p *repository.MarginAdjust) (domain.Code, error) {
	code, leverage, err := use.userRepo.UpdateMarginMode(ctx, p)
	if err != nil {
		return code, err
	}

	// 推送配置变化
	leverageData := &commonpb.KafkaAccountLeverage{
		Uid:        p.UID,
		Symbol:     p.Symbol,
		Leverage:   int32(leverage.Leverage),
		LLeverage:  int32(leverage.LLeverage),
		SLeverage:  int32(leverage.SLeverage),
		BLeverage:  int32(leverage.BLeverage),
		MarginMode: int32(leverage.MarginMode),
	}
	// 推送 leverage 配置
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountLeverage]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountLeverage]]{
				Uid: leverageData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountLeverage]{
					Event: commonpb.AccountEvent_TYPE_LEVERAGE,
					Data:  leverageData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}

	return code, nil
}

// UpdateOrderConfig 修改订单配置
func (use *UserUseCase) UpdateOrderConfig(ctx context.Context, p *repository.ChangeOrderConfigParam) (domain.Code, error) {
	return use.userRepo.ChangeOrderConfig(ctx, p)
}

// UpdateOrderConfirm 修改下单确认
func (use *UserUseCase) UpdateOrderConfirm(ctx context.Context, p *repository.ChangeOrderConfirmParam) (domain.Code, error) {
	return use.userRepo.UpdateOrderConfirm(ctx, p)
}

// LoadJoinMargin 获取参与保证金
func (use *UserUseCase) LoadJoinMargin(ctx context.Context, param *repository.CommonParam) (repository.JoinMarginRes, error) {
	return use.userRepo.LoadJoinMargin(ctx, param)
}

// GetUserAsset 获取用户资产
func (use *UserUseCase) GetUserAsset(ctx context.Context, param *repository.ReqAsset) (domain.Code, []repository.Asset) {
	return use.userRepo.GetUserAsset(ctx, param)
}

// SwapInit 初始化合约账户
func (use *UserUseCase) SwapInit(ctx context.Context, param *repository.UIDParam) domain.Code {
	return use.userRepo.SwapInit(ctx, param)
}

// UserLeverage 获取杠杆倍数
func (use *UserUseCase) UserLeverage(ctx context.Context, param *repository.CommonParam) ([]*repository.Leverage, error) {
	return use.userRepo.UserLeverage(ctx, param)
}

func (use *UserUseCase) UserBasicConfig(ctx context.Context, param *repository.UIDParam) (*repository.UserBasicConfig, error) {
	return use.userRepo.UserBasicConfig(ctx, param)
}

// GetUserOpenCloseTimes 获取开仓次数
func (use *UserUseCase) GetUserOpenCloseTimes(ctx context.Context, param *repository.OpenCloseTimesReq) (*repository.OpenCloseTimesRes, error) {
	return use.userRepo.GetUserOpenCloseTimes(ctx, param)
}

// GetUserStatistics 获取用户统计
func (use *UserUseCase) GetUserStatistics(ctx context.Context, param *repository.UserStatistics) ([]repository.UserStatisticsReply, error) {
	return use.userRepo.GetUserStatistics(ctx, param)
}
