package usecase

import (
	"context"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/util"

	"github.com/go-redsync/redsync"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// OptionDemoUseCaseParam 期权用例参数
type OptionDemoUseCaseParam struct {
	dig.In

	Config     *cfg.Config[configs.Config] `name:"config"`
	Redsync    *redsync.Redsync            `name:"rs"`
	OptionRepo repository.OptionRepository
}

type OptionDemoUseCase struct {
	config *cfg.Config[configs.Config]
	rs     *redsync.Redsync

	optionRepo repository.OptionRepository
}

// NewOptionDemoUseCase 创建期权用例实例
func NewOptionDemoUseCase(param OptionDemoUseCaseParam) usecase.OptionDemoUseCase {
	return &OptionDemoUseCase{
		config: param.Config,
		rs:     param.Redsync,

		optionRepo: param.OptionRepo,
	}
}

// DemoAsset implements usecase.OptionDemoUseCase.
func (use *OptionDemoUseCase) DemoAsset(ctx context.Context, uid string) (decimal.Decimal, error) {
	return use.optionRepo.GetDemoAsset(ctx, uid)
}

// Exercise implements usecase.OptionDemoUseCase.
func (use *OptionDemoUseCase) Exercise(ctx context.Context, param *usecase.ExerciseParam) ([]*usecase.OptionTradeReply, error) {
	var (
		reply       = make([]*usecase.OptionTradeReply, 0)
		profitReal  = decimal.Zero
		totalCancel = decimal.Zero
		billLogs    = make([]repository.BillOptionSync, 0) // 前台用户账单
		optionKey   = util.OptionSubKey(param.Base, param.Quote, param.OptionId)
		unprocessed = make([]repository.Option, 0)
	)

	// 记录流水
	userOptions, err := use.optionRepo.LoadUserOption(ctx, param.UID, param.OptionType)
	if err != nil {
		return nil, err
	}
	for _, option := range userOptions {
		processed := false
		for _, order := range param.Orders {
			if option.OrderId == order.OrderId && order.State == domain.OptionStateEexercised {
				if order.ProfitReal.Sign() > 0 {
					profitReal = profitReal.Add(order.ProfitReal)
				}

				realBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, int(domain.BillTypeOptionProfitReal), util.AccountType(order.AccountType), param.OptionType, order.ProfitReal, order.Premium, param.OptionId, order.OrderId)
				billLogs = append(billLogs, realBill)
				reply = append(reply, &usecase.OptionTradeReply{
					OrderId: order.OrderId,
				})
				processed = true

				break
			}
			if option.OrderId == order.OrderId && order.State == domain.OptionStateCanceled {
				totalCancel = totalCancel.Add(order.Premium).Add(order.Fee)

				realBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, int(domain.BillTypeOptionPremiumReturn), util.AccountType(order.AccountType), param.OptionType, order.Premium, decimal.Zero, param.OptionId, order.OrderId)
				logBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, int(domain.BillTypeOptionFeeReturn), util.AccountType(order.AccountType), param.OptionType, order.Fee, decimal.Zero, param.OptionId, order.OrderId)
				billLogs = append(billLogs, realBill, logBill)
				reply = append(reply, &usecase.OptionTradeReply{
					OrderId: order.OrderId,
				})
				processed = true

				break
			}
		}
		if !processed {
			unprocessed = append(unprocessed, option)
		}
	}

	// 锁定用户合约账户
	userMutex := use.rs.NewMutex(domain.MutexOptionDemoLock+param.UID, redsync.SetExpiry(time.Second*30))
	if userMutex.Lock() != nil {
		return reply, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	// 获取资产
	available, err := use.optionRepo.GetDemoAsset(ctx, param.UID)
	if err != nil {
		return reply, err
	}
	err = use.optionRepo.UpdateDemoAsset(ctx, param.UID, available.Add(profitReal).Add(totalCancel))
	if err != nil {
		return reply, err
	}

	if len(unprocessed) == 0 {
		// 删除用户期权记录
		if err = use.optionRepo.RemoveOption(ctx, param.UID, param.OptionType); err != nil {
			logrus.Error("[RemoveOption] call redis HDel err:%s, hashkey:%s optionKey:%s", err, use.optionRepo.OptionHashKey(param.UID, param.OptionType), optionKey)
		}
	} else {
		// 更新用户期权
		if err = use.optionRepo.UpdateOption(ctx, param.UID, param.OptionType, optionKey, unprocessed); err != nil {
			logrus.Error("[UpdateOption] call redis HSet err:%s, hashkey:%s optionKey:%s", err, use.optionRepo.OptionHashKey(param.UID, param.OptionType), optionKey)
		}
	}
	// TODO 同步流水到data-center

	// go func() {
	// 	if len(billLogs) > 0 {
	// 		coupling.AddOptionBills(client, billLogs...)
	// 	}
	// }()

	return reply, nil
}

// Trade implements usecase.OptionDemoUseCase.
func (use *OptionDemoUseCase) Trade(ctx context.Context, param *usecase.OptionTradeParam) ([]*usecase.OptionTradeReply, error) {
	var (
		reply       = make([]*usecase.OptionTradeReply, 0)
		totalAmount = decimal.Zero
		options     = make(map[string]repository.Option)
	)
	// 计算总划转金额
	for _, order := range param.Orders {
		totalAmount = totalAmount.Add(order.Premium).Add(order.Fee)
		subKey := util.OptionSubKey(param.Base, param.Quote, order.OptionId)
		options[subKey] = repository.Option{
			OrderId: order.OrderId,
			Premium: order.Premium,
			Fee:     order.Fee,
		}
		reply = append(reply, &usecase.OptionTradeReply{
			OrderId: order.OrderId,
		})
	}

	// 锁定用户合约账户
	userMutex := use.rs.NewMutex(domain.MutexOptionDemoLock+param.UID, redsync.SetExpiry(time.Second*30))
	if userMutex.Lock() != nil {
		return reply, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	// 计算账户可用金额
	available, err := use.optionRepo.GetDemoAsset(ctx, param.UID)
	if err != nil {
		return reply, err
	}
	if available.LessThan(totalAmount) {
		return reply, errors.New(param.UID + " demo account available insufficient funds")
	}
	err = use.optionRepo.UpdateDemoAsset(ctx, param.UID, available.Sub(totalAmount))
	if err != nil {
		return reply, errors.Wrap(err, "decr balance from user: "+param.UID)
	}
	// 缓存期权记录
	if err := use.optionRepo.UpdateMultiOption(ctx, param.UID, param.OptionType, options); err != nil {
		// revert contract asset
		revokeErr := use.optionRepo.UpdateDemoAsset(ctx, param.UID, available)
		if revokeErr != nil {
			logrus.Error("revert user asset in option demo trade err: %s", revokeErr.Error())
		}

		return reply, err
	}
	// TODO 同步账单到DB和ES

	// go func() {
	// 	_, billLogs := modelutil.NewOptionRecord(param)
	// 	if len(billLogs) > 0 {
	// 		client := redislib.Redis()
	// 		coupling.AddOptionBills(client, billLogs...)
	// 	}
	// }()

	return reply, nil
}
