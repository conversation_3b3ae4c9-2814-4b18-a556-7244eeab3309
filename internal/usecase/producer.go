package usecase

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	client "futures-asset/internal/libs/kafka"

	"github.com/IBM/sarama"
	"go.uber.org/dig"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
	wspb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/kafka"
)

type ProducerUseCaseParam struct {
	dig.In

	Config  *cfg.Config[configs.Config] `name:"config"`
	Cluster *client.Cluster             `name:"kafka"`
}

type ProducerUseCase struct {
	cli     *kafka.Producer
	cluster *client.Cluster

	topic map[string]string
}

// NewProducerUseCase 建立kafka Producer
func NewProducerUseCase(param ProducerUseCaseParam) (usecase.ProducerUseCase, error) {
	producer, err := kafka.NewProducer(&kafka.Config{
		ClientID:          param.Config.Kafka.ClientID,
		Brokers:           param.Config.Kafka.Brokers,
		ChannelBufferSize: param.Config.Kafka.ChannelBufferSize,
		SASL: kafka.SASL{
			Enable:    param.Config.Kafka.SASL.Enable,
			Mechanism: sarama.SASLMechanism(param.Config.Kafka.SASL.Mechanism),
			User:      param.Config.Kafka.SASL.User,
			Password:  param.Config.Kafka.SASL.Password,
		},
	}, kafka.NewDefaultProducerConfig())
	if err != nil {
		return nil, fmt.Errorf("kafka.NewConsumerGroupUseCase error: %w", err)
	}

	var topics []string

	// Websocket 消息推送
	if name, ok := param.Config.Kafka.Producer.Topic[domain.AccountTopic]; ok {
		topics = append(topics, name)
	}

	if name, ok := param.Config.Kafka.Producer.Topic[domain.AccountBillTopic]; ok {
		topics = append(topics, name)
	}

	if name, ok := param.Config.Kafka.Producer.Topic[domain.AccountPosTopic]; ok {
		topics = append(topics, name)
	}

	// 数据异步落库
	if name, ok := param.Config.Kafka.Producer.Topic[domain.AssetsBillTopic]; ok {
		topics = append(topics, name)
	}

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.OptionBillTopic]; ok {
	// 	topics = append(topics, name)
	// }

	if name, ok := param.Config.Kafka.Producer.Topic[domain.FundingFeeTopic]; ok {
		topics = append(topics, name)
	}

	if name, ok := param.Config.Kafka.Producer.Topic[domain.FundingRateTopic]; ok {
		topics = append(topics, name)
	}

	if name, ok := param.Config.Kafka.Producer.Topic[domain.FundingChangeTopic]; ok {
		topics = append(topics, name)
	}

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetTopic]; ok {
	// 	topics = append(topics, name)
	// }

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetAddTopic]; ok {
	// 	topics = append(topics, name)
	// }

	// if name, ok := param.Config.Kafka.Producer.Topic[domain.TrialAssetRecycleTopic]; ok {
	// 	topics = append(topics, name)
	// }

	if err := param.Cluster.CreateTopics(param.Config.Kafka.Brokers, topics); err != nil {
		return nil, err
	}

	mTopics := make(map[string]string)
	for name, topic := range param.Config.Kafka.Producer.Topic {
		mTopics[name] = strings.Split(topic, ":")[0]
	}

	return &ProducerUseCase{
		cli:     producer,
		cluster: param.Cluster,
		topic:   mTopics,
	}, nil
}

type ProducerError struct {
	ID        string
	Partition int32
	Offset    int64
	Err       error
}

func (e ProducerError) Error() string {
	return fmt.Sprintf("send notify messages partition: %d offset: %d error: %s", e.Partition, e.Offset, e.Err.Error())
}

// SendAssetsBill 异步消费落库-用户账单流水
func (use *ProducerUseCase) SendAssetsBill(message []*entity.BillAsset) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendAssetsBill json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.AssetsBillTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

// SendFundingFee 异步消费落库-资金费用推送
func (use *ProducerUseCase) SendFundingFee(message []*entity.LogFundingFee) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendFundingFee json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.FundingFeeTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

// SendFundingRate 异步消费落库-资金费率推送
func (use *ProducerUseCase) SendFundingRate(message *commonpb.KafkaFundingRate) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendFundingRate json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.FundingRateTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

func (use *ProducerUseCase) SendFundingChange(message []*commonpb.KafkaFundingChange) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendFundingChange json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.FundingChangeTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

// SendAccount Websocket推送-账户相关推送
func (use *ProducerUseCase) SendAccount(message any) error {
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("SendAccount json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.AccountTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

// SendAccountBill Websocket推送-账单流水
func (use *ProducerUseCase) SendAccountBill(message *entity.BillAsset) error {
	bill := &wspb.KafkaBill{
		Uid:            message.UID,
		BillId:         message.BillID,
		Symbol:         message.Symbol,
		Currency:       message.Currency,
		BillType:       int32(message.BillType),
		FlowType:       int32(message.FlowType),
		Amount:         message.Amount.String(),
		Balance:        message.Balance.String(),
		FundingRate:    message.FundingRate.String(),
		MarkPrice:      message.MarkPrice.String(),
		RefId:          message.RefID,
		FromPair:       message.FromPair,
		ToPair:         message.ToPair,
		FromWalletType: int32(message.FromWalletType),
		ToWalletType:   int32(message.ToWalletType),
		RecycleId:      message.RecycleID,
		CreateTime:     message.CreateTime,
	}

	body, err := json.Marshal(bill)
	if err != nil {
		return fmt.Errorf("SendAccountBill json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.AccountBillTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}

// SendAccountPosition Websocket推送-用户仓位推送
func (use *ProducerUseCase) SendAccountPosition(message *repository.PosSwap) error {
	pos := &wspb.KafkaPosition{
		Uid:             message.UID,
		Currency:        message.Currency,
		IsolatedMargin:  message.IsolatedMargin.String(),
		TrialMargin:     message.TrialMargin.String(),
		Leverage:        int32(message.Leverage),
		Liquidation:     message.Liquidation.String(),
		MarginMode:      message.MarginMode,
		OpenPriceAvg:    message.OpenPriceAvg.String(),
		OpenTime:        message.OpenTime,
		Pos:             message.Pos.String(),
		PosAvailable:    message.PosAvailable.String(),
		PosSide:         message.PosSide,
		PosValue:        message.PosValue.String(),
		ProfitReal:      message.ProfitReal.String(),
		ProfitUnreal:    message.ProfitUnreal.String(),
		Symbol:          message.Symbol,
		PosId:           message.PosId,
		UserType:        message.UserType,
		PosStatus:       int32(message.PosStatus),
		Subsidy:         message.Subsidy.String(),
		RebornId:        message.RebornId,
		LiquidationType: int32(message.LiquidationType),
		AwardIds:        message.AwardIds,
		MarkPrice:       message.MarkPrice.String(),
	}

	body, err := json.Marshal(pos)
	if err != nil {
		return fmt.Errorf("SendAccountPosition json.Marshal error: %w", err)
	}

	partition, offset, err := use.cli.SendMessage(kafka.ProducerMessage{
		ID:    strconv.FormatInt(time.Now().UnixNano(), 10),
		Topic: use.topic[domain.AccountPosTopic],
		Value: body,
	})
	if err != nil {
		return &ProducerError{
			Partition: partition,
			Offset:    offset,
			Err:       err,
		}
	}

	return nil
}
