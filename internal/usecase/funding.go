package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	ytSnowflake "futures-asset/internal/libs/snowflake"
	"futures-asset/util"

	"github.com/bwmarrin/snowflake"
	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
	cfg "yt.com/backend/common.git/config"
)

// FundingUseCaseParam 资产用例参数
type FundingUseCaseParam struct {
	dig.In

	RS     *redsync.Redsync            `name:"rs"`
	Config *cfg.Config[configs.Config] `name:"config"`
	Redis  *redis.ClusterClient        `name:"redis-cluster"`

	ProducerUseCase usecase.ProducerUseCase
	FundingRepo     repository.FundingRepository
	PriceRepo       repository.PriceRepository
	FormulaRepo     repository.FormulaRepository
	BurstRepo       repository.BurstRepository
	CacheRepo       repository.CacheRepository
	SettingRepo     repository.SettingRepository
	PositionUseCase usecase.PositionUseCase
	PositionRepo    repository.PositionRepository
	TrialRepo       repository.TrialRepository
	AssetRepo       repository.AssetRepository
}

// FundingUseCase 资产用例实现
type FundingUseCase struct {
	rs     *redsync.Redsync
	config *cfg.Config[configs.Config]
	gen    *snowflake.Node
	rdb    *redis.ClusterClient

	producerUseCase usecase.ProducerUseCase
	fundingRepo     repository.FundingRepository
	priceRepo       repository.PriceRepository
	formulaRepo     repository.FormulaRepository
	burstRepo       repository.BurstRepository
	cacheRepo       repository.CacheRepository
	settingRepo     repository.SettingRepository
	positionUseCase usecase.PositionUseCase
	positionRepo    repository.PositionRepository
	trialRepo       repository.TrialRepository
	assetRepo       repository.AssetRepository
}

// NewFundingUseCase 创建资产用例实例
func NewFundingUseCase(param FundingUseCaseParam) usecase.FundingUseCase {
	n, err := ytSnowflake.New()
	if err != nil {
		panic(fmt.Sprintf("NewOrderUseCase error: %v", err))
	}

	return &FundingUseCase{
		rs:     param.RS,
		config: param.Config,
		gen:    n,
		rdb:    param.Redis,

		producerUseCase: param.ProducerUseCase,
		fundingRepo:     param.FundingRepo,
		priceRepo:       param.PriceRepo,
		formulaRepo:     param.FormulaRepo,
		burstRepo:       param.BurstRepo,
		cacheRepo:       param.CacheRepo,
		settingRepo:     param.SettingRepo,
		positionUseCase: param.PositionUseCase,
		positionRepo:    param.PositionRepo,
		trialRepo:       param.TrialRepo,
		assetRepo:       param.AssetRepo,
	}
}

// AllFundData implements usecase.FundingUseCase.
func (use *FundingUseCase) AllFundData(ctx context.Context, wg *sync.WaitGroup) (r []repository.FundingRate) {
	return use.fundingRepo.AllFundData(ctx, wg)
}

// FundRateAll implements usecase.FundingUseCase.
func (use *FundingUseCase) FundRateAll(ctx context.Context) (repository.FundingRateAll, error) {
	return use.fundingRepo.FundRateAll(ctx)
}

// FundingFeeList implements usecase.FundingUseCase.
func (use *FundingUseCase) FundingFeeList(ctx context.Context, req *repository.FundingFeeListParam) (repository.FundingFeeList, error) {
	return use.fundingRepo.FundingFeeList(ctx, req)
}

// GetFundRate implements usecase.FundingUseCase.
func (use *FundingUseCase) GetFundRate(ctx context.Context, symbol string) (repository.FundingRate, error) {
	return use.fundingRepo.GetFundRate(ctx, symbol)
}

// GetFundRateList implements usecase.FundingUseCase.
func (use *FundingUseCase) GetFundRateList(ctx context.Context, req *repository.FundRateParam) (repository.FundRateReply, error) {
	return use.fundingRepo.GetFundRateList(ctx, req)
}

// LastFundMap implements usecase.FundingUseCase.
func (use *FundingUseCase) LastFundMap(ctx context.Context, wg *sync.WaitGroup) (r map[string]entity.LogFundingRate) {
	return use.fundingRepo.LastFundMap(ctx, wg)
}

func (use *FundingUseCase) GetBaseNum(ctx context.Context, key string) int {
	return use.fundingRepo.GetBaseNum(ctx, key)
}

func (use *FundingUseCase) SetBaseNum(ctx context.Context, key string, value int) error {
	return use.fundingRepo.SetBaseNum(ctx, key, value)
}

func (use *FundingUseCase) MakeFundRate(ctx context.Context, symbol string, contractConfig *repository.ContractPair) {
	indexPrice := use.priceRepo.GetIndexPrice(ctx, symbol)
	use.fundingRepo.SetOneFundRate(ctx, domain.GetFundRateRedisKey(symbol), symbol, indexPrice, contractConfig.Interest)
	// 计算并留存溢价指数
	_ = use.formulaRepo.PremiumIndex(ctx, symbol, *contractConfig)
	// 计算并留存资金费率
	fundingRate := use.fundingRepo.FundRate(ctx, symbol, *contractConfig)

	nowTime := time.Now()

	// 推送资金费率
	assetData := &commonpb.KafkaFundingRate{
		Symbol:      symbol,
		FundingRate: fundingRate.String(),
		FundingTime: nowTime.Unix(),
	}
	if err := use.producerUseCase.SendFundingRate(assetData); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}

	// 每天 0，8，16 点结算
	if nowTime.Hour()%8 == 0 {
		if nowTime.Minute() == 0 {
			logFundRate := &entity.LogFundingRate{
				Symbol:      symbol,
				FundingRate: fundingRate,
				MarkPrice:   use.priceRepo.GetMarkPrice(ctx, symbol),
				IndexPrice:  indexPrice,
				FundingTime: nowTime.Unix(),
			}

			logrus.WithFields(logrus.Fields{
				"fundRate":   fundingRate,
				"indexPrice": indexPrice,
				"symbol":     symbol,
				"nowTime":    nowTime.Unix(),
			}).Info("SettlementAll")
			if !fundingRate.IsZero() {
				use.SettlementFundingFee(fundingRate, symbol, contractConfig)
			}

			err := use.fundingRepo.CreateFundingRate(ctx, logFundRate)
			if err != nil {
				logrus.WithFields(logrus.Fields{
					"err":         err,
					"logFundRate": logFundRate,
				}).Error("fundingRepo.CreateFundingRate")
			}
		}
	}
}

// SettlementFundingFee 资金费率为正:净多出资金费用 资金费用为负:净空仓方向出资金费用
func (use *FundingUseCase) SettlementFundingFee(fundingRate decimal.Decimal, symbol string, contractConfig *repository.ContractPair) {
	usersKey := domain.GetAllUserPosKey(symbol)
	users, err := use.rdb.HGetAll(context.Background(), usersKey).Result()
	if err != nil {
		logrus.Error("usersKey HGetAll", usersKey, "error:", err)
		return
	}

	logrus.Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundingRate))

	// 出资金费率的用户
	fundCostOutUsers := make([]repository.SettlementPos, 0)
	// 收资金费率的用户
	fundCostInUsers := make([]repository.SettlementPos, 0)

	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
	totalOutPos := decimal.Zero
	totalInPos := decimal.Zero
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero
	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
			continue
		}

		// 全仓爆仓中不能转入
		checkBurst := repository.CheckBurstParam{
			Symbol:     userPosSwap.Symbol,
			UID:        userPosSwap.UID,
			MarginMode: futuresassetpb.MarginMode(userPosSwap.MarginMode),
		}
		isBursting, err := use.burstRepo.IsBursting(context.Background(), checkBurst)
		if err != nil {
			logrus.Error("usersKey HGetAll", usersKey, "error:", err)
			continue
		}
		if isBursting {
			logrus.Info("user in burst.userPos:%+v", userPosSwap)
			continue
		}

		base, quote := util.BaseQuote(userPosSwap.Symbol)
		settlementPos := repository.SettlementPos{
			UserHoldPos: userPosSwap,
			Base:        base,
			Quote:       quote,
		}
		// 单向持仓需要特殊处理
		if !userPosSwap.BothPos.IsZero() && userPosSwap.BothTrialMargin.LessThanOrEqual(decimal.Zero) {
			settlementPos.PosSide = domain.BothPos
			settlementPos.Pos = userPosSwap.BothPos.Abs()
			if userPosSwap.BothPos.IsPositive() {
				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
				use.processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
			} else if userPosSwap.BothPos.IsNegative() {
				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
				use.processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
			}
		} else {
			// Isolated true 是逐仓 逐仓和全仓处理方式不一样
			if domain.MarginMode(userPosSwap.MarginMode) == domain.MarginModeIsolated {
				if userPosSwap.LongPos.IsPositive() && userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
					settlementPos.PosSide = domain.LongPos
					settlementPos.Pos = userPosSwap.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					use.processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
				}
				if userPosSwap.ShortPos.IsPositive() && userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
					settlementPos.PosSide = domain.ShortPos
					settlementPos.Pos = userPosSwap.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					use.processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
				}
			} else {
				realPos := decimal.Zero
				longPos := decimal.Zero
				shortPos := decimal.Zero
				if userPosSwap.LongTrialMargin.LessThanOrEqual(decimal.Zero) {
					longPos = userPosSwap.LongPos
				}
				if userPosSwap.ShortTrialMargin.LessThanOrEqual(decimal.Zero) {
					shortPos = userPosSwap.ShortPos
				}
				realPos = longPos.Sub(shortPos)
				settlementPos.Pos = realPos.Abs()
				if realPos.IsPositive() {
					settlementPos.PosSide = domain.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					use.processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
				} else if realPos.IsNegative() {
					settlementPos.PosSide = domain.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					use.processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundingRate, &settlementPos)
				}
			}
		}
	}

	// 体验金仓位计算
	trialFundCostOutUsers, trialFundCostInUsers, trialTotalOutPos, trialTotalInPos, trialTotalLongPos, trialTotalShortPos := use.settlementTrialPos(fundingRate, symbol, contractConfig)
	if len(trialFundCostOutUsers) > 0 {
		fundCostOutUsers = append(fundCostOutUsers, trialFundCostOutUsers...)
	}
	if len(trialFundCostInUsers) > 0 {
		fundCostInUsers = append(fundCostInUsers, trialFundCostInUsers...)
	}
	totalOutPos = totalOutPos.Add(trialTotalOutPos)
	totalInPos = totalInPos.Add(trialTotalInPos)
	totalLongPos = totalLongPos.Add(trialTotalLongPos)
	totalShortPos = totalShortPos.Add(trialTotalShortPos)

	if !totalInPos.Equal(totalOutPos) {
		logrus.Info(fmt.Sprintf("%s pos static err.totalInPos != totalOutPos.totalInPos:%+v, totalOutPos:%+v", symbol, totalInPos, totalOutPos))
		logrus.Info(fmt.Sprintf("******* %s totalLongPos：%+v totalShortPos:%+v", symbol, totalLongPos, totalShortPos))
		logrus.Info(fmt.Sprintf("******* %s fundCostOutUsers:%+v", symbol, fundCostOutUsers))
		logrus.Info(fmt.Sprintf("******* %s fundCostInUsers:%+v", symbol, fundCostInUsers))
	}

	// 总共实收资金费用
	totalRealGet := decimal.Zero
	for _, userPos := range fundCostOutUsers {
		if userPos.IsTrial {
			// 体验金仓位收取资金费率
			totalRealGet = totalRealGet.Add(use.PtGetTrialFundCost(context.Background(), &userPos, fundingRate))
		} else {
			// 真实仓位收取资金费率
			totalRealGet = totalRealGet.Add(use.PtGetFundCost(context.Background(), &userPos, fundingRate))
		}
	}

	logrus.Info(fmt.Sprintf("Pt put fund cost.totalRealGet: %+v,totalInPos: %+v", totalRealGet, totalInPos))
	for _, userPos := range fundCostInUsers {
		if userPos.IsTrial {
			// 体验金仓位发放资金费率
			use.PtPutTrialFundCost(context.Background(), &userPos, fundingRate, totalRealGet, totalInPos)
		} else {
			// 真实仓位收发放金费率
			use.PtPutFundCost(context.Background(), &userPos, fundingRate, totalRealGet, totalInPos)
		}
	}
}

func (use *FundingUseCase) settlementTrialPos(fundRate decimal.Decimal, contractCode string, contractConfig *repository.ContractPair) ([]repository.SettlementPos, []repository.SettlementPos, decimal.Decimal, decimal.Decimal, decimal.Decimal, decimal.Decimal) {
	// 出资金费率的用户
	fundCostOutUsers := make([]repository.SettlementPos, 0)
	// 收资金费率的用户
	fundCostInUsers := make([]repository.SettlementPos, 0)

	// totalInPos：收资金费用用户的总仓位  totalOutPos：出资金费用用户的总仓位
	totalOutPos := decimal.Zero
	totalInPos := decimal.Zero
	totalLongPos := decimal.Zero
	totalShortPos := decimal.Zero

	usersKey := domain.GetAllUserTrialPosKey(contractCode)
	users, err := use.rdb.HGetAll(context.Background(), usersKey).Result()
	if err != nil {
		logrus.Error("userTrialKey HGetAll", usersKey, "error:", err)
		return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
	}
	logrus.Info(fmt.Sprintf("Settlement All.contractConfig: %+v, fundRate: %+v", contractConfig, fundRate))

	for _, userPos := range users {
		userPosSwap := repository.UserHoldPos{}
		err = json.Unmarshal([]byte(userPos), &userPosSwap)
		if err != nil {
			logrus.Error(fmt.Sprintf("unmarshal err:%+v, userPos:%s", err, userPos))
			continue
		}
		if userPosSwap.LongPos.IsZero() && userPosSwap.ShortPos.IsZero() && userPosSwap.BothPos.IsZero() {
			continue
		}

		// 全仓爆仓中不能转入
		checkBurst := repository.CheckBurstParam{
			Symbol:     userPosSwap.Symbol,
			UID:        userPosSwap.UID,
			MarginMode: futuresassetpb.MarginMode(userPosSwap.MarginMode),
		}
		isBursting, err := use.burstRepo.IsBursting(context.Background(), checkBurst)
		if err != nil {
			logrus.Error("usersKey HGetAll", usersKey, "error:", err)
			continue
		}
		if isBursting {
			continue
		}

		base, quote := util.BaseQuote(userPosSwap.Symbol)
		settlementPos := repository.SettlementPos{
			UserHoldPos: userPosSwap,
			Base:        base,
			Quote:       quote,
			IsTrial:     true,
		}
		// 因为全仓不能开保证金仓位,所直接略过
		// 单向持仓需要特殊处理
		if !userPosSwap.BothPos.IsZero() {
			settlementPos.PosSide = domain.BothPos
			settlementPos.Pos = userPosSwap.BothPos.Abs()
			if userPosSwap.BothPos.IsPositive() {
				totalLongPos = totalLongPos.Add(userPosSwap.BothPos.Abs())
				use.processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			} else if userPosSwap.BothPos.IsNegative() {
				totalShortPos = totalShortPos.Add(userPosSwap.BothPos.Abs())
				use.processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
			}
		} else {
			if domain.MarginMode(userPosSwap.MarginMode) == domain.MarginModeIsolated {
				if userPosSwap.LongPos.IsPositive() {
					settlementPos.PosSide = domain.LongPos
					settlementPos.Pos = userPosSwap.LongPos
					totalLongPos = totalLongPos.Add(userPosSwap.LongPos.Abs())
					use.processLongPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
				if userPosSwap.ShortPos.IsPositive() {
					settlementPos.PosSide = domain.ShortPos
					settlementPos.Pos = userPosSwap.ShortPos
					totalShortPos = totalShortPos.Add(userPosSwap.ShortPos.Abs())
					use.processShortPos(&fundCostOutUsers, &fundCostInUsers, &totalOutPos, &totalInPos, fundRate, &settlementPos)
				}
			}
		}
	}

	return fundCostOutUsers, fundCostInUsers, totalOutPos, totalInPos, totalLongPos, totalShortPos
}

func (use *FundingUseCase) processLongPos(fundCostOutUsers, fundCostInUsers *[]repository.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *repository.SettlementPos) {
	if fundRate.IsPositive() {
		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
	} else {
		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
		*totalInPos = totalInPos.Add(settlementPos.Pos)
	}
}

func (use *FundingUseCase) processShortPos(fundCostOutUsers, fundCostInUsers *[]repository.SettlementPos, totalOutPos, totalInPos *decimal.Decimal, fundRate decimal.Decimal, settlementPos *repository.SettlementPos) {
	if fundRate.IsPositive() {
		*fundCostInUsers = append(*fundCostInUsers, *settlementPos)
		*totalInPos = totalInPos.Add(settlementPos.Pos)
	} else {
		*fundCostOutUsers = append(*fundCostOutUsers, *settlementPos)
		*totalOutPos = totalOutPos.Add(settlementPos.Pos)
	}
}

// PtGetFundCost 用户支付资金费用给平台, 资金费用取绝对值进位
// 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
func (use *FundingUseCase) PtGetFundCost(ctx context.Context, tmpUserPos *repository.SettlementPos, fundRate decimal.Decimal) decimal.Decimal {
	settleId := use.gen.Generate().String()
	markPrice := use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
	fundCost := tmpUserPos.Pos.Mul(markPrice).Mul(fundRate).Abs()
	fundCost, _ = util.RoundCeil(fundCost, domain.CurrencyPrecision)

	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtGetFundCost lock err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userAsset, err := use.cacheRepo.Load(ctx, tmpUserPos.UID, tmpUserPos.Symbol(), tmpUserPos.Currency)
	if err != nil {
		return decimal.Zero
	}

	bParamList := make([]repository.BalanceUpdate, 0)
	bIsolatedParamList := make([]repository.BalanceUpdate, 0)
	isTrialPos := false

	if tmpUserPos.MarginMode == 0 {
		if tmpUserPos.IsTrial {
			switch tmpUserPos.PosSide {
			case domain.LongPos:
				tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
			case domain.ShortPos:
				tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
			case domain.BothPos:
				tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
			default:
				return decimal.Zero
			}
		} else {
			switch tmpUserPos.PosSide {
			case domain.LongPos:
				tmpUserPos.MarginMode = userAsset.LongPos.MarginMode
				isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)
			case domain.ShortPos:
				tmpUserPos.MarginMode = userAsset.ShortPos.MarginMode
				isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)
			case domain.BothPos:
				tmpUserPos.MarginMode = userAsset.BothPos.MarginMode
				isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)
			default:
				return decimal.Zero
			}
		}
	}

	if isTrialPos {
		return decimal.Zero
	}

	switch domain.MarginMode(tmpUserPos.MarginMode) {
	case domain.MarginModeCross:
		// 获取quote 总资产折合 假如qBalance < fundCost, fundCost修改
		rate := decimal.NewFromInt(1)
		qBalance := userAsset.CBalance(tmpUserPos.Quote)
		holdMargin, _, _, _, err := use.assetRepo.TotalCrossMaintainMargin(ctx, userAsset, tmpUserPos.Symbol())
		if err != nil {
			logrus.Info(fmt.Sprintf("PtGetFundCost TotalCrossMaintainMargin error: %v", err))
			return decimal.Zero
		}
		if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
			rate = use.priceRepo.SpotRate(ctx, tmpUserPos.Quote, domain.CurrencyUSDT)
			if rate.LessThanOrEqual(decimal.Zero) {
				logrus.Info(fmt.Sprintln("PtGetFundCost rate error:", rate))
				return decimal.Zero
			}
			uBalance, err := use.assetRepo.TotalJoinBalance(ctx, userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("PtGetFundCost TotalBalance err.userCache:%+v,tmpUserPos:%+v,fundRate:%+v, err:%v", userAsset, tmpUserPos, fundRate, err))
				return decimal.Zero
			}
			uCost := fundCost.Mul(rate)
			if uBalance.GreaterThan(uCost.Add(holdMargin)) {
				totalCollect := uCost
				bParam := repository.BalanceUpdate{
					Symbol:      tmpUserPos.Symbol(),
					Currency:    domain.CurrencyUSDT,
					OrderId:     settleId,
					UID:         tmpUserPos.UID,
					Amount:      totalCollect.Neg(),
					OperateTime: time.Now().UnixNano(),
					OperateType: domain.BillTypeFunding,
				}
				bParamList = append(bParamList, bParam)
			}
		} else {
			if qBalance.GreaterThan(fundCost.Add(holdMargin)) {
				bParam := repository.BalanceUpdate{
					Symbol:      tmpUserPos.Symbol(),
					Currency:    tmpUserPos.Currency,
					OrderId:     settleId,
					UID:         tmpUserPos.UID,
					Amount:      fundCost.Neg(),
					OperateTime: time.Now().UnixNano(),
					OperateType: domain.BillTypeFunding,
				}
				bParamList = append(bParamList, bParam)
			}
		}

	case domain.MarginModeIsolated:
		bParam := repository.BalanceUpdate{
			Symbol:      tmpUserPos.Symbol(),
			Currency:    tmpUserPos.Currency,
			OrderId:     settleId,
			UID:         tmpUserPos.UID,
			Amount:      fundCost.Neg(),
			OperateTime: time.Now().UnixNano(),
			OperateType: domain.BillTypeFunding,
		}
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			posValue := userAsset.LongPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(ctx, tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(userAsset.LongPos.Symbol, userAsset.LongPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.LongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Info("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Sub(fundCost)
				if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		case domain.ShortPos:
			posValue := userAsset.ShortPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(ctx, tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(userAsset.ShortPos.Symbol, userAsset.ShortPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.ShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Info("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Sub(fundCost)
				if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		case domain.BothPos:
			posValue := userAsset.BothPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(ctx, tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(userAsset.BothPos.Symbol, userAsset.BothPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.BothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Info("PtGetFundCost", userAsset.UID, "cost", fundCost)
				userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Sub(fundCost)
				if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		default:
			logrus.Info(fmt.Sprintf("PtGetFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
			return decimal.Zero

		}

	default:
		logrus.Info(fmt.Sprintf("PtGetFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))

		return decimal.Zero
	}

	if len(bParamList) > 0 {
		for _, param := range bParamList {
			if !param.Amount.IsZero() {
				balanceRes := use.BalanceAdd(ctx, param, userAsset, true)

				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
					balanceRes.BillAssetLogs[i].FundingRate = fundRate
				}

				err = use.cacheRepo.UpdatePosAndAsset(ctx, userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,userAsset:%+v", err, tmpUserPos, fundRate, userAsset))
					return decimal.Zero
				}
				use.CreateFundingFee(ctx, tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol()))
				logrus.Println("PtGetFundCost CreateFundingFee", userAsset.UID, fundRate)

				// // TODO 推送 资金费用 账单
				// go func() {
				// 	redis := redislib.Redis()
				// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				// }()
				// // 账本统计
				// go func(p parameter.BalanceUpdate) {
				// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
				// 	if err != nil {
				// 		loglib.GetLog().Errorf("PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
				// 		return
				// 	}
				// 	if p.Amount.IsPositive() {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				// 	} else {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				// 	}
				// }(param)
			}
		}
	}

	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if !param.Amount.IsZero() {
				balanceRes := repository.BalanceRes{
					AssetLogs:     make([]*repository.MqCmsAsset, 0),
					BillAssetLogs: make([]repository.BillAssetSync, 0),
				}
				use.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
					balanceRes.BillAssetLogs[i].FundingRate = fundRate
				}

				err = use.cacheRepo.UpdatePosAndAsset(ctx, userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,userAsset:%+v", err, tmpUserPos, fundRate, userAsset))
					return decimal.Zero
				}
				use.CreateFundingFee(ctx, tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol()))

				logrus.Println("PtGetFundCost CreateFundingFee", userAsset.UID, fundRate)

				// // TODO 推送 资金费用 账单
				// go func() {
				// 	redis := redislib.Redis()
				// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				// }()
				// // 账本统计
				// go func(p parameter.BalanceUpdate) {
				// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
				// 	if err != nil {
				// 		loglib.GetLog().Errorf("2 PtGetFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
				// 		return
				// 	}
				// 	if p.Amount.IsPositive() {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				// 	} else {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				// 	}
				// }(param)
			}
		}
	}

	return fundCost
}

// PtPutFundCost 平台支付资金费用给用户 资金费用取绝对值截取
func (use *FundingUseCase) PtPutFundCost(ctx context.Context, tmpUserPos *repository.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal) decimal.Decimal {
	settleId := use.gen.Generate().String()
	fundCost := tmpUserPos.Pos.Mul(use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())).Mul(fundRate).Abs().Truncate(domain.CurrencyPrecision)
	if !totalInPos.IsZero() {
		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(domain.CurrencyPrecision)
		// 有可能实际收取的不够，所以需要按比例重新计算下
		if receivableCost.Cmp(fundCost) < 0 {
			logrus.Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
			fundCost = receivableCost
		}
	}
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtPutFundCost lock err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userAsset, err := use.cacheRepo.Load(ctx, tmpUserPos.UID, tmpUserPos.Symbol(), tmpUserPos.Currency)
	if err != nil {
		return decimal.Zero
	}

	isTrialPos := false
	switch tmpUserPos.PosSide {
	case domain.LongPos:
		isTrialPos = userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero)

	case domain.ShortPos:
		isTrialPos = userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero)

	case domain.BothPos:
		isTrialPos = userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero)

	default:
		return decimal.Zero

	}

	if isTrialPos {
		return decimal.Zero
	}

	logrus.Println("PtPutFundCost", "UserId", userAsset.UID, "settleId", settleId, "fundCost", fundCost)

	bParamList := make([]repository.BalanceUpdate, 0)
	bIsolatedParamList := make([]repository.BalanceUpdate, 0)
	bParam := repository.BalanceUpdate{
		Symbol:      tmpUserPos.Symbol(),
		Currency:    tmpUserPos.Currency,
		OrderId:     settleId,
		UID:         userAsset.UID,
		Amount:      fundCost,
		OperateTime: time.Now().UnixNano(),
		OperateType: domain.BillTypeFunding,
	}
	if domain.MarginMode(tmpUserPos.MarginMode) == domain.MarginModeIsolated {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(fundCost)
			if userAsset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.LongPos.TrialMargin = userAsset.LongPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.ShortPos:
			userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(fundCost)
			if userAsset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.ShortPos.TrialMargin = userAsset.ShortPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.BothPos:
			userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(fundCost)
			if userAsset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.BothPos.TrialMargin = userAsset.BothPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		default:
			logrus.Info(fmt.Sprintf("PtPutFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
			return decimal.Zero

		}
		// userAsset.Used = userAsset.Used.Add(fundCost)
	} else {
		bParamList = append(bParamList, bParam)
	}

	logrus.Info(fmt.Sprintf("PtPutFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
	if len(bParamList) > 0 {
		for _, param := range bParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := use.BalanceAdd(ctx, param, userAsset, true)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
				balanceRes.BillAssetLogs[i].FundingRate = fundRate
			}
			err = use.cacheRepo.UpdatePosAndAsset(ctx, userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,userAsset:%+v", err, tmpUserPos, fundRate, userAsset))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					use.CreateFundingFee(ctx, tmpUserPos, *assetLog, userAsset, fundRate, use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol()))
					logrus.Println("PtPutFundCost CreateFundingFee", userAsset.UID, fundRate)
				}
			}
			// // TODO 推送 资金费用 账单
			// go func() {
			// 	redis := redislib.Redis()
			// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
			// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			// }()
			// // 账本统计
			// go func(p parameter.BalanceUpdate) {
			// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
			// 	if err != nil {
			// 		loglib.GetLog().Errorf("PtPutFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
			// 		return
			// 	}
			// 	if p.Amount.IsPositive() {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
			// 	} else {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
			// 	}
			// }(param)
		}
	}
	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := repository.BalanceRes{
				AssetLogs:     make([]*repository.MqCmsAsset, 0),
				BillAssetLogs: make([]repository.BillAssetSync, 0),
			}
			use.OnBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(context.Background(), tmpUserPos.Symbol())
				balanceRes.BillAssetLogs[i].FundingRate = fundRate
			}
			err = use.cacheRepo.UpdatePosAndAsset(context.Background(), userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdatePosAndAsset err:%+v, tmpUserPos:%+v,fundRate:%+v,userAsset:%+v", err, tmpUserPos, fundRate, userAsset))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					use.CreateFundingFee(context.Background(), tmpUserPos, *assetLog, userAsset, fundRate, use.priceRepo.GetMarkPrice(context.Background(), tmpUserPos.Symbol()))
					logrus.Println("PtPutFundCost CreateFundingFee", userAsset.UID, fundRate)
				}
			}
			// // TODO 推送 资金费用 账单
			// go func() {
			// 	redis := redislib.Redis()
			// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
			// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			// }()
			// go func(p parameter.BalanceUpdate) {
			// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
			// 	if err != nil {
			// 		loglib.GetLog().Errorf("2 PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
			// 		return
			// 	}
			// 	if p.Amount.IsPositive() {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
			// 	} else {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
			// 	}
			// }(param)
		}
	}

	return fundCost
}

// PtGetTrialFundCost 体验金用户支付资金费用给平台, 资金费用取绝对值进位
// 按照公式资金费用计算出来为负值，所以平台收的资金费用是公式计算出来的数值的绝对值并进位之后的结果
func (use *FundingUseCase) PtGetTrialFundCost(ctx context.Context, tmpUserPos *repository.SettlementPos, fundRate decimal.Decimal) decimal.Decimal {
	settleId := use.gen.Generate().String()
	fundCost := tmpUserPos.Pos.Mul(use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())).Mul(fundRate).Abs()
	fundCost, _ = util.RoundCeil(fundCost, domain.CurrencyPrecision)
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtGetTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userAsset, err := use.cacheRepo.Load(context.Background(), tmpUserPos.UID, tmpUserPos.Symbol(), tmpUserPos.Currency)
	if err != nil {
		return decimal.Zero
	}

	bIsolatedParamList := make([]repository.BalanceUpdate, 0)

	if tmpUserPos.MarginMode == 0 {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
		case domain.ShortPos:
			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
		case domain.BothPos:
			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
		default:
			return decimal.Zero
		}
	}

	switch domain.MarginMode(tmpUserPos.MarginMode) {
	case domain.MarginModeIsolated:
		bParam := repository.BalanceUpdate{
			Symbol:      tmpUserPos.Symbol(),
			Currency:    tmpUserPos.Currency,
			OrderId:     settleId,
			UID:         tmpUserPos.UID,
			Amount:      fundCost.Neg(),
			OperateTime: time.Now().UnixNano(),
			OperateType: domain.BillTypeFunding,
		}

		markPrice := use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			posValue := userAsset.TrialLongPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(ctx, tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialLongPos.Symbol, userAsset.TrialLongPos.UID, "PtGetTrialFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialLongPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}
		case domain.ShortPos:
			posValue := userAsset.TrialShortPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(ctx, tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialShortPos.Symbol, userAsset.TrialShortPos.UID, "PtGetFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialShortPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}

		case domain.BothPos:
			posValue := userAsset.TrialBothPos.CalcPosValue(markPrice)
			marginLevel, _, err := use.settingRepo.FetchMarginLevel(context.Background(), tmpUserPos.Symbol(), posValue)
			if err != nil {
				logrus.Error(0, userAsset.TrialBothPos.Symbol, userAsset.TrialBothPos.UID, "PtGetTrialFundCost FetchMarginLevel error:", err)
				return decimal.Zero
			}

			holdMargin := marginLevel.HoldingMarginRate.Mul(posValue)
			if userAsset.TrialBothPos.IsolatedMargin.GreaterThan(fundCost.Add(holdMargin)) {
				logrus.Println("PtGetTrialFundCost", userAsset.UID, "cost", fundCost)
				userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Sub(fundCost)
				if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
					userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Sub(fundCost)
				}
				bIsolatedParamList = append(bIsolatedParamList, bParam)
			}

		default:
			logrus.Info(fmt.Sprintf("PtGetTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
			return decimal.Zero

		}

	default:
		logrus.Info(fmt.Sprintf("PtGetTrialFundCost MarginMode err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))

		return decimal.Zero
	}

	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if !param.Amount.IsZero() {
				balanceRes := repository.BalanceRes{
					AssetLogs:     make([]*repository.MqCmsAsset, 0),
					BillAssetLogs: make([]repository.BillAssetSync, 0),
				}
				use.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
				for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
					balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
					balanceRes.BillAssetLogs[i].FundingRate = fundRate
				}

				err = use.trialRepo.UpdateTrialPos(ctx, tmpUserPos.Symbol(), userAsset)
				if err != nil {
					logrus.Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v,userAsset:%+v", err, tmpUserPos, fundRate, userAsset))
					return decimal.Zero
				}
				use.CreateFundingFee(ctx, tmpUserPos, *balanceRes.AssetLogs[0], userAsset, fundRate, use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol()))

				logrus.Println("PtGetTrialFundCost CreateFundingFee", userAsset.UID, fundRate)

				// // TODO 推送 资金费用 账单
				// go func() {
				// 	redis := redislib.Redis()
				// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
				// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
				// }()
				// // 账本记录
				// go func(p parameter.BalanceUpdate) {
				// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
				// 	if err != nil {
				// 		loglib.GetLog().Errorf("PtGetTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err)
				// 		return
				// 	}
				// 	if param.Amount.IsPositive() {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
				// 	} else {
				// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
				// 	}
				// }(param)
			}
		}
	}

	return fundCost
}

// PtPutTrialFundCost 平台支付资金费用给体验金用户 资金费用取绝对值截取
func (use *FundingUseCase) PtPutTrialFundCost(ctx context.Context, tmpUserPos *repository.SettlementPos, fundRate, totalRealGet, totalInPos decimal.Decimal) decimal.Decimal {
	settleId := use.gen.Generate().String()
	fundCost := tmpUserPos.Pos.Mul(use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())).Mul(fundRate).Abs().Truncate(domain.CurrencyPrecision)
	if !totalInPos.IsZero() {
		receivableCost := tmpUserPos.Pos.Div(totalInPos).Mul(totalRealGet).Abs().Truncate(domain.CurrencyPrecision)
		// 有可能实际收取的不够，所以需要按比例重新计算下
		if receivableCost.Cmp(fundCost) < 0 {
			logrus.Info(fmt.Sprintf("fund rate in receiv < real.receivableCost:%+v,fundCost:%+v,tmpUserPos:%+v", receivableCost, fundCost, tmpUserPos))
			fundCost = receivableCost
		}
	}
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+tmpUserPos.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		logrus.Info(fmt.Sprintf("PtPutTrialFundCost lock err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
		return decimal.Zero
	}
	defer userMutex.Unlock()

	userAsset, err := use.cacheRepo.Load(ctx, tmpUserPos.UID, tmpUserPos.Symbol(), tmpUserPos.Currency)
	if err != nil {
		return decimal.Zero
	}

	if tmpUserPos.MarginMode == 0 {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			tmpUserPos.MarginMode = userAsset.TrialLongPos.MarginMode
		case domain.ShortPos:
			tmpUserPos.MarginMode = userAsset.TrialShortPos.MarginMode
		case domain.BothPos:
			tmpUserPos.MarginMode = userAsset.TrialBothPos.MarginMode
		default:
			return decimal.Zero
		}
	}

	logrus.Println("PtPutTrialFundCost", "UserId", userAsset.UID, "settleId", settleId, "fundCost", fundCost)

	bIsolatedParamList := make([]repository.BalanceUpdate, 0)
	bParam := repository.BalanceUpdate{
		Symbol:      tmpUserPos.Symbol(),
		Currency:    tmpUserPos.Currency,
		OrderId:     settleId,
		UID:         tmpUserPos.UID,
		Amount:      fundCost,
		OperateTime: time.Now().UnixNano(),
		OperateType: domain.BillTypeFunding,
	}

	if domain.MarginMode(tmpUserPos.MarginMode) == domain.MarginModeIsolated {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			userAsset.TrialLongPos.IsolatedMargin = userAsset.TrialLongPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialLongPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialLongPos.TrialMargin = userAsset.TrialLongPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.ShortPos:
			userAsset.TrialShortPos.IsolatedMargin = userAsset.TrialShortPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialShortPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialShortPos.TrialMargin = userAsset.TrialShortPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		case domain.BothPos:
			userAsset.TrialBothPos.IsolatedMargin = userAsset.TrialBothPos.IsolatedMargin.Add(fundCost)
			if userAsset.TrialBothPos.TrialMargin.GreaterThan(decimal.Zero) {
				userAsset.TrialBothPos.TrialMargin = userAsset.TrialBothPos.TrialMargin.Add(fundCost)
			}
			bIsolatedParamList = append(bIsolatedParamList, bParam)

		default:
			logrus.Info(fmt.Sprintf("PtPutTrialFundCost PosSide err.tmpUserPos:%+v,fundRate:%+v", tmpUserPos, fundRate))
			return decimal.Zero
		}
	}

	logrus.Info(fmt.Sprintf("PtPutTrialFundCost FundingBalanceParam %s fundCost: %+v", settleId, fundCost))
	if len(bIsolatedParamList) > 0 {
		for _, param := range bIsolatedParamList {
			if param.Amount.IsZero() {
				continue
			}
			balanceRes := repository.BalanceRes{
				AssetLogs:     make([]*repository.MqCmsAsset, 0),
				BillAssetLogs: make([]repository.BillAssetSync, 0),
			}
			use.OnTrialBalanceAdd(param, &balanceRes, param.Amount, param.Currency)
			for i := 0; i < len(balanceRes.BillAssetLogs); i++ {
				balanceRes.BillAssetLogs[i].MarkPrice = use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol())
				balanceRes.BillAssetLogs[i].FundingRate = fundRate
			}
			err = use.trialRepo.UpdateTrialPos(ctx, tmpUserPos.Symbol(), userAsset)
			if err != nil {
				logrus.Info(fmt.Sprintf("UpdateTrialPos err:%+v, tmpUserPos:%+v,fundRate:%+v", err, tmpUserPos, fundRate))
				return decimal.Zero
			}
			if len(balanceRes.AssetLogs) > 0 {
				for _, assetLog := range balanceRes.AssetLogs {
					use.CreateFundingFee(ctx, tmpUserPos, *assetLog, userAsset, fundRate, use.priceRepo.GetMarkPrice(ctx, tmpUserPos.Symbol()))
					logrus.Println("PtPutTrialFundCost CreateFundingFee", userAsset.UID, fundRate)
				}
			}
			// // TODO 账本统计
			// go func() {
			// 	// 推送 资金费用 账单
			// 	redis := redislib.Redis()
			// 	coupling.AddAssetLogs(redis, balanceRes.AssetLogs...)
			// 	coupling.AddBills(redis, balanceRes.BillAssetLogs...)
			// }()
			// go func(p parameter.BalanceUpdate) {
			// 	balance, err := swapcache.GetCurrencyTotalBalance(userAsset.UserId, p.Currency)
			// 	if err != nil {
			// 		logrus.Info(fmt.Sprintf("PtPutTrialFundCost swapcache.GetCurrencyTotalBalance err: %v", err))
			// 		return
			// 	}
			// 	if p.Amount.IsPositive() {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount, balance)
			// 	} else {
			// 		es.SaveLedgerDetail(userAsset.UserId, p.Currency, es.DERIVATIVES_ACCOUNT_TYPE, es.FUND_FEE_LEDGER_TYPE, 0, p.Amount.Neg(), balance)
			// 	}
			// }(param)
		}
	}

	return fundCost
}

func (use *FundingUseCase) CreateFundingFee(
	ctx context.Context,
	tmpUserPos *repository.SettlementPos,
	mqAsset repository.MqCmsAsset,
	userAsset *repository.AssetSwap,
	fundRate, markPrice decimal.Decimal,
) {
	logFunding := entity.LogFundingFee{
		RefID:       mqAsset.OrderId,
		UID:         tmpUserPos.UID,
		UserType:    tmpUserPos.UserType,
		Currency:    tmpUserPos.Quote,
		Symbol:      tmpUserPos.Symbol(),
		FundingRate: fundRate,
		MarkPrice:   markPrice,
		Amount:      mqAsset.Amount,
		NetPos:      tmpUserPos.Pos,
		MarginMode:  int(tmpUserPos.MarginMode),
		OperateTime: mqAsset.OperateTime,
		PosSide:     int(tmpUserPos.PosSide),
	}
	if logFunding.Amount.IsPositive() {
		logFunding.Direction = domain.Income
	} else {
		logFunding.Direction = domain.Outlay
	}

	lFundPos := entity.FundingFeePos{}
	sFundPos := entity.FundingFeePos{}
	bFundPos := entity.FundingFeePos{}
	if tmpUserPos.LongPos.IsPositive() {
		lFundPos.PosSide = int(domain.LongPos)
		lFundPos.Pos = tmpUserPos.LongPos
		lFundPos.OpenPriceAvg = tmpUserPos.LongPriceAvg.Truncate(domain.CurrencyPrecision)
		lFundPos.Liquidation = userAsset.LongPos.Liquidation
		lFundPos.Margin = tmpUserPos.LongIsolatedMargin
		lFundPos.OpenTime = tmpUserPos.LongOpenTime / 1e9
		lFundPos.PosId = tmpUserPos.LongPosId
	}
	if tmpUserPos.ShortPos.IsPositive() {
		sFundPos.PosSide = int(domain.ShortPos)
		sFundPos.Pos = tmpUserPos.ShortPos
		sFundPos.OpenPriceAvg = tmpUserPos.ShortPriceAvg.Truncate(domain.CurrencyPrecision)
		sFundPos.Liquidation = userAsset.ShortPos.Liquidation
		sFundPos.Margin = tmpUserPos.ShortIsolatedMargin
		sFundPos.OpenTime = tmpUserPos.ShortOpenTime / 1e9
		sFundPos.PosId = tmpUserPos.ShortPosId
	}
	if !tmpUserPos.BothPos.IsZero() {
		bFundPos.PosSide = int(domain.BothPos)
		bFundPos.Pos = tmpUserPos.BothPos.Abs()
		bFundPos.OpenPriceAvg = tmpUserPos.BothPriceAvg.Truncate(domain.CurrencyPrecision)
		bFundPos.Liquidation = userAsset.BothPos.Liquidation
		bFundPos.Margin = tmpUserPos.BothIsolatedMargin
		bFundPos.OpenTime = tmpUserPos.BothOpenTime / 1e9
		bFundPos.PosId = tmpUserPos.BothPosId
	}

	switch tmpUserPos.PosSide {
	case domain.LongPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = userAsset.TrialLongPos.UserType
			logFunding.Leverage = userAsset.TrialLongPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = userAsset.LongPos.UserType
			logFunding.Leverage = userAsset.LongPos.Leverage
		}

	case domain.ShortPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = userAsset.TrialShortPos.UserType
			logFunding.Leverage = userAsset.TrialShortPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = userAsset.ShortPos.UserType
			logFunding.Leverage = userAsset.ShortPos.Leverage
		}

	case domain.BothPos:
		if tmpUserPos.IsTrial {
			// 体验金仓位
			logFunding.UserType = userAsset.TrialBothPos.UserType
			logFunding.Leverage = userAsset.TrialBothPos.Leverage
		} else {
			// 真实仓位
			logFunding.UserType = userAsset.BothPos.UserType
			logFunding.Leverage = userAsset.BothPos.Leverage
		}

	default:

	}

	if tmpUserPos.MarginMode == int32(domain.MarginModeIsolated) {
		switch tmpUserPos.PosSide {
		case domain.LongPos:
			logFunding.PosData = append(logFunding.PosData, lFundPos)

		case domain.ShortPos:
			logFunding.PosData = append(logFunding.PosData, sFundPos)

		case domain.BothPos:
			logFunding.PosData = append(logFunding.PosData, bFundPos)

		default:

		}
	} else {
		lever := decimal.NewFromInt(int64(logFunding.Leverage))
		if lFundPos.Pos.IsPositive() {
			if lFundPos.Margin.IsZero() && lever.IsPositive() {
				lFundPos.Margin = lFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, lFundPos)
		}
		if sFundPos.Pos.IsPositive() {
			if sFundPos.Margin.IsZero() && lever.IsPositive() {
				sFundPos.Margin = sFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, sFundPos)
		}
		if !bFundPos.Pos.IsZero() {
			if bFundPos.Margin.IsZero() && lever.IsPositive() {
				bFundPos.Margin = bFundPos.Pos.Mul(markPrice).Div(lever).Truncate(domain.CurrencyPrecision)
			}
			logFunding.PosData = append(logFunding.PosData, bFundPos)
		}
	}

	posB, _ := json.Marshal(logFunding.PosData)
	logFunding.StrPos = string(posB)

	err := use.fundingRepo.CreateFundingFee(ctx, &logFunding)
	if err != nil {
		logrus.WithField("err", err).Error("CreateFundingFee CreateFundingFee")
	}
}

// BalanceAdd 用户余额更新
// 由于手续费需要减 所以处理手续费的时候需要先取反
func (use *FundingUseCase) BalanceAdd(ctx context.Context, p repository.BalanceUpdate, asset *repository.AssetSwap, skipTrial bool) (res repository.BalanceRes) {
	if p.Amount.IsZero() {
		return
	}
	res = repository.BalanceRes{
		AssetLogs:      make([]*repository.MqCmsAsset, 0),
		BillAssetLogs:  make([]repository.BillAssetSync, 0),
		TrialAssetLogs: make([]*entity.TrialAsset, 0),
	}

	// Amount 大于0 (加钱)
	if p.Amount.Sign() > 0 {
		asset.AddBalance(p.Currency, p.Amount)
		use.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// ----------------- Amount 小于0 (扣钱) -----------------

	// 单一保证金
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		// 不跳过体验金
		if !skipTrial {
			trialBalance := asset.TrialCBalance(p.Currency)
			if trialBalance.IsPositive() {
				if afterBalance := trialBalance.Add(p.Amount); afterBalance.LessThan(decimal.Zero) {
					// 体验金不够扣
					trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialBalance)
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					use.OnTrialBalanceAdd(p, &res, trialBalance.Neg(), p.Currency)
					p.Amount = afterBalance
				} else {
					// 体验金够扣
					trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					use.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
					return
				}
			}
		}
		asset.AddBalance(p.Currency, p.Amount)
		use.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// 混合保证金 负数金额会走到这里

	// 不跳过体验金
	if !skipTrial {
		trialCBalance := asset.TrialCBalance(p.Currency)
		// 当前币种余额够扣减
		if trialCBalance.Add(p.Amount).IsPositive() {
			trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
			use.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
			return
		}

		// 当前币种余额不够 有多少扣多少
		if trialCBalance.IsPositive() {
			trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialCBalance)
			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
			use.OnTrialBalanceAdd(p, &res, trialCBalance.Neg(), p.Currency)
		}

		// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
		trialCBalance = decimal.Max(decimal.NewFromInt(0), trialCBalance)
		// amount 是负数
		p.Amount = p.Amount.Add(trialCBalance)
		for _, v := range domain.CurrencyList {
			if v == p.Currency {
				continue
			}
			rate := use.priceRepo.SpotRate(ctx, v, p.Currency)
			vFee, _ := util.RoundCeil(p.Amount.Div(rate), domain.CurrencyPrecision)
			vTrialBalance := asset.TrialCBalance(v)
			// 当前币种余额够扣减
			if vTrialBalance.GreaterThan(decimal.Zero) {
				if vTrialBalance.Add(vFee).IsPositive() {
					trialAssetList := asset.ConsumeTrialBalance(v, vFee.Neg())
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					use.OnTrialBalanceAdd(p, &res, vFee, v)
					return
				} else {
					trialAssetList := asset.ConsumeTrialBalance(v, vTrialBalance)
					res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
					use.OnTrialBalanceAdd(p, &res, vTrialBalance.Neg(), v)
					p.Amount = p.Amount.Add(vTrialBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
				}
			}
		}
	}

	cBalance := asset.CBalance(p.Currency)
	// 当前币种余额够扣减
	if cBalance.Add(p.Amount).IsPositive() {
		asset.AddBalance(p.Currency, p.Amount)
		use.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	// 当前币种余额不够 有多少扣多少
	if cBalance.IsPositive() {
		asset.AddBalance(p.Currency, cBalance.Neg())
		use.OnBalanceAdd(p, &res, cBalance.Neg(), p.Currency)
	}

	// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
	cBalance = decimal.Max(decimal.NewFromInt(0), cBalance)
	// amount 是负数
	p.Amount = p.Amount.Add(cBalance)
	for _, v := range domain.CurrencyList {
		if v == p.Currency {
			continue
		}
		rate := use.priceRepo.SpotRate(ctx, v, p.Currency)
		vFee, _ := util.RoundCeil(p.Amount.Div(rate), domain.CurrencyPrecision)
		vBalance := asset.CBalance(v)
		// 当前币种余额够扣减
		if vBalance.GreaterThan(decimal.Zero) {
			if vBalance.Add(vFee).IsPositive() {
				asset.AddBalance(v, vFee)
				use.OnBalanceAdd(p, &res, vFee, v)
				return
			} else {
				asset.AddBalance(v, vBalance.Neg())
				use.OnBalanceAdd(p, &res, vBalance.Neg(), v)
				p.Amount = p.Amount.Add(vBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
			}
		}
	}

	// 当前币种都已经看过了 发现还没有扣完需要继续扣 以便穿仓补贴
	if !p.Amount.IsZero() {
		asset.AddBalance(p.Currency, p.Amount)
		use.OnBalanceAdd(p, &res, p.Amount, p.Currency)
		return
	}

	return res
}

// OnBalanceAdd 余额更新之后产生的流水
// p:资产变更的基本信息 amount：资产变更的数量 currency:资产变更的币种 联合保证金时有可能跟p中不一致
func (use *FundingUseCase) OnBalanceAdd(p repository.BalanceUpdate, res *repository.BalanceRes, amount decimal.Decimal, currency string) {
	mqCmsAsset := &repository.MqCmsAsset{}

	util.Assign(&p, mqCmsAsset)
	mqCmsAsset.Currency = strings.ToUpper(currency)
	mqCmsAsset.Amount = amount
	res.AssetLogs = append(res.AssetLogs, mqCmsAsset)

	billSwap := &repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:        p.UID,
			RefID:      p.OrderId,
			BillID:     use.gen.Generate().Int64(),
			Symbol:     strings.ToUpper(p.Symbol),
			Currency:   strings.ToUpper(currency),
			BillType:   int(p.OperateType),
			Amount:     amount,
			CreateTime: time.Now().UnixNano(),
		},
	}
	res.BillAssetLogs = append(res.BillAssetLogs, *billSwap)
}

// OnTrialBalanceAdd 体验金余额更新之后产生的流水
// p:资产变更的基本信息 amount：资产变更的数量 currency:资产变更的币种 联合保证金时有可能跟p中不一致
func (use *FundingUseCase) OnTrialBalanceAdd(p repository.BalanceUpdate, res *repository.BalanceRes, amount decimal.Decimal, currency string) {
	switch p.OperateType {
	case domain.BillTypeFee:
		p.OperateType = domain.BillTypeFeeTrial

	case domain.BillTypeFunding:
		p.OperateType = domain.BillTypeFundingTrial

	case domain.BillTypePlatFee:
		p.OperateType = domain.BillTypePlatFeeTrial

	case domain.BillTypeReal:
		p.OperateType = domain.BillTypeRealTrial

	default:

	}

	mqCmsAsset := &repository.MqCmsAsset{}

	util.Assign(&p, mqCmsAsset)
	mqCmsAsset.Currency = strings.ToUpper(currency)
	mqCmsAsset.TAsset = repository.CmsTrialAsset{
		TAmount: amount,
		TDetail: res.TrialAssetLogs,
	}
	mqCmsAsset.Amount = amount
	res.AssetLogs = append(res.AssetLogs, mqCmsAsset)

	billSwap := &repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:        p.UID,
			RefID:      p.OrderId,
			BillID:     use.gen.Generate().Int64(),
			Symbol:     strings.ToUpper(p.Symbol),
			Currency:   strings.ToUpper(currency),
			BillType:   int(p.OperateType),
			Amount:     amount,
			CreateTime: time.Now().UnixNano(),
		},
	}
	res.BillAssetLogs = append(res.BillAssetLogs, *billSwap)
}
