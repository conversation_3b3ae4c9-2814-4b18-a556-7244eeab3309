package usecase

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	ytSnowflake "futures-asset/internal/libs/snowflake"
	"futures-asset/internal/utils"
	"futures-asset/util"

	"github.com/bwmarrin/snowflake"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	cfg "yt.com/backend/common.git/config"
)

// PositionUseCase 仓位用例实现
type PositionUseCase struct {
	config       *cfg.Config[configs.Config]
	positionRepo repository.PositionRepository
	cacheRepo    repository.CacheRepository
	assetRepo    repository.AssetRepository

	gen *snowflake.Node
}

// PositionUseCaseParam 仓位用例参数
type PositionUseCaseParam struct {
	dig.In

	Config       *cfg.Config[configs.Config] `name:"config"`
	PositionRepo repository.PositionRepository
	CacheRepo    repository.CacheRepository
	AssetRepo    repository.AssetRepository
}

// NewPositionUseCase 创建仓位用例实例
func NewPositionUseCase(param PositionUseCaseParam) usecase.PositionUseCase {
	n, err := ytSnowflake.New()
	if err != nil {
		panic(fmt.Sprintf("NewOrderUseCase error: %v", err))
	}

	return &PositionUseCase{
		config:       param.Config,
		positionRepo: param.PositionRepo,
		cacheRepo:    param.CacheRepo,
		assetRepo:    param.AssetRepo,
		gen:          n,
	}
}

// PlatPosDetail implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosDetail(ctx context.Context, req *repository.PlatPosDetailReq) (repository.PlatPosDetail, error) {
	return uc.positionRepo.PlatPosDetail(ctx, req)
}

// PlatPosList implements usecase.PositionUseCase.
func (uc *PositionUseCase) PlatPosList(ctx context.Context) (repository.PlatPosList, error) {
	return uc.positionRepo.PlatPosList(ctx)
}

// PosInfo implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosInfo(ctx context.Context, param *repository.UserPosParam) (repository.PosQuery, error) {
	return uc.positionRepo.PosInfo(ctx, param)
}

// PosTotal implements usecase.PositionUseCase.
func (uc *PositionUseCase) PosTotal(ctx context.Context, contractCode string) decimal.Decimal {
	return uc.positionRepo.PosTotal(ctx, contractCode)
}

// QueryUserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) QueryUserPos(ctx context.Context, req *repository.UserPosParam) (repository.UserPosReply, error) {
	return uc.positionRepo.QueryUserPos(ctx, req)
}

// UserHoldPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserHoldPos(ctx context.Context, req *repository.UserHoldPosReq) (repository.HoldPosReply, error) {
	return uc.positionRepo.UserHoldPos(ctx, req)
}

// UserPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) UserPos(ctx context.Context, req *repository.SwapParam) ([]repository.PosSwap, error) {
	return uc.positionRepo.UserPos(ctx, req)
}

// OpenLongPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	leverage := accountSettleParam.GetLeverage()
	marginMode := accountSettleParam.GetMarginMode()
	userType := accountSettleParam.GetUserType()
	base, quote := accountSettleParam.GetBaseQuote()
	orderFronzen := accountSettleParam.GetOrderFronzen()

	feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	if err != nil {
		return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
	}
	unfrozenMargin, err := uc.calcUnfrozenMargin(accountSettleParam)
	if err != nil {
		return fmt.Errorf("calcUnfrozenMargin() error: err=%v", err)
	}

	contractCode := util.ContractCode(base, quote)

	// 新建仓位
	if userAsset.LongPos.PosStatus == domain.PosStatusNone || userAsset.LongPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		posId := uc.gen.Generate().String()
		userAsset.LongPos.NewPos(userAsset.UID, posId, userType, int32(marginMode), leverage)
		// 设置合约代码、方向、币种
		userAsset.LongPos.PosSide = domain.LongPos
		userAsset.LongPos.Symbol = contractCode
		userAsset.LongPos.Currency = quote
	}

	// 计算开仓均价
	userAsset.LongPos.OpenPriceAvg = userAsset.LongPos.CalcOpenPriceAvg(amount, price)
	// 更新仓位数
	userAsset.LongPos.Pos = userAsset.LongPos.Pos.Add(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:OpenLongPos()", amount))
	// 更新可平仓数
	userAsset.LongPos.PosAvailable = userAsset.LongPos.PosAvailable.Add(amount)

	// 减少冻结的保证金
	userAsset.DecrFrozen(contractCode, unfrozenMargin)
	*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:unfrozen from:OpenLongPos()", unfrozenMargin))

	// 扣手续费,余额变更
	fee := amount.Mul(price).Mul(feeRate)
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:OpenLongPos()", fee.Neg(), domain.BillTypeFee.String()))

	// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 账本记录
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
	// 	if err != nil {
	// 		logrus.Errorf("OpenLongPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	// }()

	// 手续费明细
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 计算仓位保证金
	posMargin := userAsset.LongPos.CalcPosMargin(amount, price, fee)
	// 06.09 james沟通调整:
	// if 真实成本 < (冻结资金 - 手续费):
	// 	仓位保证金 = 真实成本
	// 	退用户余额 = (冻结资金 - 手续费) - 真实成本
	// else 真实成本 >=(冻结资金 - 手续费):
	//     仓位保证金 = (冻结资金 - 手续费)
	if posMargin.GreaterThan(orderFronzen.Sub(fee)) {
		posMargin = orderFronzen.Sub(fee)
	}

	// 逐仓处理保证金
	if userAsset.LongPos.Isolated() {
		userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(posMargin)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:OpenLongPos()", posMargin))
	}

	// 计算占用体验金
	// trialBillList := make([]*entity.TrialAsset, 0)
	// asset.LongPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.LongPos, pCache)
	// reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

	// 更新缓存
	ctx := context.Background()
	err = uc.cacheRepo.UpdateLongPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateLongPos() error: err=%v", err)
	}

	// 统计用户开仓数据
	// if !domain.RobotUsers.HasKey(slf.UID) {
	// 	go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
	// 		UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.LongPos.OpenTime,
	// 		HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: asset.LongPos.PosId,
	// 	})
	// }

	// 体验金标志?
	// haveTrial := 0
	// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// }

	// 创建仓位变动日志
	// reply.LogPos = slf.NewLogPosSync(asset.LongPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side, trade.PosSide, slf.Amount, decimal.Zero)
	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.LongPos.PosId,
	// 	Pos:          asset.LongPos.Pos,
	// 	OpenPriceAvg: asset.LongPos.OpenPriceAvg,
	// 	PosSide:      asset.LongPos.PosSide,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// OpenShortPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	leverage := accountSettleParam.GetLeverage()
	marginMode := accountSettleParam.GetMarginMode()
	userType := accountSettleParam.GetUserType()
	base, quote := accountSettleParam.GetBaseQuote()
	orderFronzen := accountSettleParam.GetOrderFronzen()

	feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	if err != nil {
		return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
	}
	unfrozenMargin, err := uc.calcUnfrozenMargin(accountSettleParam)
	if err != nil {
		return fmt.Errorf("calcUnfrozenMargin() error: err=%v", err)
	}

	contractCode := util.ContractCode(base, quote)

	// 新建仓位
	if userAsset.ShortPos.PosStatus == domain.PosStatusNone || userAsset.ShortPos.PosStatus == domain.PosStatusEnd {
		posId := uc.gen.Generate().String()
		userAsset.ShortPos.NewPos(userAsset.UID, posId, userType, int32(marginMode), leverage)
		// 设置合约代码、方向、币种
		userAsset.ShortPos.PosSide = domain.ShortPos
		userAsset.ShortPos.Symbol = contractCode
		userAsset.ShortPos.Currency = quote
	}

	// 计算开仓均价
	userAsset.ShortPos.OpenPriceAvg = userAsset.ShortPos.CalcOpenPriceAvg(amount, price)
	// 更新仓位数
	userAsset.ShortPos.Pos = userAsset.ShortPos.Pos.Add(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:OpenShortPos()", amount))
	// 更新可平仓位
	userAsset.ShortPos.PosAvailable = userAsset.ShortPos.PosAvailable.Add(amount)

	// 减少冻结的保证金
	userAsset.DecrFrozen(contractCode, unfrozenMargin)
	*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:unfrozen from:OpenShortPos()", unfrozenMargin))

	// 扣手续费,余额变更
	fee := amount.Mul(price).Mul(feeRate)
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:OpenShortPos()", fee.Neg(), domain.BillTypeFee.String()))

	// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 账本记录
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
	// 	if err != nil {
	// 		logrus.Errorf("OpenShortPos GetCurrencyTotalBalance uid: %s userType:%d currency: %s error: %v", trade.UID, trade.UserType, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	// }()

	// 手续费明细
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 计算仓位保证金
	posMargin := userAsset.ShortPos.CalcPosMargin(amount, price, fee)
	// 06.09 james沟通调整:
	// if 真实成本 < (冻结资金 - 手续费):
	// 	仓位保证金 = 真实成本
	// 	退用户余额 = (冻结资金 - 手续费) - 真实成本
	// else 真实成本 >=(冻结资金 - 手续费):
	//     仓位保证金 = (冻结资金 - 手续费)
	if posMargin.GreaterThan(orderFronzen.Sub(fee)) {
		posMargin = orderFronzen.Sub(fee)
	}

	// 逐仓处理保证金
	if userAsset.ShortPos.Isolated() {
		userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(posMargin)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:OpenShortPos()", posMargin))
	}

	// 计算占用体验金
	// trialBillList := make([]*entity.TrialAsset, 0)
	// asset.ShortPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.ShortPos, pCache)
	// reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

	// 更新缓存
	ctx := context.Background()
	err = uc.cacheRepo.UpdateShortPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateShortPos() error: err=%v", err)
	}

	// 统计用户开仓数据
	// if !domain.RobotUsers.HasKey(slf.UID) {
	// 	go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
	// 		UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.ShortPos.OpenTime,
	// 		HoldPosValue: slf.Price.Mul(slf.Amount), LatestPosId: asset.ShortPos.PosId,
	// 	})
	// }

	// 体验金标志?
	// 	haveTrial := 0
	// if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// }

	// 创建仓位变动日志
	// reply.LogPos = slf.NewLogPosSync(asset.ShortPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side, trade.PosSide, slf.Amount, decimal.Zero)
	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.ShortPos.PosId,
	// 	Pos:          asset.ShortPos.Pos,
	// 	OpenPriceAvg: asset.ShortPos.OpenPriceAvg,
	// 	PosSide:      asset.ShortPos.PosSide,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// OpenBothPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) OpenBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, amount decimal.Decimal, fee decimal.Decimal, unfrozenMargin decimal.Decimal, changelog *[]string) error {
	price := accountSettleParam.GetPrice()
	leverage := accountSettleParam.GetLeverage()
	side := accountSettleParam.GetSide()
	marginMode := accountSettleParam.GetMarginMode()
	userType := accountSettleParam.GetUserType()
	base, quote := accountSettleParam.GetBaseQuote()
	orderFronzen := accountSettleParam.GetOrderFronzen()

	// 反向操作时如果重新开仓的话，父函数已经计算amount,fee,unfrozenMargin
	if amount.IsZero() {
		amount = accountSettleParam.GetAmount()
		if side == domain.Sell {
			amount = amount.Neg()
		}
	}
	if fee.IsZero() {
		feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
		if err != nil {
			return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
		}
		fee = amount.Mul(price).Mul(feeRate)
	}
	if unfrozenMargin.IsZero() {
		var err error
		unfrozenMargin, err = uc.calcUnfrozenMargin(accountSettleParam)
		if err != nil {
			return fmt.Errorf("calcUnfrozenMargin() error: err=%v", err)
		}
	}

	contractCode := util.ContractCode(base, quote)

	// 新建仓位
	if userAsset.BothPos.PosStatus == domain.PosStatusNone || userAsset.BothPos.PosStatus == domain.PosStatusEnd { // 新开仓分配持仓Id和首次开仓时间
		oldPosId := userAsset.BothPos.PosId
		posId := uc.gen.Generate().String()
		// 机器人持仓仓位ID不变化
		if userType == 6 && len(oldPosId) > 0 {
			posId = oldPosId
		}
		userAsset.BothPos.NewPos(userAsset.UID, posId, userType, int32(marginMode), leverage)
		// 设置合约代码、方向、币种
		userAsset.ShortPos.PosSide = domain.BothPos
		userAsset.ShortPos.Symbol = contractCode
		userAsset.ShortPos.Currency = quote
	}

	// 没有IsRobotSelfTrade字段
	// 机器人单向持仓自成交不变动仓位开仓均价(相当于自身多空抵消, 无仓位变化) ?
	//if !trade.IsRobotSelfTrade {
	// 计算开仓均价
	userAsset.BothPos.OpenPriceAvg = userAsset.BothPos.CalcOpenPriceAvg(amount, price)
	//}
	// 更新仓位数
	userAsset.BothPos.Pos = userAsset.BothPos.Pos.Add(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:OpenBothPos()", amount))
	// 更新可平仓数
	userAsset.BothPos.PosAvailable = userAsset.BothPos.PosAvailable.Add(amount)

	// 减少冻结的保证金
	userAsset.DecrFrozen(contractCode, unfrozenMargin)
	*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:unfrozen from:OpenBothPos()", unfrozenMargin))

	// 手续费余额变更
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:OpenBothPos()", fee.Neg(), domain.BillTypeFee.String()))

	// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 账本记录
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, slf.Quote)
	// 	if err != nil {
	// 		logrus.Errorf("OpenBothPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, fee.Neg(), balance)
	// }()

	// 手续费明细
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 计算仓位保证金
	posMargin := userAsset.BothPos.CalcPosMargin(amount.Abs(), price, fee) // 仓位保证金
	// 06.09 james沟通调整:
	// if 真实成本 < (冻结资金 - 手续费):
	// 	仓位保证金 = 真实成本
	// 	退用户余额 = (冻结资金 - 手续费) - 真实成本
	// else 真实成本 >=(冻结资金 - 手续费):
	//     仓位保证金 = (冻结资金 - 手续费)
	if posMargin.GreaterThan(orderFronzen.Sub(fee)) {
		posMargin = orderFronzen.Sub(fee)
	}

	// 逐仓处理保证金
	if userAsset.BothPos.Isolated() {
		userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(posMargin)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:OpenBothPos()", posMargin))
	}

	// 计算占用体验金
	// trialBillList := make([]*entity.TrialAsset, 0)
	// asset.BothPos, trialBillList = asset.CalcTrialMargin(slf.Quote, posMargin, asset.BothPos, pCache)
	// reply.TrialLogs = append(reply.TrialLogs, trialBillList...)

	// 更新缓存
	ctx := context.Background()
	err := uc.cacheRepo.UpdateBothPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateBothPos() error: err=%v", err)
	}

	// 统计用户开仓数据
	// if !domain.RobotUsers.HasKey(slf.UID) {
	// 	go redislib.Redis().LPush(domain.SyncListUserStatistics, swap.UserStatistics{
	// 		UID: slf.UID, AccountType: match.AccountTypeSwap, FirstOpenTime: asset.BothPos.OpenTime,
	// 		HoldPosValue: slf.Price.Mul(amount.Abs()), LatestPosId: asset.BothPos.PosId,
	// 	})
	// }

	// 体验金标志?
	// haveTrial := 0
	// if asset.BothPos.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// }

	// 创建仓位变动日志
	// reply.LogPos = slf.NewLogPosSync(asset.BothPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side,
	// 	trade.PosSide, amount, decimal.Zero)
	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.BothPos.PosId,
	// 	Pos:          asset.BothPos.Pos,
	// 	OpenPriceAvg: asset.BothPos.OpenPriceAvg,
	// 	PosSide:      asset.BothPos.PosSide,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// CloseLongPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseLongPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {
	price := accountSettleParam.GetPrice()
	amount := accountSettleParam.GetAmount()
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()
	feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	if err != nil {
		return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
	}

	// 体验金标志与拉平 ?
	// haveTrial := 0
	// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// 	// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
	// 	if asset.LongPos.IsolatedMargin.LessThan(asset.LongPos.TrialMargin) {
	// 		asset.LongPos.TrialMargin = asset.LongPos.IsolatedMargin
	// 	}
	// }

	// 计算平仓盈亏
	profitReal := userAsset.LongPos.CalcProfitReal(price, amount)

	// 更新仓位数
	userAsset.LongPos.Pos = userAsset.LongPos.Pos.Sub(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:CloseLongPos()", amount.Neg()))

	// compareProfitReal = profitReal
	// originPosAmount := asset.LongPos.Pos
	// originPosIsolateMargin := asset.LongPos.IsolatedMargin

	// 检查仓位是否合法
	if userAsset.LongPos.Pos.Sign() < 0 {
		return fmt.Errorf("251111 pos is negative after closing long: pos=%v amount=%v", userAsset.LongPos.Pos, amount)
	}

	// 检查杠杆是否合法
	if userAsset.LongPos.Leverage < 1 {
		return errors.New("251114 long pos leverage < 1")
	}

	// 更新平仓盈亏记数
	userAsset.LongPos.ProfitReal = userAsset.LongPos.ProfitReal.Add(profitReal)

	billProfitReal := profitReal

	// skipTrialDiscount := true
	// 亏钱先扣体检金 ?
	// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
	// ...
	// }

	// 盈亏更新真金白银,余额变更
	if !billProfitReal.IsZero() {
		uc.BalanceAdd(billProfitReal, domain.BillTypeReal, userAsset, priceRepo, accountSettleParam)
		*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseLongPos()", billProfitReal, domain.BillTypeReal.String()))
		// reply.AssetLogs = append(reply.AssetLogs, profitBillList.AssetLogs...)
		// reply.BillAssetLogs = append(reply.BillAssetLogs, profitBillList.BillAssetLogs...)
	}

	// 扣手续费,余额变更
	fee := amount.Mul(price).Mul(feeRate)
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseLongPos()", fee.Neg(), domain.BillTypeFee.String()))

	// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 手续费明细 ?
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 暗成交 ?
	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	// if slf.TradeType == domain.TradeTypeDirect {
	// 	if asset.LongPos.PosAvailable.LessThan(slf.Amount) {
	// 		log.Printf("dark deal close long pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
	// 			slf.UID, asset.LongPos.PosAvailable, slf.Amount)
	// 		return reply, domain.Code251119, errors.New("dark deal close long pos pos available less than slf.amount")
	// 	}
	// 	asset.LongPos.PosAvailable = asset.LongPos.PosAvailable.Sub(slf.Amount)
	// }

	userAsset.LongPos.LiquidationType = accountSettleParam.GetLiquidationType()

	// 处理仓位保证金
	closePosMargin := decimal.Zero
	if userAsset.LongPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = amount.Div(userAsset.LongPos.Pos).Mul(userAsset.LongPos.IsolatedMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(fee))

		// 体验金?
		// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		// 	asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(subDeltaAmount)
		// }

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(subDeltaAmount)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:CloseLongPos()", subDeltaAmount))

		if userAsset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			// 体验金?
			// 逐仓的 IsolatedMargin 包含体验金保证金，所以穿仓需要用体验金的保证金填补
			// if asset.LongPos.TrialMargin.LessThan(decimal.Zero) {
			// 	if asset.LongPos.IsolatedMargin.LessThan(decimal.Zero) {
			// 		asset.LongPos.IsolatedMargin = decimal.Min(decimal.NewFromInt(0), asset.LongPos.IsolatedMargin.Add(asset.LongPos.TrialMargin.Neg()))
			// 	}
			// 	asset.LongPos.TrialMargin = decimal.Zero
			// }

			// 平仓穿仓逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if userAsset.LongPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := userAsset.LongPos.IsolatedMargin.Abs() // 这个补贴就是保证金正数?
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 逐仓保证金加穿仓补贴的费用
				userAsset.LongPos.IsolatedMargin = userAsset.LongPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseLongPos()", bankruptAmount))

				userAsset.LongPos.Subsidy = userAsset.LongPos.Subsidy.Add(bankruptAmount)
				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"isolatedMargin": userAsset.LongPos.IsolatedMargin,
					"subsidy":        userAsset.LongPos.Subsidy,
				}).Info("tp0803 close isolated margin < 0")
			}
		}
	} else { // 全仓模式
		closePosMargin = price.Mul(amount).Div(decimal.NewFromInt(int64(userAsset.LongPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 体验金?
		// 只有盈亏为负数时候, 从保证金上扣
		// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) {
		//	subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
		//	asset.LongPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.LongPos.TrialMargin.Add(subDeltaAmount))
		// }

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if userAsset.LongPos.LiquidationType != domain.LiquidationTypeBurst {
			// 单一资产模式
			totalBalance := userAsset.CBalance(quote)
			rate := decimal.NewFromInt(1) // quote不是U时, 汇率还是1?
			// 联合资产模式
			if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI { // Todo 这里要确认AssetMode
				var err error = nil
				ctx := context.Background()
				totalBalance, err = uc.assetRepo.TotalJoinBalance(ctx, userAsset)
				if err != nil {
					return fmt.Errorf("TotalJoinBalance() error: err=%v", err)
				}
				rate = priceRepo.SpotURate(ctx, quote)
				if rate.IsZero() {
					return errors.New("CloseLongPos close rate is zero") // todo Code992408 由于已经联调下次修改需要更新
				}
			}

			// 体验金?
			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			// if asset.LongPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
			//	if totalBalance.Abs().LessThanOrEqual(asset.LongPos.TrialMargin.Mul(rate)) {
			//		totalBalance = totalBalance.Add(totalBalance.Neg())
			//		asset.LongPos.TrialMargin = asset.LongPos.TrialMargin.Add(totalBalance.Div(rate))
			//	} else {
			//		totalBalance = totalBalance.Add(asset.LongPos.TrialMargin.Mul(rate))
			//		asset.LongPos.TrialMargin = decimal.Zero
			//	}
			// }

			// 全身加起来还是负的, 平台爸爸补
			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseLongPos()", bankruptAmount))

				userAsset.LongPos.Subsidy = userAsset.LongPos.Subsidy.Add(bankruptAmount)

				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"subsidy":        userAsset.LongPos.Subsidy,
				}).Info("tp0803 close cross total balance < 0")
			}
		}
	}

	// 平仓后如果仓位归0, 生成历史持仓记录
	if userAsset.LongPos.Pos.Sign() <= 0 {
		// reply.ClearPos = asset.LongPos
		userAsset.LongPos.Clear() // 清空现有持仓
	}

	// 更新缓存
	contractCode := util.ContractCode(base, quote)
	ctx := context.Background()
	err = uc.cacheRepo.UpdateLongPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateLongPos() error: err=%v", err)
	}

	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	// if !domain.RobotUsers.HasKey(slf.UID) {
	// 	tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
	// 	if tempProfit.Sign() > 0 {
	// 		go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
	// 			UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
	// 			TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
	// 		})
	// 	} else {
	// 		go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
	// 			UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
	// 			TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
	// 		})
	// 	}
	// }

	// 撤单 ?
	// go func() {
	// 	// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
	// 	totalBalance := asset.CBalance(trade.FeeCurrency)
	// 	if asset.AssetMode == domain.AssetMode {
	// 		totalBalance, err = asset.TotalJoinBalance(pCache)
	// 		if err != nil {
	// 			log.Println("CloseShortPos revoke", err)
	// 		}
	// 	}
	// 	if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
	// 	}
	// 	// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
	// 	if asset.ShortPos.Pos.Sign() <= 0 {
	// 		RemoveRivalScore(asset.ShortPos.UID, asset.ShortPos.ContractCode, asset.ShortPos.PosSide, false) // 删除对手方评分
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, slf.Base, slf.Quote, domain.Close, domain.Buy, asset.ShortPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
	// 	}
	// }()

	// 账本记录?
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, bFeeParam.Currency)
	// 	if err != nil {
	// 		logrus.Errorf("CloseShortPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(trade.Fee))
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	// }()

	// 返回?
	// reply.LogPos = slf.NewLogPosSync(asset.LongPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side, trade.PosSide, slf.Amount, profitReal)

	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.LongPos.PosId,
	// 	Pos:          asset.LongPos.Pos,
	// 	OpenPriceAvg: asset.LongPos.OpenPriceAvg,
	// 	PosSide:      asset.LongPos.PosSide,
	// 	ProfitReal:   profitReal,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// CloseShortPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseShortPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {
	price := accountSettleParam.GetPrice()
	amount := accountSettleParam.GetAmount()
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()
	feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	if err != nil {
		return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
	}

	// 体验金标志与拉平 ?
	// haveTrial := 0
	// if asset.Short.TrialMargin.GreaterThan(decimal.Zero) {
	// 	haveTrial = 1
	// 	// 保证金小于体验金保证金时候需要强制拉平（仅可能会出现在纯体验金仓位）
	// 	if asset.ShortP o s.IsolatedMargin.LessThan(asset.ShortPos.TrialMargin) {
	// 		asset.ShortPos.TrialMargin = asset.ShortPos.IsolatedMargin
	// 	}
	// }

	// 计算平仓盈亏
	profitReal := userAsset.ShortPos.CalcProfitReal(price, amount)

	// 更新仓位数
	userAsset.ShortPos.Pos = userAsset.ShortPos.Pos.Sub(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:CloseShortPos()", amount.Neg()))

	// compareProfitReal = profitReal
	// originPosAmount := asset.ShortPos.Pos
	// originPosIsolateMargin := asset.ShortPos.IsolatedMargin

	// 检查仓位是否合法
	if userAsset.ShortPos.Pos.Sign() < 0 {
		return fmt.Errorf("251111 pos is negative after closing short: pos=%v amount=%v", userAsset.ShortPos.Pos, amount)
	}

	// 检查杠杆是否合法
	if userAsset.ShortPos.Leverage < 1 {
		return errors.New("251114 short pos leverage < 1")
	}

	// 更新平仓盈亏记数
	userAsset.ShortPos.ProfitReal = userAsset.ShortPos.ProfitReal.Add(profitReal)

	billProfitReal := profitReal

	// skipTrialDiscount := true
	// 亏钱先扣体检金 ?
	// if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) && billProfitReal.LessThan(decimal.Zero) {
	// ...
	// }

	// 盈亏更新真金白银,余额变更
	if !billProfitReal.IsZero() {
		uc.BalanceAdd(billProfitReal, domain.BillTypeReal, userAsset, priceRepo, accountSettleParam)
		*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseShortPos()", billProfitReal, domain.BillTypeReal.String()))
		// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
		// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
		// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)
	}

	// 扣手续费,余额变更
	fee := amount.Mul(price).Mul(feeRate)
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseShortPos()", fee.Neg(), domain.BillTypeFee.String()))

	// reply.AssetLogs = append(reply.AssetLogs, feeBillList.AssetLogs...)
	// reply.BillAssetLogs = append(reply.BillAssetLogs, feeBillList.BillAssetLogs...)
	// reply.TrialLogs = append(reply.TrialLogs, feeBillList.TrialAssetLogs...)

	// 手续费明细 ?
	// feeDetails := make([]payload.FeeDetail, 0)
	// for _, detail := range feeBillList.AssetLogs {
	// 	feeDetails = append(feeDetails, payload.FeeDetail{
	// 		Currency: detail.Currency,
	// 		Amount:   detail.Amount,
	// 		Price:    pCache.SpotURate(detail.Currency),
	// 	})
	// }

	// 暗成交 ?
	// 如果是暗成交的话, 生成的委托单没有进深度(撮合没有调用lock接口), 此时平仓后需要扣减 PosAvailable
	// 非暗成交时, 委托单进深度(撮合调用lock接口, 此时 PosAvailable 已经扣除了平仓的数量), 因此不用重复扣减 PosAvailable
	// if slf.TradeType == domain.TradeTypeDirect {
	// 	if asset.ShortPos.PosAvailable.LessThan(slf.Amount) {
	// 		log.Printf("dark deal close pos pos available less than slf.amount uid:%s PosAvailable:%s slf.amount:%s",
	// 			slf.UID, asset.ShortPos.PosAvailable, slf.Amount)
	// 		return reply, domain.Code251119, errors.New("dark deal close pos pos available less than req.amount")
	// 	}
	// 	asset.ShortPos.PosAvailable = asset.ShortPos.PosAvailable.Sub(slf.Amount)
	// }

	userAsset.ShortPos.LiquidationType = accountSettleParam.GetLiquidationType()

	// 处理仓位保证金
	closePosMargin := decimal.Zero
	if userAsset.ShortPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = amount.Div(userAsset.ShortPos.Pos).Mul(userAsset.ShortPos.IsolatedMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(fee))

		// 体验金?
		// if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
		// 	asset.ShortPos.TrialMargin = asset.ShortPos.TrialMargin.Add(subDeltaAmount)
		// }

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(subDeltaAmount)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:CloseShortPos()", subDeltaAmount))

		if userAsset.ShortPos.LiquidationType != domain.LiquidationTypeBurst {
			// 体验金?
			// 逐仓的 IsolatedMargin 包含体验金保证金，所以穿仓需要用体验金的保证金填补
			// if asset.ShortPos.TrialMargin.LessThan(decimal.Zero) {
			// 	if asset.ShortPos.IsolatedMargin.LessThan(decimal.Zero) {
			// 		asset.ShortPos.IsolatedMargin = decimal.Min(decimal.NewFromInt(0), asset.ShortPos.IsolatedMargin.Add(asset.ShortPos.TrialMargin.Neg()))
			// 	}
			// 	asset.ShortPos.TrialMargin = decimal.Zero
			// }

			// 平仓穿仓逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if userAsset.ShortPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := userAsset.ShortPos.IsolatedMargin.Abs() // 这个补贴就是保证金正数?
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 逐仓保证金加穿仓补贴的费用
				userAsset.ShortPos.IsolatedMargin = userAsset.ShortPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseShortPos()", bankruptAmount))

				userAsset.ShortPos.Subsidy = userAsset.ShortPos.Subsidy.Add(bankruptAmount)
				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"isolatedMargin": userAsset.ShortPos.IsolatedMargin,
					"subsidy":        userAsset.ShortPos.Subsidy,
				}).Info("tp0803 close isolated margin < 0")
			}
		}
	} else { // 全仓模式
		closePosMargin = price.Mul(amount).Div(decimal.NewFromInt(int64(userAsset.ShortPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 体验金?
		// 只有盈亏为负数时候, 从保证金上扣 ?
		// if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) {
		//	subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin).Sub(trade.Fee)
		//	asset.ShortPos.TrialMargin = decimal.Max(decimal.NewFromInt(0), asset.ShortPos.TrialMargin.Add(subDeltaAmount))
		// }

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if userAsset.ShortPos.LiquidationType != domain.LiquidationTypeBurst {
			// 单一资产模式
			totalBalance := userAsset.CBalance(quote)
			rate := decimal.NewFromInt(1) // quote不是U时, 汇率还是1?
			// 联合资产模式
			if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI { // Todo 这里要确认AssetMode
				var err error = nil
				ctx := context.Background()
				totalBalance, err = uc.assetRepo.TotalJoinBalance(ctx, userAsset)
				if err != nil {
					return fmt.Errorf("TotalJoinBalance() error: err=%v", err)
				}
				rate = priceRepo.SpotURate(ctx, quote)
				if rate.IsZero() {
					return errors.New("CloseShortPos close rate is zero") // todo Code992408 由于已经联调下次修改需要更新
				}
			}

			// 体验金?
			// 全仓保证金中不包含体验金保证金，所以穿仓需要用体验金的保证金填补一次
			// if asset.ShortPos.TrialMargin.GreaterThan(decimal.Zero) && totalBalance.Sign() < 0 {
			//	if totalBalance.Abs().LessThanOrEqual(asset.ShortPos.TrialMargin.Mul(rate)) {
			//		totalBalance = totalBalance.Add(totalBalance.Neg())
			//		asset.ShortPos.TrialMargin = asset.ShortPos.TrialMargin.Add(totalBalance.Div(rate))
			//	} else {
			//		totalBalance = totalBalance.Add(asset.ShortPos.TrialMargin.Mul(rate))
			//		asset.ShortPos.TrialMargin = decimal.Zero
			//	}
			// }

			// 全身加起来还是负的, 平台爸爸补
			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseShortPos()", bankruptAmount))
				userAsset.ShortPos.Subsidy = userAsset.ShortPos.Subsidy.Add(bankruptAmount)

				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"subsidy":        userAsset.ShortPos.Subsidy,
				}).Info("tp0803 close cross total balance < 0")
			}
		}
	}

	// 平仓后如果仓位归0, 生成历史持仓记录
	if userAsset.ShortPos.Pos.Sign() <= 0 {
		// reply.ClearPos = asset.ShortPos
		userAsset.ShortPos.Clear() // 清空现有持仓
	}

	// 更新缓存
	contractCode := util.ContractCode(base, quote)
	ctx := context.Background()
	err = uc.cacheRepo.UpdateShortPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateShortPos() error: err=%v", err)
	}

	// 非机器人用户发送胜率,总收益,总平仓仓位价值数据
	// if !domain.RobotUsers.HasKey(slf.UID) {
	// 	tempProfit := compareProfitReal.Sub(compareFee.Mul(decimal.NewFromInt(2)))
	// 	if tempProfit.Sign() > 0 {
	// 		go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
	// 			UID: slf.UID, Times: 1, WinTimes: 1, ContractCode: slf.ContractCode(),
	// 			TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
	// 		})
	// 	} else {
	// 		go redislib.Redis().LPush(domain.SyncListUserWinRate, entity.UserWinRate{
	// 			UID: slf.UID, Times: 1, ContractCode: slf.ContractCode(),
	// 			TotalProfit: tempProfit, TotalClose: slf.Price.Mul(slf.Amount), TotalCloseMargin: closePosMargin,
	// 		})
	// 	}
	// }

	// 撤单 ?
	// go func() {
	// 	// 平仓后亏损, 账户余额不够冻结金额, 进行撤单操作
	// 	totalBalance := asset.CBalance(trade.FeeCurrency)
	// 	if asset.AssetMode == domain.AssetMode {
	// 		totalBalance, err = asset.TotalJoinBalance(pCache)
	// 		if err != nil {
	// 			log.Println("CloseShortPos revoke", err)
	// 		}
	// 	}
	// 	if totalBalance.LessThan(slf.FrozenTotal(asset, slf.Quote)) {
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, "", "", domain.Open, 0, 0, int32(domain.CancelTypeClosePosLoss), 1)
	// 	}
	// 	// 平仓后, 如果仓位为0, 需要撤销止赢止损平仓单
	// 	if asset.ShortPos.Pos.Sign() <= 0 {
	// 		RemoveRivalScore(asset.ShortPos.UID, asset.ShortPos.ContractCode, asset.ShortPos.PosSide, false) // 删除对手方评分
	// 		match.Service.ConditionCancel(asset.ShortPos.UID, slf.Base, slf.Quote, domain.Close, domain.Buy, asset.ShortPos.MarginMode, int32(domain.CancelTypeClosePosAll), 1)
	// 	}
	// }()

	// 账本记录?
	// go func() {
	// 	if trade.UserType == domain.UserTypePlatformRobot {
	// 		return
	// 	}
	// 	balance, err := GetCurrencyTotalBalance(trade.UID, bFeeParam.Currency)
	// 	if err != nil {
	// 		logrus.Errorf("CloseShortPos GetCurrencyTotalBalance uid: %s currency: %s error: %v", trade.UID, slf.Quote, err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, billProfitReal, balance.Add(trade.Fee))
	// 	es.SaveLedgerDetail(trade.UID, slf.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.FUTURE_TRADE_TYPE, trade.Fee.Neg(), balance)
	// }()

	// 返回?
	// reply.LogPos = slf.NewLogPosSync(asset.ShortPos, slf.OperateTime, slf.TradeId, trade.OrderId, trade.Side, trade.PosSide, slf.Amount, profitReal)

	// reply.Reply = payload.Reply{
	// 	UID:          asset.UID,
	// 	PosId:        asset.ShortPos.PosId,
	// 	Pos:          asset.ShortPos.Pos,
	// 	OpenPriceAvg: asset.ShortPos.OpenPriceAvg,
	// 	PosSide:      asset.ShortPos.PosSide,
	// 	ProfitReal:   profitReal,
	// 	FeeDetail:    feeDetails,
	// 	HaveTrial:    haveTrial,
	// }

	return nil
}

// CloseBothPos implements usecase.PositionUseCase.
func (uc *PositionUseCase) CloseBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, amount decimal.Decimal, fee decimal.Decimal, frozenMargin decimal.Decimal, changelog *[]string) error {
	price := accountSettleParam.GetPrice()
	//amount := accountSettleParam.GetAmount()
	uid := accountSettleParam.GetUID()
	base, quote := accountSettleParam.GetBaseQuote()
	//feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	// if err != nil {
	// 	return fmt.Errorf("getSettlementFeeRate() error: err=%v", err)
	// }

	// 计算平仓盈亏
	profitReal := userAsset.BothPos.CalcProfitReal(price, amount)

	// 更新仓位数
	userAsset.BothPos.Pos = userAsset.BothPos.Pos.Sub(amount)
	*changelog = append(*changelog, fmt.Sprintf("asset:pos amount:%v from:CloseBothPos()", amount.Neg()))

	// 检查仓位是否合法
	if userAsset.BothPos.Pos.Sign() < 0 {
		return fmt.Errorf("251111 pos is negative after closing both: pos=%v amount=%v", userAsset.BothPos.Pos, amount)
	}

	// 检查杠杆是否合法
	if userAsset.BothPos.Leverage < 1 {
		return errors.New("251114 both pos leverage < 1")
	}

	// 更新平仓盈亏记数
	userAsset.BothPos.ProfitReal = userAsset.BothPos.ProfitReal.Add(profitReal)

	billProfitReal := profitReal

	// 盈亏更新真金白银,余额变更
	if !billProfitReal.IsZero() {
		uc.BalanceAdd(billProfitReal, domain.BillTypeReal, userAsset, priceRepo, accountSettleParam)
		*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseBothPos()", billProfitReal, domain.BillTypeReal.String()))
	}

	// 扣手续费,余额变更
	uc.BalanceAdd(fee.Neg(), domain.BillTypeFee, userAsset, priceRepo, accountSettleParam)
	*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:%v from:CloseBothPos()", fee.Neg(), domain.BillTypeFee.String()))

	userAsset.BothPos.LiquidationType = accountSettleParam.GetLiquidationType()

	// 处理仓位保证金
	closePosMargin := decimal.Zero
	if userAsset.BothPos.Isolated() {
		// 逐仓平仓需要扣减仓位保证金数量计算，由于手续费、盈亏直接在IsolatedMargin已修改，所以要使用原始保证金originPosIsolateMargin计算
		closePosMargin = amount.Div(userAsset.BothPos.Pos).Mul(userAsset.BothPos.IsolatedMargin).Truncate(domain.CurrencyPrecision)

		subDeltaAmount := decimal.NewFromInt(0).Sub(closePosMargin.Sub(fee))

		// pos.IsolatedMargin 不可能小于0，subMargin 最大为当前仓位保证金数量
		userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(subDeltaAmount)
		*changelog = append(*changelog, fmt.Sprintf("asset:margin amount:%v type:isolated from:CloseBothPos()", subDeltaAmount))

		if userAsset.BothPos.LiquidationType != domain.LiquidationTypeBurst {

			// 平仓穿仓逻辑判断：因为盈亏和手续费在上面逻辑已经对IsolatedMargin进行更新，所以逐仓非爆仓情况，平仓的保证金如果不够扣的话为平穿
			if userAsset.BothPos.IsolatedMargin.Sign() < 0 {
				bankruptAmount := userAsset.BothPos.IsolatedMargin.Abs() // 这个补贴就是保证金正数?
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 逐仓保证金加穿仓补贴的费用
				userAsset.BothPos.IsolatedMargin = userAsset.BothPos.IsolatedMargin.Add(bankruptAmount)
				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseBothPos()", bankruptAmount))

				userAsset.BothPos.Subsidy = userAsset.BothPos.Subsidy.Add(bankruptAmount)
				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"isolatedMargin": userAsset.BothPos.IsolatedMargin,
					"subsidy":        userAsset.BothPos.Subsidy,
				}).Info("tp0803 close isolated margin < 0")
			}
		}
	} else { // 全仓模式
		closePosMargin = price.Mul(amount).Div(decimal.NewFromInt(int64(userAsset.BothPos.Leverage))).Truncate(domain.CurrencyPrecision)

		// 全仓非爆仓情况, 平仓后仓位保证金为负数时, 平台补贴
		if userAsset.BothPos.LiquidationType != domain.LiquidationTypeBurst {
			// 单一资产模式
			totalBalance := userAsset.CBalance(quote)
			rate := decimal.NewFromInt(1) // quote不是U时, 汇率还是1?
			// 联合资产模式
			if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI { // Todo 这里要确认AssetMode
				var err error = nil
				ctx := context.Background()
				totalBalance, err = uc.assetRepo.TotalJoinBalance(ctx, userAsset)
				if err != nil {
					return fmt.Errorf("TotalJoinBalance() error: err=%v", err)
				}
				rate = priceRepo.SpotURate(ctx, quote)
				if rate.IsZero() {
					return errors.New("CloseBothPos close rate is zero") // todo Code992408 由于已经联调下次修改需要更新
				}
			}

			// 全身加起来还是负的, 平台爸爸补
			if totalBalance.Sign() < 0 {
				bankruptAmount := totalBalance.Div(rate).Abs()
				// 生成穿仓补贴账单
				_ = uc.generatedBillLog(bankruptAmount, domain.BillTypeCloseSubsidy, accountSettleParam)

				// 账户余额需要加上穿仓补贴的费用
				userAsset.AddBalance(quote, bankruptAmount)
				*changelog = append(*changelog, fmt.Sprintf("asset:balance amount:%v type:bankrupt from:CloseBothPos()", bankruptAmount))

				userAsset.BothPos.Subsidy = userAsset.BothPos.Subsidy.Add(bankruptAmount)

				// 记录仓位的穿仓补贴
				logrus.WithFields(logrus.Fields{
					"tradeid":        accountSettleParam.GetTradeId(),
					"uid":            uid,
					"bankruptAmount": bankruptAmount,
					"subsidy":        userAsset.BothPos.Subsidy,
				}).Info("tp0803 close cross total balance < 0")
			}
		}
	}

	// 平仓后如果仓位归0, 生成历史持仓记录
	if userAsset.BothPos.Pos.Sign() <= 0 {
		// reply.ClearPos = asset.BothPos
		userAsset.BothPos.Clear() // 清空现有持仓
	}

	// 更新缓存
	contractCode := util.ContractCode(base, quote)
	ctx := context.Background()
	err := uc.cacheRepo.UpdateBothPos(ctx, contractCode, userAsset)
	if err != nil {
		return fmt.Errorf("UpdateBothPos() error: err=%v", err)
	}
	return nil
}

// BalanceAdd 处理余额变更逻辑
func (uc *PositionUseCase) BalanceAdd(amount decimal.Decimal, billType domain.BillType, userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam) error {
	billAssetLogs := make([]repository.BillAssetSync, 0)
	_, quote := accountSettleParam.GetBaseQuote()

	beforeBalance := userAsset.CBalance(quote)
	defer func() {
		afterBalance := userAsset.CBalance(quote)
		pc, _, line, ok := runtime.Caller(2)
		if !ok {
			return
		}
		parentFunc := runtime.FuncForPC(pc).Name()
		if idx := strings.LastIndex(parentFunc, "."); idx >= 0 {
			parentFunc = parentFunc[idx+1:]
		}
		logrus.WithFields(logrus.Fields{
			"tradeid":       accountSettleParam.AccountSettle.GetTradeId(),
			"uid":           userAsset.UID,
			"quote":         quote,
			"amount":        amount,
			"billType":      billType.String(),
			"beforeBalance": beforeBalance,
			"afterBalance":  afterBalance,
			"parentFunc":    parentFunc,
			"parentLine":    line,
		}).Info("tp0803 balance change after")
	}()

	// amount = 0 啥也不干
	if amount.IsZero() {
		return nil
	}

	// amount 大于0 (得加钱)
	if amount.Sign() > 0 {
		userAsset.AddBalance(quote, amount)
		billAssetLogs = append(billAssetLogs, uc.generatedBillLog(amount, billType, accountSettleParam))
		return nil
	}

	// amount 负数当然扣他钱
	// 单一保证金
	if userAsset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_SINGLE {
		// 不跳过体验金
		// if !skipTrial {
		// 	trialBalance := asset.TrialCBalance(p.Currency)
		// 	if trialBalance.IsPositive() {
		// 		if afterBalance := trialBalance.Add(p.Amount); afterBalance.LessThan(decimal.Zero) {
		// 			// 体验金不够扣
		// 			trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialBalance)
		// 			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 			slf.OnTrialBalanceAdd(p, &res, trialBalance.Neg(), p.Currency)
		// 			p.Amount = afterBalance
		// 		} else {
		// 			// 体验金够扣
		// 			trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
		// 			res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 			slf.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
		// 			return
		// 		}
		// 	}
		// }

		userAsset.AddBalance(quote, amount)
		billAssetLogs = append(billAssetLogs, uc.generatedBillLog(amount, billType, accountSettleParam))
	} else { // 多币种扣减?

		// 体验金逻辑
		// if !skipTrial {
		// 	trialCBalance := asset.TrialCBalance(p.Currency)
		// 	// 当前币种余额够扣减
		// 	if trialCBalance.Add(p.Amount).IsPositive() {
		// 		trialAssetList := asset.ConsumeTrialBalance(p.Currency, p.Amount.Neg())
		// 		res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 		slf.OnTrialBalanceAdd(p, &res, p.Amount, p.Currency)
		// 		return
		// 	}

		// 	// 当前币种余额不够 有多少扣多少
		// 	if trialCBalance.IsPositive() {
		// 		trialAssetList := asset.ConsumeTrialBalance(p.Currency, trialCBalance)
		// 		res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 		slf.OnTrialBalanceAdd(p, &res, trialCBalance.Neg(), p.Currency)
		// 	}

		// 	// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
		// 	trialCBalance = decimal.Max(decimal.NewFromInt(0), trialCBalance)
		// 	// amount 是负数
		// 	p.Amount = p.Amount.Add(trialCBalance)
		// 	for _, v := range domain.CurrencyList {
		// 		if v == p.Currency {
		// 			continue
		// 		}
		// 		rate := pCache.SpotRate(v, p.Currency)
		// 		vFee, _ := util.RoundCeil(p.Amount.Div(rate), domain.CurrencyPrecision)
		// 		vTrialBalance := asset.TrialCBalance(v)
		// 		// 当前币种余额够扣减
		// 		if vTrialBalance.GreaterThan(decimal.Zero) {
		// 			if vTrialBalance.Add(vFee).IsPositive() {
		// 				trialAssetList := asset.ConsumeTrialBalance(v, vFee.Neg())
		// 				res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 				slf.OnTrialBalanceAdd(p, &res, vFee, v)
		// 				return
		// 			} else {
		// 				trialAssetList := asset.ConsumeTrialBalance(v, vTrialBalance)
		// 				res.TrialAssetLogs = append(res.TrialAssetLogs, trialAssetList...)
		// 				slf.OnTrialBalanceAdd(p, &res, vTrialBalance.Neg(), v)
		// 				p.Amount = p.Amount.Add(vTrialBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
		// 			}
		// 		}
		// 	}
		// }

		cBalance := userAsset.CBalance(quote)
		// 当前币种余额够扣减
		if cBalance.Add(amount).IsPositive() {
			userAsset.AddBalance(quote, amount)
			billAssetLogs = append(billAssetLogs, uc.generatedBillLog(amount, billType, accountSettleParam))
			return nil
		}

		// 当前币种余额不够 有多少扣多少
		if cBalance.IsPositive() {
			userAsset.AddBalance(quote, cBalance.Neg())
			billAssetLogs = append(billAssetLogs, uc.generatedBillLog(cBalance.Neg(), billType, accountSettleParam))
		}

		// 原始余额为负数时候，取0，下面才能计算出正确扣减数量
		cBalance = decimal.Max(decimal.NewFromInt(0), cBalance)
		// amount 是负数!
		amount = amount.Add(cBalance)
		for _, v := range domain.CurrencyList {
			if v == quote {
				continue
			}
			ctx := context.Background()
			rate := priceRepo.SpotRate(ctx, v, quote)
			vFee, _ := util.RoundCeil(amount.Div(rate), domain.CurrencyPrecision)
			vBalance := userAsset.CBalance(v)
			// 当前币种余额够扣减
			if vBalance.GreaterThan(decimal.Zero) {
				if vBalance.Add(vFee).IsPositive() {
					userAsset.AddBalance(v, vFee)
					billAssetLogs = append(billAssetLogs, uc.generatedBillLog(vFee, billType, accountSettleParam))
					return nil
				} else {
					userAsset.AddBalance(v, vBalance.Neg())
					billAssetLogs = append(billAssetLogs, uc.generatedBillLog(vBalance.Neg(), billType, accountSettleParam))
					amount = amount.Add(vBalance.Mul(rate).Truncate(domain.CurrencyPrecision))
				}
			}
		}

		// 当前币种都已经看过了 发现还没有扣完需要继续扣 以便穿仓补贴
		if !amount.IsZero() {
			userAsset.AddBalance(quote, amount)
			billAssetLogs = append(billAssetLogs, uc.generatedBillLog(amount, billType, accountSettleParam))
			return nil
		}
	}

	return nil
}

func (uc *PositionUseCase) COBothPos(userAsset *repository.AssetSwap, priceRepo repository.PriceRepository, accountSettleParam utils.AccountSettleParam, changelog *[]string) error {
	// 方向不一致, 只要方向相反的操作, 视为先平(Close)旧的仓位, 看情况可能还要再开(Open)新的仓位, 所以我叫COBoth
	// 今天开会Tilda说单向仓没有平的概念, 但是我们后台可以把反方向上的数量增加看成平仓处理
	amount := accountSettleParam.GetAmount()
	side := accountSettleParam.GetSide()
	price := accountSettleParam.GetPrice()
	leverage := accountSettleParam.GetLeverage()

	// 总手续费计算, 后面会分比例
	feeRate, err := uc.getSettlementFeeRate(accountSettleParam)
	fee := amount.Mul(price).Mul(feeRate)

	// 总解冻金计算, 后面会分比例
	unfrozenMargin, err := util.RoundCeil(amount.Mul(price).Div(decimal.NewFromInt(int64(leverage))), domain.CurrencyPrecision)
	if err != nil {
		return fmt.Errorf("252201 RoundCeil() error: err=%v", err)
	}

	// 单向时卖置为负数?
	if side == domain.Sell {
		amount = amount.Neg()
	}

	closeAmount := amount                 // 要平的量
	closeFee := fee                       // 平的手续费
	closeUnfrozenMargin := unfrozenMargin // 平的解冻保证金
	openAmount := decimal.Zero            // 要开的量
	openFee := decimal.Zero               // 开的手续费
	openUnfrozenMargin := decimal.Zero    // 开的解冻保证金

	if amount.Abs().GreaterThan(userAsset.BothPos.Pos.Abs()) { // 超过了, 得准备再开反向仓位了
		closeAmount = userAsset.BothPos.Pos.Neg() // 目前全部要平的数
		closeRate := closeAmount.Div(amount).Abs()
		// 手续费和解冻保证金按比例分配
		closeFee = fee.Mul(closeRate).Abs().Truncate(domain.CurrencyPrecision)
		closeUnfrozenMargin = unfrozenMargin.Mul(closeRate).Abs().Truncate(domain.CurrencyPrecision)
		// Pos和amount符号相反, 差值则是要重新开仓的量
		openAmount = userAsset.BothPos.Pos.Add(amount)
	}

	uc.CloseBothPos(userAsset, priceRepo, accountSettleParam, closeAmount, closeFee, closeUnfrozenMargin, changelog)

	// 再开反向仓位
	if accountSettleParam.IsOpenPosition() && !openAmount.IsZero() {
		openFee := fee.Sub(closeFee)
		openUnfrozenMargin = unfrozenMargin.Sub(closeUnfrozenMargin)
		uc.OpenBothPos(userAsset, priceRepo, accountSettleParam, openAmount, openFee, openUnfrozenMargin, changelog)
	}

	logrus.WithFields(logrus.Fields{
		"tradeid":             accountSettleParam.GetTradeId(),
		"orderid":             accountSettleParam.GetOrderID(),
		"uid":                 userAsset.UID,
		"closeAmount":         closeAmount.String(),
		"closeFee":            closeFee.String(),
		"closeUnfrozenMargin": closeUnfrozenMargin.String(),
		"openAmount":          openAmount.String(),
		"openFee":             openFee.String(),
		"openUnfrozenMargin":  openUnfrozenMargin.String(),
	}).Info("Close then Open Position")

	return nil
}

func (uc *PositionUseCase) generatedBillLog(amount decimal.Decimal, billType domain.BillType, accountSettleParam utils.AccountSettleParam) repository.BillAssetSync {
	uid := accountSettleParam.GetUID()
	orderId := accountSettleParam.GetUID()
	_, quote := accountSettleParam.GetBaseQuote()

	billSwap := repository.BillAssetSync{
		BillAsset: entity.BillAsset{
			BillID:     uc.gen.Generate().Int64(),
			UID:        uid,
			RefID:      orderId,
			Currency:   strings.ToUpper(quote),
			BillType:   int(billType),
			Amount:     amount,
			CreateTime: time.Now().UnixNano(),
		},
	}

	return billSwap
}

func (p *PositionUseCase) getSettlementFeeRate(accountSettleParam utils.AccountSettleParam) (decimal.Decimal, error) {
	settleType := accountSettleParam.GetAccountSettleType()
	switch settleType {
	case "taker":
		return accountSettleParam.GetFeeRateTaker(), nil
	case "maker":
		return accountSettleParam.GetFeeRateMaker(), nil
	default:
		return decimal.Zero, fmt.Errorf("unsupported settle type: %s", settleType)
	}
}

// CalcUnfrozenMargin 计算解冻保证金
func (uc *PositionUseCase) calcUnfrozenMargin(accountSettleParam utils.AccountSettleParam) (decimal.Decimal, error) {
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	leverage := accountSettleParam.GetLeverage()

	// 检查杠杆是否合法
	if leverage <= 0 {
		return decimal.Zero, fmt.Errorf("invalid leverage: %d", leverage)
	}

	unfrozenMargin, err := util.RoundCeil(amount.Mul(price).Div(decimal.NewFromInt(int64(leverage))), domain.CurrencyPrecision)
	if err != nil {
		return decimal.Zero, err
	} else {
		return unfrozenMargin, nil
	}

	// 取消订单解冻保证金
	// orderType := domain.FutureOrderType(accountSettleParam.GetOrderType())

	// // 根据订单类型计算解冻保证金
	// if orderType == domain.OrderTypeMarket {
	// 	// 市价单: UnfilledVolume/Leverage
	// 	unfilledVolume := accountSettleParam.GetUnfilledVolume()
	// 	return unfilledVolume.Div(decimal.NewFromInt(int64(leverage))), nil
	// } else if orderType == domain.OrderTypeLimit {
	// 	// 限价单: price * GetUnfilledAmount / Leverage
	// 	price := accountSettleParam.GetPrice()
	// 	unfilledAmount := accountSettleParam.GetUnfilledAmount()
	// 	return price.Mul(unfilledAmount).Div(decimal.NewFromInt(int64(leverage))), nil
	// } else {
	// 	return decimal.Zero, fmt.Errorf("unsupported order type: %d", orderType)
	// }
}
