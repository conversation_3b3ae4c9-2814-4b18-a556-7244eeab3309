package usecase

import (
	"context"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/util"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// OptionUseCaseParam 期权用例参数
type OptionUseCaseParam struct {
	dig.In

	Config *cfg.Config[configs.Config] `name:"config"`
	Rdb    *redis.ClusterClient        `name:"redis-cluster"`
	RS     *redsync.Redsync            `name:"rs"`

	OptionRepo repository.OptionRepository
	CacheRepo  repository.CacheRepository
	AssetRepo  repository.AssetRepository
}

// OptionUseCase 期权用例
type OptionUseCase struct {
	config *cfg.Config[configs.Config]
	rs     *redsync.Redsync
	rdb    *redis.ClusterClient

	optionRepo repository.OptionRepository
	cacheRepo  repository.CacheRepository
	assetRepo  repository.AssetRepository
}

// NewOptionUseCase 创建期权用例实例
func NewOptionUseCase(param OptionUseCaseParam) usecase.OptionUseCase {
	return &OptionUseCase{
		config: param.Config,
		rs:     param.RS,
		rdb:    param.Rdb,

		optionRepo: param.OptionRepo,
		cacheRepo:  param.CacheRepo,
		assetRepo:  param.AssetRepo,
	}
}

// Asset implements usecase.OptionUseCase.
func (use *OptionUseCase) Asset(ctx context.Context, param *usecase.CurrencyParam) (usecase.OptionAssetReply, error) {
	// if len(param.Currency) == 0 {
	// 	param.Currency = domain.CurrencyUSDT
	// }
	// var (
	// 	reply = usecase.OptionAssetReply{}
	// 	total = &entity.OptionTotalProfit{UID: param.UID, Currency: param.Currency}
	// 	key   = util.GetTotalProfitRedisKey(param.UID, param.Currency)
	// )
	// result, err := use.rdb.HGet(domain.OptionTotalProfitHash, key).Result()
	// if (err != nil && err != redis.Nil) || len(result) == 0 {
	// 	if err = total.GetData(param.UID, param.Currency); err != nil && err != gorm.ErrRecordNotFound {
	// 		logrus.Errorf("get user option asset err:%+v param:%+v", err, param)
	// 		return reply, err
	// 	}
	// 	return reply, nil
	// } else {
	// 	_ = json.Unmarshal([]byte(result), &total)
	// }
	// reply.Unexercised = total.Unexercised
	// reply.TotalProfit = total.TotalProfit
	// reply.TotalLoss = total.TotalLoss

	// return reply, nil
	return usecase.OptionAssetReply{}, nil
}

// Exercise implements usecase.OptionUseCase.
func (use *OptionUseCase) Exercise(ctx context.Context, param *usecase.ExerciseParam) ([]*usecase.OptionTradeReply, error) {
	// var (
	// 	reply            = make([]*usecase.OptionTradeReply, 0)
	// 	profitReal       = decimal.Zero                         // 期权盈利
	// 	totalCancel      = decimal.Zero                         // 期权撤单总额
	// 	assetLogs        = make([]*repository.MqCmsAsset, 0)    // 财务后台资金流水
	// 	billLogs         = make([]repository.BillOptionSync, 0) // 前台用户账单
	// 	optionKey        = util.OptionSubKey(param.Base, param.Quote, param.OptionId)
	// 	unprocessed      = make([]repository.Option, 0)
	// 	totalPremiumLoss = decimal.Zero // 权利金+手续费
	// )

	// // 记录流水
	// userOptions, err := use.optionRepo.LoadUserOption(ctx, param.UID, param.OptionType)
	// if err != nil {
	// 	return nil, err
	// }
	// for _, option := range userOptions {
	// 	processed := false
	// 	for _, order := range param.Orders {
	// 		if option.OrderId == order.OrderId && order.State == domain.OptionStateEexercised {
	// 			if order.ProfitReal.Sign() > 0 {
	// 				profitReal = profitReal.Add(order.ProfitReal)
	// 			}
	// 			if order.ProfitReal.Sign() >= 0 {
	// 				realLog := modelutil.NewExerciseRecord(param.ExerciseBase, option, domain.BillTypeOptionProfitReal, order.ProfitReal, order)
	// 				assetLogs = append(assetLogs, realLog)
	// 			}
	// 			feeLog := modelutil.NewExerciseRecord(param.ExerciseBase, option, domain.BillTypeOptionRealFee, option.Fee, order)
	// 			feeLog.Amount = feeLog.Amount.Neg() // 实际产生手续费给负数
	// 			assetLogs = append(assetLogs, feeLog)

	// 			realBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, domain.BillTypeOptionProfitReal, util.AccountType(order.AccountType), param.OptionType, order.ProfitReal, order.Premium, param.OptionId, order.OrderId)
	// 			billLogs = append(billLogs, realBill)
	// 			reply = append(reply, &usecase.OptionTradeReply{
	// 				OrderId: order.OrderId,
	// 			})
	// 			processed = true
	// 			totalPremiumLoss = totalPremiumLoss.Add(order.Premium).Add(order.Fee)
	// 			break
	// 		}
	// 		if option.OrderId == order.OrderId && order.State == domain.OptionStateCanceled {
	// 			totalCancel = totalCancel.Add(order.Premium).Add(order.Fee)
	// 			premiumLog := modelutil.NewExerciseRecord(param.ExerciseBase, option, domain.BillTypeOptionPremiumReturn, option.Premium, order)
	// 			feeLog := modelutil.NewExerciseRecord(param.ExerciseBase, option, domain.BillTypeOptionFeeReturn, option.Fee, order)
	// 			assetLogs = append(assetLogs, premiumLog, feeLog)

	// 			realBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, domain.BillTypeOptionPremiumReturn, util.AccountType(order.AccountType), param.OptionType, order.Premium, decimal.Zero, param.OptionId, order.OrderId)
	// 			logBill := *repository.NewBillOptionExerciseSync(param.UID, param.Base, param.Quote, domain.BillTypeOptionFeeReturn, util.AccountType(order.AccountType), param.OptionType, order.Fee, decimal.Zero, param.OptionId, order.OrderId)
	// 			billLogs = append(billLogs, realBill, logBill)
	// 			reply = append(reply, &usecase.OptionTradeReply{
	// 				OrderId: order.OrderId,
	// 			})
	// 			processed = true

	// 			break
	// 		}
	// 	}
	// 	if !processed {
	// 		unprocessed = append(unprocessed, option)
	// 	}
	// }

	// // 锁定用户合约账户
	// takerMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.SetExpiry(30*time.Second))
	// if takerMutex.Lock() != nil {
	// 	return reply, domain.ErrLockPos
	// }
	// defer takerMutex.Unlock()
	// // 获取资产
	// userAsset, err := use.cacheRepo.Load(ctx, param.UID, domain.CurrencyUSDT)
	// if err != nil {
	// 	return reply, err
	// }
	// // 增加合约账户金额
	// userAsset.AddBalance(param.Quote, profitReal.Add(totalCancel))
	// err = use.cacheRepo.UpdateBalance(ctx, userAsset)
	// if err != nil {
	// 	return reply, errors.Wrap(err, "update balance through option user: "+param.UID)
	// }

	// if len(unprocessed) == 0 {
	// 	// 删除用户期权记录
	// 	if err = use.optionRepo.RemoveOption(ctx, param.UID, param.OptionType); err != nil {
	// 		logrus.Errorf("RemoveOption err:%s, optionKey:%s", err, optionKey)
	// 	}
	// } else {
	// 	// 更新用户期权
	// 	if err = use.optionRepo.UpdateOption(ctx, param.UID, param.OptionType, optionKey, unprocessed); err != nil {
	// 		logrus.Errorf("UpdateOption err:%s, optionKey:%s", err, optionKey)
	// 	}
	// }

	// // TODO 同步流水到data-center

	// // go func() {
	// // 	assetMsg := message.AssetMsg{
	// // 		Currency: param.Quote,
	// // 		Balance:  userAsset.CBalance(param.Quote),
	// // 		Frozen:   userAsset.Frozen,
	// // 	}
	// // 	message.New(param.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
	// // 		MsgType: message.AccountMsgTypeAsset,
	// // 		MsgData: assetMsg,
	// // 	})
	// // 	if len(assetLogs) > 0 {
	// // 		client := redislib.Redis()
	// // 		coupling.AddOptionBills(client, billLogs...)
	// // 		coupling.AddAssetLogs(client, assetLogs...)
	// // 	}
	// // }()

	// // TODO 账本统计

	// // diffAmount := totalPremiumLoss.Sub(profitReal.Add(totalCancel))
	// // go func() {
	// // 	balance, err := swapcache.GetCurrencyTotalBalance(req.UID, req.Quote)
	// // 	if err != nil {
	// // 		logrus.Errorf("Exercise swapcache.GetCurrencyTotalBalance err: %v", err)
	// // 		return
	// // 	}
	// // 	es.SaveLedgerDetail(req.UID, req.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.TRADE_LEDGER_TYPE, es.OPTION_ATRADE_TYPE, diffAmount, balance)
	// // }()

	// return reply, nil

	return nil, nil
}

// InitOption implements usecase.OptionUseCase.
func (use *OptionUseCase) InitOption(ctx context.Context, param *usecase.UIDParam) error {
	// hash := domain.AllOptionUsersKey()
	// if err := use.rdb.HSet(hash, param.UID, "{}").Err(); err != nil {
	// 	logrus.Errorf("init option users hash:%+v,err:%+v, param:%+v", hash, err, param)
	// }

	// // 初始化盈亏记录
	// profitTime := util.OneDayBeginAndEndTimeStamp(time.Now())
	// profitTimeStr := strconv.FormatInt(profitTime, 10)
	// if err := use.rdb.SAdd(domain.OptionProfitLossDate, profitTimeStr).Err(); err != nil {
	// 	logrus.Errorf("SAdd OptionProfitDate:%+v, err:%+v", profitTimeStr, err)
	// }

	// profitHashKey := domain.OptionProfitLossData.Key(profitTimeStr)
	// optionPnl := entity.OptionProfitLoss{
	// 	UID:         param.UID,
	// 	Currency:    domain.CurrencyUSDT,
	// 	OperateTime: profitTime,
	// }
	// optionPnl.CreateTime = time.Now().UnixNano()
	// hashKey := optionPnl.UID + util.ProfitLossBassKey(domain.CurrencyUSDT)
	// pnl, _ := json.Marshal(&optionPnl)
	// if err := use.rdb.HSet(profitHashKey, hashKey, string(pnl)).Err(); err != nil {
	// 	logrus.Errorf("err create option profit redis hash:%s, key:%s, data:%+v", profitHashKey, hashKey, pnl)
	// }

	// return nil

	return nil
}

// Trade implements usecase.OptionUseCase.
func (use *OptionUseCase) Trade(ctx context.Context, param *usecase.OptionTradeParam) ([]*usecase.OptionTradeReply, error) {
	var (
		reply       = make([]*usecase.OptionTradeReply, 0)
		totalAmount = decimal.Zero
		options     = make(map[string]repository.Option)
		totalFee    = decimal.Zero
	)
	// 计算总划转金额
	for _, order := range param.Orders {
		totalAmount = totalAmount.Add(order.Premium).Add(order.Fee)
		totalFee = totalFee.Add(order.Fee)
		subKey := util.OptionSubKey(param.Base, param.Quote, order.OptionId)
		options[subKey] = repository.Option{
			OrderId: order.OrderId,
			Premium: order.Premium,
			Fee:     order.Fee,
		}
		reply = append(reply, &usecase.OptionTradeReply{
			OrderId: order.OrderId,
		})
	}

	// 锁定用户合约账户
	takerMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if takerMutex.Lock() != nil {
		return reply, domain.ErrLockPos
	}
	defer takerMutex.Unlock()

	// 计算账户可用金额
	userAsset, err := use.cacheRepo.Load(ctx, param.UID, "", domain.CurrencyUSDT)
	if err != nil {
		return reply, err
	}

	// userCache := swapcache.NewPosCache(swapcache.CacheParam{
	// 	TradeCommon: repository.TradeCommon{
	// 		Quote: domain.CurrencyUSDT,
	// 	},
	// }, param.UID)
	// // 获取资产
	// userAsset, err := userCache.Load()
	// if err != nil {
	// 	return reply, err
	// }
	// 最大可转出金额判断
	optionsMarginBalance, err := use.assetRepo.OptionsMarginBalance(ctx, userAsset, param.UID)
	if err != nil {
		logrus.Error("Trade OptionsMarginBalance error:", err)
		return reply, err
	}
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "optionsMarginBalance", optionsMarginBalance)

	userAsset.Available = optionsMarginBalance.
		Sub(use.assetRepo.HoldCostTotalCross(ctx, userAsset, domain.CurrencyUSDT)).
		Sub(use.assetRepo.FrozenTotal(ctx, userAsset, domain.CurrencyUSDT))
	totalCrossMargin, _, _, _, err := use.assetRepo.TotalCrossMaintainMargin(ctx, userAsset, "")

	logrus.Info(0, "------------------- OptionService Trade", param.UID, "HoldCostTotalCross", use.assetRepo.HoldCostTotalCross(ctx, userAsset, domain.CurrencyUSDT))
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "FrozenTotal", use.assetRepo.FrozenTotal(ctx, userAsset, domain.CurrencyUSDT))
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "Available", userAsset.Available)
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "totalCrossMargin", totalCrossMargin)

	if err != nil {
		logrus.Error("Trade totalCrossMargin error:", err)
		return reply, err
	}
	tempAmount := userAsset.CBalance(param.Quote).
		Sub(use.assetRepo.HoldCostTotalIsolated(ctx, userAsset, domain.CurrencyUSDT, true)).
		Sub(totalCrossMargin).
		Sub(use.assetRepo.FrozenTotal(ctx, userAsset, domain.CurrencyUSDT))

	logrus.Info(0, "------------------- OptionService Trade", param.UID, "CBalance", userAsset.CBalance(param.Quote))
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "HoldCostTotalIsolated", use.assetRepo.HoldCostTotalIsolated(ctx, userAsset, domain.CurrencyUSDT, true))
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "FrozenTotal", use.assetRepo.FrozenTotal(ctx, userAsset, domain.CurrencyUSDT))
	logrus.Info(0, "------------------- OptionService Trade", param.UID, "tempAmount", tempAmount)

	available := decimal.Min(tempAmount, userAsset.Available, userAsset.CBalance(param.Quote))

	logrus.Info(0, "------------------- OptionService Trade", param.UID, "available", available, "min", tempAmount, userAsset.Available, userAsset.CBalance(param.Quote))

	if available.LessThan(totalAmount) {
		return reply, errors.New(param.UID + " swap account available insufficient funds")
	}
	// 账户金额扣减
	userAsset.AddBalance(param.Quote, totalAmount.Neg())

	if userAsset.CBalance(param.Quote).Sign() < 0 {
		return reply, errors.New(param.UID + " swap account balance is negative after decr")
	}
	err = use.cacheRepo.UpdateBalance(ctx, userAsset)
	if err != nil {
		return reply, errors.Wrap(err, "decr balance from user: "+param.UID)
	}

	// TODO 记录流水

	// cache := optioncache.New(param.Base, param.Quote, param.UID, param.OptionType)
	// if err := cache.UpdateMultiOption(options); err != nil {
	// 	// revert contract asset
	// 	userAsset.AddBalance(req.Quote, totalAmount)
	// 	revokeErr := userCache.IncrOrDecr(userAsset)
	// 	if revokeErr != nil {
	// 		log.Printf("revert user asset in option trade err: %s", err.Error())
	// 	}
	// 	return domain.Code992405, reply, err
	// }

	//  TODO 同步流水到data-center

	// go func() {
	// 	assetMsg := message.AssetMsg{
	// 		Currency: req.Quote,
	// 		Balance:  userAsset.CBalance(req.Quote),
	// 		Frozen:   userAsset.Frozen,
	// 	}
	// 	message.New(req.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
	// 		MsgType: message.AccountMsgTypeAsset,
	// 		MsgData: assetMsg,
	// 	})
	// 	assetLogs, billLogs := modelutil.NewOptionRecord(req)
	// 	if len(assetLogs) > 0 || len(billLogs) > 0 {
	// 		client := redislib.Redis()
	// 		coupling.AddOptionBills(client, billLogs...)
	// 		coupling.AddAssetLogs(client, assetLogs...)
	// 	}
	// }()

	// TODO 账本统计

	// go func() {
	// 	balance, err := swapcache.GetCurrencyTotalBalance(req.UID, req.Quote)
	// 	if err != nil {
	// 		logrus.Errorf("Trade swapcache.GetCurrencyTotalBalance err: %v", err)
	// 		return
	// 	}
	// 	es.SaveLedgerDetail(req.UID, req.Quote, es.DERIVATIVES_ACCOUNT_TYPE, es.CHARGE_FOR_TROUBLE_LEDGER_TYPE, es.OPTION_ATRADE_TYPE, totalFee.Neg(), balance)
	// }()

	return reply, nil
}
