package usecase

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/delivery/event/message"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	ytSnowflake "futures-asset/internal/libs/snowflake"
	"futures-asset/util"

	"github.com/bwmarrin/snowflake"
	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"

	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	commonpb "yt.com/backend/common.git/business/grpc/gen/ws/v1"
	cfg "yt.com/backend/common.git/config"
)

// AssetUseCaseParam 资产用例参数
type AssetUseCaseParam struct {
	dig.In
	Config *cfg.Config[configs.Config] `name:"config"`

	RS *redsync.Redsync `name:"rs"`

	AssetRepo       repository.AssetRepository
	ProfitLossRepo  repository.ProfitLossRepository
	PriceRepo       repository.PriceRepository
	CacheRepo       repository.CacheRepository
	FormulaRepo     repository.FormulaRepository
	BurstRepo       repository.BurstRepository
	SettingRepo     repository.SettingRepository
	OrderRepo       repository.OrderRepository
	ProducerUseCase usecase.ProducerUseCase
}

// AssetUseCase 资产用例实现
type AssetUseCase struct {
	rs  *redsync.Redsync
	gen *snowflake.Node

	config          *cfg.Config[configs.Config]
	assetRepo       repository.AssetRepository
	profitLossRepo  repository.ProfitLossRepository
	priceRepo       repository.PriceRepository
	cacheRepo       repository.CacheRepository
	formulaRepo     repository.FormulaRepository
	burstRepo       repository.BurstRepository
	settingRepo     repository.SettingRepository
	orderRepo       repository.OrderRepository
	producerUseCase usecase.ProducerUseCase
}

// NewAssetUseCase 创建资产用例实例
func NewAssetUseCase(param AssetUseCaseParam) usecase.AssetUseCase {
	n, err := ytSnowflake.New()
	if err != nil {
		panic(fmt.Sprintf("NewOrderUseCase error: %v", err))
	}

	return &AssetUseCase{
		rs:  param.RS,
		gen: n,

		config:          param.Config,
		assetRepo:       param.AssetRepo,
		profitLossRepo:  param.ProfitLossRepo,
		priceRepo:       param.PriceRepo,
		cacheRepo:       param.CacheRepo,
		formulaRepo:     param.FormulaRepo,
		burstRepo:       param.BurstRepo,
		settingRepo:     param.SettingRepo,
		orderRepo:       param.OrderRepo,
		producerUseCase: param.ProducerUseCase,
	}
}

// LockAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) LockAsset(ctx context.Context, param *usecase.BatchLockAssetParam) ([]*futuresassetpb.AssetOperationResult, error) {
	if len(param.UID) <= 0 || len(param.Symbol) == 0 || param.Leverage <= 0 || len(param.Currency) <= 0 || len(param.Orders) <= 0 {
		return nil, usecase.ParamInvalidError{Msg: "param err"}
	}
	if param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_CROSS && param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
		return nil, usecase.ParamInvalidError{Msg: "margin mode err"}
	}

	for _, order := range param.Orders {
		if order.OrderID == "" || order.Amount.IsZero() || order.OrderTime <= 0 {
			return nil, usecase.ParamInvalidError{Msg: "order err"}
		}
	}

	// 如果用户在爆仓中, 不能进行挂单
	isBursting, _ := use.burstRepo.IsBursting(ctx, repository.CheckBurstParam{
		UID:        param.UID,
		MarginMode: param.MarginMode,
		IsTrialPos: param.IsTrial,
	})
	if isBursting && param.IsInnerCall != 1 {
		return nil, usecase.BrustingError{Msg: "user bursting"}
	}

	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return nil, errors.New("user lock err when recycle trial asset")
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Symbol, param.Currency)
	if err != nil {
		return nil, errors.Wrap(err, "load user asset")
	}

	leverage := asset.GetLeverage(param.Symbol)
	if leverage.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_UNSPECIFIED && leverage.MarginMode != param.MarginMode {
		return nil, usecase.ParamInvalidError{Msg: "margin mode"}
	}

	userLeverage := decimal.Zero
	switch param.PositionMode {
	case futuresassetpb.PositionMode_POSITION_MODE_ONE_WAY:
		userLeverage = decimal.NewFromInt(int64(leverage.BLeverage))

	default:
		switch leverage.MarginMode {
		case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
			userLeverage = decimal.NewFromInt(int64(leverage.Leverage))

		case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
			if param.PosSide == futuresassetpb.PosSide_POS_SIDE_LONG {
				userLeverage = decimal.NewFromInt(int64(leverage.LLeverage))
			} else if param.PosSide == futuresassetpb.PosSide_POS_SIDE_SHORT {
				userLeverage = decimal.NewFromInt(int64(leverage.SLeverage))
			}

		default:
			userLeverage = decimal.NewFromInt(int64(leverage.Leverage))
		}
	}
	if userLeverage.LessThanOrEqual(decimal.Zero) {
		return nil, usecase.ParamInvalidError{Msg: "leverage negative"}
	}

	maxPosValue, err := use.formulaRepo.GetMaxPosValueWithLeverage(ctx, param.Symbol, leverage.Leverage)
	if err != nil {
		return nil, errors.Wrap(err, "get max pos value")
	}
	if maxPosValue.IsZero() {
		return nil, errors.New("max pos value is zero")
	}

	items := make([]*futuresassetpb.AssetOperationResult, 0, len(param.Orders))
	for _, order := range param.Orders {
		replyItem := &futuresassetpb.AssetOperationResult{
			OrderId: order.OrderID,
		}

		markPrice := use.priceRepo.GetMarkPrice(ctx, param.Symbol)
		if maxPosValue.IsPositive() {
			frozenValue := asset.GetFrozenByCode(param.Symbol).Mul(userLeverage)
			if maxPosValue.LessThan(asset.ShortPos.CalcPosValue(markPrice).Add(asset.LongPos.CalcPosValue(markPrice)).
				Add(asset.BothPos.CalcPosValue(markPrice)).Add(order.Amount.Mul(userLeverage)).Add(frozenValue)) {
				replyItem.ErrorCode = int32(domain.ErrMaxPos)
				replyItem.ErrorMsg = "max pos limit"
				items = append(items, replyItem)

				continue
			}
		}

		available, err := use.assetRepo.GetAvailableBase(ctx, asset, param.MarginMode, param.Currency)
		if err != nil {
			return nil, errors.Wrap(err, "get available base")
		}
		if available.Sign() <= 0 {
			replyItem.ErrorCode = int32(domain.CodeInsufflateFunds)
			replyItem.ErrorMsg = "insufficient balance"
			items = append(items, replyItem)

			continue
		}

		originFrozen := order.Amount
		rate := decimal.NewFromInt(1)
		diff := available.Sub(originFrozen)
		if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
			// 联合保证金 需要转成 usdt
			rate = use.priceRepo.SpotURate(ctx, param.Currency)
			if rate.IsZero() {
				logrus.Infof("available: %s  leverage: %d rate: %s order.Amount: %s", available, leverage.Leverage, rate, order.Amount)

				replyItem.ErrorCode = int32(domain.CodeInsufflateFunds)
				replyItem.ErrorMsg = "spot rate is zero"
				items = append(items, replyItem)

				continue
			}

			usdtFrozen, _ := util.RoundCeil(originFrozen.Mul(rate), domain.CurrencyPrecision)
			diff = available.Sub(usdtFrozen)
			// if order.OrderType == futuresassetpb.OrderType_ORDER_TYPE_MARKET {
			// 	// 市价锁定, 资金不足则按照最大可锁定资金锁定
			// 	if diff.Sign() <= 0 {
			// 		codeSetting, err := use.settingRepo.GetCachePair(ctx, param.Symbol)
			// 		if err != nil {
			// 			return nil, err
			// 		}
			// 		minAmount := codeSetting.MinAmount
			// 		if available.Mul(userLeverage).LessThan(minAmount) {
			// 			return nil, errors.New("place min amount limit")
			// 		}
			// 		// TODO check
			// 		// param.Amount = available.Mul(userLeverage).Div(rate).Div(param.Price).Truncate(codeSetting.AmountPrecision)
			// 		// originFrozen, _ = util.RoundCeil(param.Amount.Mul(param.Price).Div(userLeverage).Mul(rate), domain.CurrencyPrecision)
			// 		// diff = available.Sub(usdtFrozen)
			// 	}
			// } else {
			// 	if diff.Sign() < 0 {
			// 		return nil, usecase.InsuffFundsError{Msg: "diff insufficient balance multi"}
			// 	}
			// 	// if available.Mul(userLeverage).Div(rate).Div(param.Price).LessThan(param.Amount) {
			// 	// 	return nil, errors.New("insufficient balance")
			// 	// }
			// 	if available.Div(rate).LessThan(param.Amount) {
			// 		return nil, usecase.InsuffFundsError{Msg: "limit order insufficient balance multi"}
			// 	}
			// }
		} else {
			// diff := available.Sub(originFrozen)
			// if param.OrderType == futuresassetpb.OrderType_ORDER_TYPE_MARKET {
			// 	// 市价锁定, 资金不足则按照最大可锁定资金锁定
			// 	if diff.Sign() <= 0 {
			// 		codeSetting, err := use.settingRepo.GetCachePair(ctx, param.Symbol)
			// 		if err != nil {
			// 			return nil, err
			// 		}
			// 		minAmount := codeSetting.MinAmount
			// 		logrus.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
			// 		if available.Mul(userLeverage).LessThan(minAmount) {
			// 			logrus.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s settingMinAmount: %s)",
			// 				available, minAmount, userLeverage, codeSetting.MinAmount)
			// 			return nil, errors.New("place min amount limit")
			// 		}
			// 		// TODO check
			// 		// param.Amount = available.Mul(userLeverage).Div(param.Price).Truncate(codeSetting.AmountPrecision)
			// 		// originFrozen, _ = util.RoundCeil(param.Amount.Mul(param.Price).Div(userLeverage), constvar.CurrencyPrecision)
			// 		// diff = available.Sub(originFrozen)
			// 	}
			// } else {
			// 	if diff.Sign() < 0 {
			// 		return nil, usecase.InsuffFundsError{Msg: "diff insufficient balance single"}
			// 	}
			// 	if available.LessThan(param.Amount) {
			// 		return nil, usecase.InsuffFundsError{Msg: "limit order insufficient balance single"}
			// 	}
			// }
		}
		if diff.Sign() < 0 {
			replyItem.ErrorCode = int32(domain.CodeInsufflateFunds)
			replyItem.ErrorMsg = "insufficient balance"
			items = append(items, replyItem)

			continue
		}

		logrus.Info(0, "================ OpenBuy.Lock [IncrFrozen]", param.UID, "originFrozen", originFrozen, "param", fmt.Sprintf("%+v", param), fmt.Sprintf("asset.Frozen %+v", asset.Frozen))
		asset.IncrFrozen(param.Symbol, originFrozen)
		logrus.Info(0, "================ OpenBuy.Lock [IncrFrozen]", param.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

		replyItem.Success = true
		items = append(items, replyItem)
	}

	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return nil, err
	}

	// TODO 资产异步存库
	// assetSwap := modelutil.NewLogAssetSync(asset, param.Quote, param.OperateTime)
	// go AddAssetLogs(redis, assetSwap) // wallet资产异步存库

	return items, nil
}

// UnLockAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) UnLockAsset(ctx context.Context, param *usecase.BatchUnlockParam) (*usecase.BatchUnlockReply, error) {
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return nil, errors.New("user lock err when recycle trial asset")
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Symbol, param.Currency)
	if err != nil {
		return nil, errors.Wrap(err, "load user asset")
	}

	/*
		{
		UserId:********
		Base:eth
		Quote:usdt
		AccountType:swap
		OperateTime:1686215518423961995
		Orders:[
		{
		UserId: UserType:0 OrderId:10886728767014174720
		Base: Quote:
		OrderType:10
		Price:1833.3 Amount:0.2 AccountType:
		Side:1
		Offset:1
		OperateTime:0
		Leverage:20
		MarginMode:1
		UnfrozenMargin:18.333
		IsInnerCall:0 IsErr:false
		HoldMode:0
		IsLimitOrder:1 LiquidationType:0
		}]
		}
	*/

	leverage := asset.GetLeverage(param.Symbol)

	reply := &usecase.BatchUnlockReply{
		SuccessList: make([]*usecase.UnLockParam, 0),
		FailedList:  make([]*usecase.UnLockParam, 0),
	}
	// assetSyncList := make([]*repository.LogAssetSync, 0)
	for _, order := range param.Orders {
		replyItem := &usecase.UnLockParam{
			OrderId: order.OrderId,
		}

		if leverage.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_UNSPECIFIED && leverage.MarginMode != order.MarginMode {
			// 记录错误订单
			reply.FailedList = append(reply.FailedList, replyItem)
			continue
		}

		// 按照统一币对批量修改(注: 此处slf.req.ContractCode()不可使用order.ContractCode()替代, 因为撮合给的orders中所有ContractCode都为空)
		asset.DecrFrozen(param.Symbol, order.Amount, len(order.AwardIds) > 0)

		// reply.SuccessList = append(reply.SuccessList, replyItem)
		// assetSync := modelutil.NewLogAssetSync(asset, param.Currency, time.Now().UnixNano())
		// assetSyncList = append(assetSyncList, assetSync)
	}

	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return nil, err
	}

	// TODO 资产异步存库
	// go AddAssetLogs(assetSyncList...) // wallet资产异步存库

	// // 如果存在体验金更新仓位保证金
	// if len(posSides) > 0 {
	// 	go func() {
	// 		_redis := redislib.Redis()
	// 		if posSides[enum.LongPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialLongPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 		if posSides[enum.ShortPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialShortPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 		if posSides[enum.BothPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialBothPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 	}()
	// }

	// 更新订单状态 先不影響後續
	if err := use.updateOrdersToCancel(ctx, param.Orders); err != nil {
		logrus.Errorf("failed to update orders to cancel: %v", err)
	}

	return reply, nil
}

// update order status
func (use *AssetUseCase) updateOrdersToCancel(ctx context.Context, orders []*usecase.UnLockParam) error {
	for _, order := range orders {
		if len(order.OrderId) == 0 {
			logrus.Errorf("order id is empty: %+v", order)
			continue
		}
		oldOrder, err := use.orderRepo.GetByOrderId(ctx, order.OrderId)
		if err != nil {
			logrus.Errorf("failed to get order by id %s: %v", order.OrderId, err)
			continue
		}
		if oldOrder == nil {
			logrus.Errorf("order not found: %s", order.OrderId)
			continue
		}
		// 取消來源待判斷
		oldOrder.CancelSource = order.CancelSource
		oldOrder.Status = order.Status
		oldOrder.UpdateTime = time.Now().UnixNano()
		err = use.orderRepo.UpdateOrder(ctx, oldOrder)
		if err != nil {
			logrus.Errorf("failed to update order %s to canceled: %v", order.OrderId, err)
			continue
		}
	}

	return nil
}

// LockPosition 冻结仓位
func (use *AssetUseCase) LockPosition(ctx context.Context, param *usecase.LockPositionParam) (*usecase.LockPositionReply, error) {
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return nil, domain.ErrDistributedLock
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Symbol, param.Currency)
	if err != nil {
		return nil, domain.ErrLoadUserAsset
	}

	reply := &usecase.LockPositionReply{
		Amount:  param.Amount,
		OrderID: param.OrderID,
		IsTrial: param.IsTrial,
	}

	var position repository.PosSwap
	switch param.PosSide {
	case futuresassetpb.PosSide_POS_SIDE_LONG:
		position = asset.LongPos
	case futuresassetpb.PosSide_POS_SIDE_SHORT:
		position = asset.ShortPos
	case futuresassetpb.PosSide_POS_SIDE_BOTH:
		position = asset.BothPos
	default:
		return nil, domain.ErrInvalidPosSide
	}

	if position.PosAvailable.Sign() <= 0 {
		return nil, domain.ErrInsufficientPosAvailable
	}
	diffPos := position.PosAvailable.Sub(param.Amount)
	if diffPos.IsNegative() {
		switch param.TriggerType {
		// 止赢止损单不做拦截
		case futuresassetpb.TriggerType_TRIGGER_TYPE_ORDER_SL, futuresassetpb.TriggerType_TRIGGER_TYPE_ORDER_TP,
			futuresassetpb.TriggerType_TRIGGER_TYPE_POSITION_SL, futuresassetpb.TriggerType_TRIGGER_TYPE_POSITION_TP:
			param.Amount = position.PosAvailable

		default:
			return nil, domain.ErrInsufficientPosAvailable
		}
	}

	position.PosAvailable = position.PosAvailable.Sub(param.Amount)
	err = use.cacheRepo.SetPos(ctx, param.PosSide, position, param.IsTrial)
	if err != nil {
		return nil, err
	}

	// TODO 推送仓位变化以落库消息
	// pos := userCache.NewLogPosSync(position, param.OperateTime, "", param.OrderId,
	// 	slf.req.Side, slf.req.Offset, slf.req.Amount, decimal.Zero)
	// go func() {
	// 	// 仓位异步存库
	// 	if err := persist.SyncPos(redislib.Redis(), slf.req.ContractCode(), pos); err != nil {
	// 		loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 	}
	//  //  仓位推送
	// }()

	return reply, nil
}

// IncrAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) IncrAsset(ctx context.Context, param *usecase.IncrParam) (usecase.AssetReply, error) {
	reply := usecase.AssetReply{
		OrderId: param.OrderId,
	}

	if len(param.UID) == 0 || len(param.Currency) == 0 || len(param.OrderId) == 0 || param.Amount.Sign() <= 0 {
		logrus.Errorf("param err: %+v", *param)
		return reply, fmt.Errorf("param err: %+v", *param)
	}

	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return reply, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, "", param.Currency)
	if err != nil {
		return reply, err
	}

	asset.AddBalance(param.Currency, param.Amount)
	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return reply, err
	}

	// 将 Frozen map 中的 decimal.Decimal 值转换为字符串
	frozenStrMap := make(map[string]string)
	for k, v := range asset.Frozen {
		frozenStrMap[k] = v.String()
	}
	assetData := &commonpb.KafkaAccountAsset{
		Uid:      asset.UID,
		Currency: param.Currency,
		Balance:  asset.CBalance(param.Currency).String(),
		Frozen:   frozenStrMap,
	}
	// 推送资产变化消息
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountAsset]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountAsset]]{
				Uid: assetData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountAsset]{
					Event: commonpb.AccountEvent_TYPE_ASSET,
					Data:  assetData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}
	// TODO 构建账单
	go func() {
		bill := entity.BillAsset{
			BillID:         use.gen.Generate().Int64(),
			UID:            param.UID,
			RefID:          param.OrderId,
			Currency:       strings.ToUpper(param.Currency),
			BillType:       int(domain.BillTypeInnerIn),
			FlowType:       domain.FlowTypeIn,
			FromPair:       param.FromPair,
			ToPair:         param.ToPair,
			FromWalletType: param.FromAccount,
			ToWalletType:   param.ToAccount,
			Amount:         param.Amount,
			CreateTime:     time.Now().UnixNano(),
		}

		err := use.producerUseCase.SendAssetsBill([]*entity.BillAsset{&bill})
		if err != nil {
			logrus.Errorf("send asset bill err: %+v", err)
		}
	}()

	return reply, nil
}

// DecrAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) DecrAsset(ctx context.Context, param *usecase.IncrParam) (usecase.AssetReply, error) {
	reply := usecase.AssetReply{
		OrderId: param.OrderId,
	}
	if len(param.UID) == 0 || len(param.Currency) == 0 || len(param.OrderId) == 0 || param.Amount.Sign() <= 0 {
		logrus.Errorf("param err: %+v", *param)
		return reply, fmt.Errorf("param err: %+v", *param)
	}

	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return reply, domain.ErrLockPos
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, "", param.Currency)
	if err != nil {
		return reply, err
	}

	maxOut, err := use.assetRepo.CanTransfer(ctx, asset, param.Currency)
	if err != nil {
		log.Println("Decrease CanTransfer error:", err)
		return reply, err
	}
	if maxOut.LessThan(param.Amount) {
		return reply, errors.New("insufficient funds available")
	}
	asset.AddBalance(param.Currency, param.Amount.Neg())
	if asset.CBalance(param.Currency).Sign() < 0 {
		return reply, errors.New("account balance is negative")
	}

	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return reply, err
	}

	// 将 Frozen map 中的 decimal.Decimal 值转换为字符串
	frozenStrMap := make(map[string]string)
	for k, v := range asset.Frozen {
		frozenStrMap[k] = v.String()
	}
	assetData := &commonpb.KafkaAccountAsset{
		Uid:      asset.UID,
		Currency: param.Currency,
		Balance:  asset.CBalance(param.Currency).String(),
		Frozen:   frozenStrMap,
	}
	// 推送资产变化消息
	if err := use.producerUseCase.SendAccount(
		&message.EventResp[usecase.Account[*commonpb.KafkaAccountAsset]]{
			Data: message.EventRespData[usecase.Account[*commonpb.KafkaAccountAsset]]{
				Uid: assetData.Uid,
				Params: usecase.Account[*commonpb.KafkaAccountAsset]{
					Event: commonpb.AccountEvent_TYPE_ASSET,
					Data:  assetData,
				},
			},
		},
	); err != nil {
		logrus.Errorf("failed to send account message: %v", err)
	}
	// TODO 构建账单
	go func() {
		bill := entity.BillAsset{
			BillID:         use.gen.Generate().Int64(),
			UID:            param.UID,
			RefID:          param.OrderId,
			Currency:       strings.ToUpper(param.Currency),
			BillType:       int(domain.BillTypeInnerIn),
			FlowType:       domain.FlowTypeOut,
			FromPair:       param.FromPair,
			ToPair:         param.ToPair,
			FromWalletType: param.FromAccount,
			ToWalletType:   param.ToAccount,
			Amount:         param.Amount.Neg(),
			CreateTime:     time.Now().UnixNano(),
		}

		err := use.producerUseCase.SendAssetsBill([]*entity.BillAsset{&bill})
		if err != nil {
			logrus.Errorf("send asset bill err: %+v", err)
		}
	}()

	return reply, nil
}

// MaxWithdraw 获取最大可提币金额
func (use *AssetUseCase) MaxWithdraw(ctx context.Context, param *usecase.MaxWithdrawParam) (usecase.MaxWithdrawReply, error) {
	data := usecase.MaxWithdrawReply{
		Currency: strings.ToUpper(param.Currency),
	}
	if len(param.UID) == 0 {
		logrus.Errorf("param err: %+v", *param)
		return data, fmt.Errorf("param err: %+v", *param)
	}

	// 获取资产
	asset, err := use.cacheRepo.Load(ctx, param.UID, "", param.Currency)
	if err != nil {
		return data, err
	}

	data.TransAmount, err = use.assetRepo.CanTransfer(ctx, asset, param.Currency)
	if err != nil {
		log.Println("MaxWithdraw CanTransfer error:", err)
		return data, err
	}
	if data.TransAmount.Sign() < 0 {
		data.TransAmount = decimal.Zero
	}

	return data, nil
}

func (use *AssetUseCase) AccountMaxWithdraw(ctx context.Context, param *usecase.MaxWithdrawParam) ([]usecase.MaxWithdrawReply, error) {
	var data []usecase.MaxWithdrawReply
	if len(param.UID) == 0 {
		logrus.Errorf("param err: %+v", *param)
		return data, fmt.Errorf("param err: %+v", *param)
	}

	asset, err := use.cacheRepo.Load(ctx, param.UID, "", param.Currency)
	if err != nil {
		return data, err
	}

	for _, currency := range domain.CurrencyList {
		temp := usecase.MaxWithdrawReply{
			Currency: strings.ToUpper(currency),
		}
		temp.TransAmount, err = use.assetRepo.CanTransfer(ctx, asset, currency)
		if err != nil {
			log.Println("MaxWithdraw CanTransfer error:", err)
			return data, err
		}
		if temp.TransAmount.Sign() < 0 {
			temp.TransAmount = decimal.Zero
		}
		data = append(data, temp)
	}

	return data, nil
}

func (use *AssetUseCase) InnerTransfer(ctx context.Context, req *usecase.InnerTransferParam) error {
	// lock from user & to user
	fromMutex := use.rs.NewMutex(domain.MutexSwapPosLock+req.FromUserId, redsync.WithExpiry(30*time.Second))
	if fromMutex.Lock() != nil {
		return domain.ErrLockPos
	}
	defer fromMutex.Unlock()
	toMutex := use.rs.NewMutex(domain.MutexSwapPosLock+req.ToUserId, redsync.WithExpiry(30*time.Second))
	if toMutex.Lock() != nil {
		return domain.ErrLockPos
	}
	defer toMutex.Unlock()

	// 获取资产
	fromAsset, err := use.cacheRepo.Load(ctx, req.FromUserId, "", req.Currency)
	if err != nil {
		return err
	}
	toAsset, err := use.cacheRepo.Load(ctx, req.ToUserId, "", req.Currency)
	if err != nil {
		return err
	}

	// 转出金额判断
	maxOut, err := use.assetRepo.CanTransfer(ctx, fromAsset, req.Currency)
	if err != nil {
		log.Println("InnerTransfer CanTransfer error:", err)
		return err
	}
	if maxOut.LessThan(req.Amount) {
		return errors.New("inner transfer from account insufficient funds available")
	}
	fromAsset.AddBalance(req.Currency, req.Amount.Neg())
	if fromAsset.CBalance(req.Currency).Sign() < 0 {
		return errors.New("inner transfer from account balance is negative")
	}

	err = use.cacheRepo.UpdateBalance(ctx, fromAsset)
	if err != nil {
		return errors.Wrap(err, "inner transfer from user: "+req.FromUserId)
	}
	toAsset.AddBalance(req.Currency, req.Amount)
	err = use.cacheRepo.UpdateBalance(ctx, toAsset)
	if err != nil {
		fromAsset.AddBalance(req.Currency, req.Amount)
		_ = use.cacheRepo.UpdateBalance(ctx, fromAsset)
		return errors.Wrap(err, "inner transfer to user: "+req.ToUserId)
	}

	// TODO 记录资产流水
	// go func() {
	// 	redis := use.rs.Redis()
	// 	fromParam := &payload.IncrParam{
	// 		UID:         req.FromUserId,
	// 		Currency:    req.Currency,
	// 		FromAccount: domain.AccountSwapInt,
	// 		ToAccount:   domain.AccountSwapInt,
	// 		Amount:      req.Amount,
	// 	}
	// 	toParam := &payload.IncrParam{
	// 		UID:         req.ToUserId,
	// 		Currency:    req.Currency,
	// 		FromAccount: domain.AccountSwapInt,
	// 		ToAccount:   domain.AccountSwapInt,
	// 		Amount:      req.Amount,
	// 	}
	// 	fromBill := modelutil.NewBillSwapWithAsset(fromParam, "", domain.BillTypeInnerOut)
	// 	toBill := modelutil.NewBillSwapWithAsset(toParam, "", domain.BillTypeInnerIn)
	// 	coupling.AddBills(redis, *fromBill, *toBill) // 前台合约划转账单记账
	// }()

	return nil
}
