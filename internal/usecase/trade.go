package usecase

import (
	"context"
	"fmt"
	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"
	"github.com/sirupsen/logrus"

	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// TradeUseCaseParam 资产用例参数
type TradeUseCaseParam struct {
	dig.In

	Config    *cfg.Config[configs.Config] `name:"config"`
	TradeRepo repository.TradeRepository
}

// TradeUseCase 资产用例实现
type TradeUseCase struct {
	config    *cfg.Config[configs.Config]
	tradeRepo repository.TradeRepository
}

// NewTradeUseCase 创建实例
func NewTradeUseCase(param TradeUseCaseParam) usecase.TradeUseCase {
	return &TradeUseCase{
		config:    param.Config,
		tradeRepo: param.TradeRepo,
	}
}

// TradeList implements usecase.TradeUseCase.
func (use *TradeUseCase) TradeList(ctx context.Context, req *usecase.TradeListParam) (usecase.TradeListResponse, error) {
	if req == nil {
		return usecase.TradeListResponse{}, nil
	}
	param := repository.TradePageParam{
		Condition: req.Condition,
		UID:       req.UID,
		Symbol:    req.Symbol,
		OrderID:   req.OrderID,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		Side:      req.Side,
	}
	resp, total, err := use.tradeRepo.Page(ctx, param)
	if err != nil {
		logrus.Error(fmt.Sprintf("tradeRepo.Page err:%s", err))
		return usecase.TradeListResponse{}, err
	}

	return usecase.TradeListResponse{
		Trades: resp,
		Page: pager.Page{
			Total:     total,
			PageSize:  req.PageSize,
			PageIndex: req.PageIndex,
		},
	}, nil
}
