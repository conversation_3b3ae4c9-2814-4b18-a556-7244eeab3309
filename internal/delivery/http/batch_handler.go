package http

import (
	"net/http"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type BatchHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
}

type batchHandler struct {
	assetUseCase usecase.AssetUseCase
}

// NewBatchHandler 创建批量处理器
func newBatchHandler(param BatchHandlerParam) *batchHandler {
	return &batchHandler{
		assetUseCase: param.AssetUseCase,
	}
}

// userAsset 分组获取用户合约资产
func (handler *batchHandler) userAsset(c *gin.Context) {
	var req payload.AssetParam

	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	asset, err := handler.assetUseCase.BatchUserAsset(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.Errorf("query asset err: %s", err.Error())
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992410, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(asset))
}

// func (handler *batchHandler) batchUnlockAsset(c *gin.Context) {
// 	var req payload.BatchUnlockParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	unlockParam := &payload.LockParam{
// 		UID:         req.UID,
// 		Base:        req.Base,
// 		Quote:       req.Quote,
// 		OperateTime: req.OperateTime,
// 	}

// 	if req.Orders[0].PosSide != domain.Long && req.Orders[0].PosSide != domain.Short && req.Orders[0].PosSide != domain.Both {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("offset err")))

// 		return
// 	}

// 	var items []payload.BatchUnlockItem
// 	var code domain.Code
// 	var err error

// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
// 	asset, err := userCache.Load()
// 	if err != nil {
// 		logrus.Errorf("batchLockAsset query asset err: %s", err.Error())
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992410, err))

// 		return
// 	}

// 	logrus.Info(0, "======", req.UID, "/batch/unlock params:", fmt.Sprintf("%+v", req))
// 	switch asset.PositionMode {
// 	case domain.HoldModeBoth:
// 		code, items, err = isolation.NewBatch(unlockParam).UnLockAsset(req.Orders)

// 	default:
// 		if req.Orders[0].PosSide == domain.Long {
// 			code, items, err = isolation.NewBatch(unlockParam).UnLockAsset(req.Orders)
// 		} else {
// 			code, items, err = isolation.NewBatch(unlockParam).UnlockPos(req.Orders)
// 		}

// 	}
// 	logrus.Info(0, "======", req.UID, "/batch/unlock response:", "code", code, "items", items, "err", err)

// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(items))
// }

// func (handler *batchHandler) batchLockAsset(c *gin.Context) {
// 	var req payload.BatchLock
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	// 如果用户在爆仓中, 不能进行挂单
// 	burstItems := make([]payload.BatchLockItem, 0)
// 	// 分别校验全仓或者逐仓是否爆仓中状态
// 	checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID, MarginMode: req.MarginMode}}
// 	switch req.MarginMode {
// 	case domain.MarginModeCross:
// 		if swapcache.UserBursting(checkBurst) {
// 			c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))

// 			return
// 		}
// 	case domain.MarginModeIsolated:
// 		checkBurst.ContractCode = util.ContractCode(req.Base, req.Quote)
// 		// 逐仓卖方向是否爆仓中
// 		if req.PositionMode == domain.HoldModeBoth {
// 			checkBurst.Liquidation.PosSide = domain.BothPos
// 		} else {
// 			checkBurst.Liquidation.PosSide = req.PosSide
// 		}
// 		if swapcache.UserBursting(checkBurst) {
// 			for i := 0; i < len(req.Orders); {
// 				order := req.Orders[i]
// 				if order.Side == domain.Sell {
// 					burstItems = append(burstItems, payload.BatchLockItem{
// 						Code:         domain.ErrUserBurst,
// 						OrderId:      order.OrderId,
// 						Amount:       decimal.Zero,
// 						FrozenMargin: decimal.Zero,
// 					})
// 					req.Orders = append(req.Orders[:i], req.Orders[i+1:]...)
// 					continue
// 				}
// 				i++
// 			}
// 		}
// 		// 逐仓买方向是否爆仓中
// 		if req.PositionMode == domain.HoldModeBoth {
// 			checkBurst.Liquidation.PosSide = domain.BothPos
// 		} else {
// 			checkBurst.Liquidation.PosSide = req.PosSide
// 		}
// 		if swapcache.UserBursting(checkBurst) {
// 			for i := 0; i < len(req.Orders); {
// 				order := req.Orders[i]
// 				if order.Side == domain.Buy {
// 					burstItems = append(burstItems, payload.BatchLockItem{
// 						Code:         domain.ErrUserBurst,
// 						OrderId:      order.OrderId,
// 						Amount:       decimal.Zero,
// 						FrozenMargin: decimal.Zero,
// 					})
// 					req.Orders = append(req.Orders[:i], req.Orders[i+1:]...)
// 					continue
// 				}
// 				i++
// 			}
// 		}
// 	}

// 	var items []payload.BatchLockItem
// 	var code domain.Code
// 	var err error

// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
// 	asset, err := userCache.Load()
// 	if err != nil {
// 		logrus.Errorf("batchLockAsset query asset err: %s", err.Error())
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992410, err))

// 		return
// 	}

// 	logrus.Info(0, "======", req.UID, "/batch/lock params", fmt.Sprintf("%+v", req))
// 	switch asset.PositionMode {
// 	case domain.HoldModeBoth:
// 		code, items, err = isolation.NewBatch(nil).LockAsset(&req)

// 	default:
// 		if req.IsOpen() {
// 			code, items, err = isolation.NewBatch(nil).LockAsset(&req)
// 		} else {
// 			code, items, err = isolation.NewBatch(nil).LockPos(&req)
// 		}
// 	}
// 	logrus.Info(0, "======", req.UID, "/batch/lock response:", fmt.Sprintf("%+v", "code", code, "items", items, "err", err))

// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	items = append(items, burstItems...)
// 	c.JSON(http.StatusOK, response.NewSuccess(items))
// }
