package http

import (
	"errors"
	"net/http"
	"time"

	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/gin-gonic/gin"
	"github.com/go-redsync/redsync/v4"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type CronParam struct {
	dig.In

	RS *redsync.Redsync `name:"rs"`

	SettingRepository repository.SettingRepository
	FundingUseCase    usecase.FundingUseCase
}

type cronHandler struct {
	rs *redsync.Redsync

	settingRepo    repository.SettingRepository
	fundingUseCase usecase.FundingUseCase
}

func newCronHandler(param CronParam) *cronHandler {
	return &cronHandler{
		rs: param.RS,

		settingRepo:    param.SettingRepository,
		fundingUseCase: param.FundingUseCase,
	}
}

func (handler *cronHandler) transactionHour(c *gin.Context) {
	// var req payload.TransactionHourReport
	// if err := request.ShouldBindJSON(c, &req); err != nil {
	// 	c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

	// 	return
	// }

	// if req.Time.IsZero() {
	// 	req.Time = time.Now()
	// }

	// logrus.Infof("start transactionCronUseCase.Hour")

	// err := handler.transactionCronUseCase.Hour(c.Request.Context(), req.Time)
	// if err != nil {
	// 	logrus.WithFields(logrus.Fields{
	// 		"err": err,
	// 	}).Error("transactionCronUseCase.Hour")

	// 	c.JSON(response.ServiceErrorToErrorResp(err))

	// 	return
	// }

	// logrus.Infof("end transactionCronUseCase.Hour")
	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}

func (handler *cronHandler) generateFundingRate(c *gin.Context) {
	fundRateMutex := handler.rs.NewMutex(domain.MutexSwapCronFund, redsync.WithExpiry(30*time.Second))
	if fundRateMutex.Lock() != nil {
		c.JSON(response.ServiceErrorToErrorResp(errors.New("get funding rate lock err")))

		return
	}
	defer fundRateMutex.Unlock()

	contractSettings, err := handler.settingRepo.GetAllPairSettingInfo(c.Request.Context())
	if err != nil {
		logrus.Error("makeFundRates get all contract config err.")

		return
	}

	for symbol, contractConfig := range contractSettings {
		go handler.fundingUseCase.MakeFundRate(c.Request.Context(), symbol, &contractConfig)
	}

	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}
