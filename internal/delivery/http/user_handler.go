package http

import (
	"errors"
	"fmt"
	"net/http"

	// 	"futures-asset/cache"
	// 	"futures-asset/cache/swapcache"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"

	// "futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	// 	"futures-asset/pkg/match"
	// 	"futures-asset/pkg/setting"
	// 	"futures-asset/util"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type UserHandlerParam struct {
	dig.In

	UserUseCase usecase.UserUseCase
	PriceRepo   repository.PriceRepository
	AssetRepo   repository.UserRepository
	SettingRepo repository.SettingRepository
	BurstRepo   repository.BurstRepository
}

type userHandler struct {
	userUseCase usecase.UserUseCase
	priceRepo   repository.PriceRepository
	assetRepo   repository.UserRepository
	settingRepo repository.SettingRepository
	burstRepo   repository.BurstRepository
}

func newUserHandler(param UserHandlerParam) *userHandler {
	return &userHandler{
		userUseCase: param.UserUseCase,
		priceRepo:   param.PriceRepo,
		assetRepo:   param.AssetRepo,
		settingRepo: param.SettingRepo,
		burstRepo:   param.BurstRepo,
	}
}

// // InitSwap 合约钱包初始化
// func (handler *userHandler) InitSwap(c *gin.Context) {
// 	var req payload.UIDParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}

// 	code := handler.userUseCase.SwapInit(c.Request.Context(), req.ToUseCase())
// 	if code != http.StatusOK {
// 		logrus.Error(fmt.Sprintf("InitSpotPrice Swap err.code:%+v, param:%+v", code, req))
// 		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(req))
// }

// UserBasicConfig 获取用户合约基础配置
func (handler *userHandler) UserBasicConfig(c *gin.Context) {
	var req payload.UIDParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	leverage, err := handler.userUseCase.UserBasicConfig(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.Info(0, fmt.Sprintf("UserBasicConfig %+v", leverage))
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251114, errors.New(err.Error())))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(leverage))
}

// UserLeverage 获取用户杠杆倍数
func (handler *userHandler) UserLeverage(c *gin.Context) {
	var req payload.UserSymbolConfig
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	leverage, err := handler.userUseCase.UserLeverage(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.Info(0, fmt.Sprintf("UserLeverage %+v", leverage))
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251114, errors.New(err.Error())))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(leverage))
}

// // LeverageMarginAdjust 调整杠杠倍数和仓位模式
// func (handler *userHandler) LeverageMarginAdjust(c *gin.Context) {
// 	var req payload.LeverageMarginAdAdjust
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}

// 	if len(req.UID) <= 0 || len(req.ContractCode) <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("userid or base, quote err: %+v", req))))
// 		return
// 	}
// 	if req.MarginMode != domain.MarginModeIsolated && req.MarginMode != domain.MarginModeCross {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", req))))
// 		return
// 	}

// 	if req.Leverage <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("leverage less than 0")))
// 		return
// 	}

// 	// 爆仓中不能调整杠杆倍数 和仓位模式
// 	checkBurst := repository.BurstLockParam{Liquidation: cache.LiquidationInfo{UID: req.UID}}
// 	if swapcache.UserBursting(checkBurst) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
// 		return
// 	}

// 	// 不能有挂单(包含止赢止损单)
// 	if match.NewMatchData().SwapOrders(req.UID, req.ContractCode).Total > 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992120, errors.New(domain.ErrOrderOrPos.Error())))
// 		return
// 	}

// 	pair, err := setting.Service.GetPairSettingInfo(util.BaseQuote(req.ContractCode))
// 	if err != nil || pair == nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250002, err))
// 		return
// 	}
// 	// 验证杠杆倍数取值范围
// 	if req.Leverage > pair.MaxLeverage {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code251007, errors.New(fmt.Sprintf("leverage %+v error, max:%+v", req.Leverage, pair.MaxLeverage))))
// 		return
// 	}

// 	code, err := handler.userUseCase.AdjustLeverageMargin(c.Request.Context(), req.ToUseCase())
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(req))
// }

// UpdatePositionMargin 调整逐仓仓位保证金
func (handler *userHandler) UpdatePositionMargin(c *gin.Context) {
	var param payload.MarginParam
	if err := request.ShouldBindJSON(c, &param); err != nil {
		c.JSON(http.StatusOK, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
		c.JSON(http.StatusOK, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", param))))
		return
	}
	if param.PosSide != futuresassetpb.PosSide_POS_SIDE_LONG && param.PosSide != futuresassetpb.PosSide_POS_SIDE_SHORT && param.PosSide != futuresassetpb.PosSide_POS_SIDE_BOTH {
		c.JSON(http.StatusOK, response.NewError(domain.Code250007, errors.New(fmt.Sprintf("Pos Side err: %+v", param))))
		return
	}

	// 检查爆仓状态 - 全仓和逐仓模式都需要检查
	marginModes := []futuresassetpb.MarginMode{
		futuresassetpb.MarginMode_MARGIN_MODE_CROSS,
		futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED,
	}

	for _, mode := range marginModes {
		checkBurst := repository.CheckBurstParam{
			UID:        param.UID,
			MarginMode: mode,
		}
		// 逐仓模式需要添加合约代码
		if mode == futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
			checkBurst.Symbol = param.Symbol
		}

		isBursting, err := handler.burstRepo.IsBursting(c.Request.Context(), checkBurst)
		if err != nil {
			c.JSON(http.StatusOK, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
			return
		}
		if isBursting {
			c.JSON(http.StatusOK, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
			return
		}
	}

	code, err := handler.userUseCase.AdjustMargin(c.Request.Context(), param.ToUseCase())
	if err != nil {
		c.JSON(http.StatusOK, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

// func (handler *userHandler) GetOrderConfig(c *gin.Context) {
// 	var req payload.CommonParam
// 	if err := request.ShouldBindJSON(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}
// 	if len(req.UID) <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("p err: %+v", req))))
// 		return
// 	}

// 	orderConfig, err := handler.userUseCase.GetOrderConfig(c.Request.Context(), req.ToUseCase())
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.OrderConfigErr, err))
// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(orderConfig))
// }

func (handler *userHandler) UpdateOrderConfig(c *gin.Context) {
	var req payload.UpdateOrderConfigParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusOK, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	code, err := handler.userUseCase.UpdateOrderConfig(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusOK, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

// // MarkPrice 获取全部标记价格
// // TODO 验证接口返回数据
// func (handler *userHandler) MarkPrice(c *gin.Context) {
// 	data := handler.priceRepo.GetAllMarkPrice(c.Request.Context())

// 	c.JSON(http.StatusOK, response.NewSuccess(data))
// }

// func checkAdjustLeverage(order *entity.Order, p *repository.UpdateLeverageParam) bool {
// 	switch order.PositionMode {
// 	case domain.HoldModeBoth:
// 		if p.BLeverage > 0 {
// 			return false
// 		}

// 	case domain.HoldModeHedge:
// 		switch domain.MarginMode(order.MarginMode) {
// 		case domain.MarginModeIsolated:
// 			if order.PosSide == domain.Long && p.LLeverage > 0 {
// 				return false
// 			}
// 			// 卖出开空 看跌
// 			if order.PosSide == domain.Short && p.SLeverage > 0 {
// 				return false
// 			}

// 		case domain.MarginModeCross:
// 			if p.Leverage > 0 {
// 				return false
// 			}
// 		}
// 	}

// 	return true
// }

// UpdateLeverage 调整用户杠杠倍数
func (handler *userHandler) UpdateLeverage(c *gin.Context) {
	var req payload.UpdateLeverageParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusOK, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	// 爆仓中不能调整杠杆倍数
	checkBurst := repository.CheckBurstParam{
		UID: req.UID,
	}
	isBursting, err := handler.burstRepo.IsBursting(c.Request.Context(), checkBurst)
	if err != nil {
		c.JSON(http.StatusOK, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}
	if isBursting {
		c.JSON(http.StatusOK, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	// TODO 不能有挂单(包含止赢止损单)
	// orders := match.NewMatchData().SwapOrders(req.UID, req.ContractCode).Orders
	// logrus.Infof("SwapOrders userid:%s,len:%v", req.UID, len(orders))
	// // ctx.Error(domain.Code992120, domain.ErrOrderOrPos.Error())
	// for i := range orders {
	// 	flag := checkAdjustLeverage(orders[i], req.ToUseCase())
	// 	if !flag {
	// 		logrus.Errorf("update both pos orders[i]:%+v,req:%v", orders[i], req)
	// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992101, errors.New(domain.ErrOrderOrPos.Error())))
	// 		return
	// 	}
	// }

	if req.Leverage < 0 || req.LLeverage < 0 || req.SLeverage < 0 || req.BLeverage < 0 {
		c.JSON(http.StatusOK, response.NewError(domain.CodeParamInvalid, errors.New("leverage less than 0")))

		return
	}
	pair, err := handler.settingRepo.GetCachePair(c.Request.Context(), req.Symbol)
	if err != nil || pair == nil {
		c.JSON(http.StatusOK, response.NewError(domain.Code250002, err))

		return
	}

	// 验证杠杆倍数取值范围
	if req.Leverage > pair.MaxLeverage || req.LLeverage > pair.MaxLeverage || req.SLeverage > pair.MaxLeverage || req.BLeverage > pair.MaxLeverage {
		c.JSON(http.StatusOK, response.NewError(domain.Code992107, errors.New(fmt.Sprintf("leverage %+v error, max:%+v", req.Leverage, pair.MaxLeverage))))

		return
	}

	code, leverage, err := handler.userUseCase.AdjustLeverage(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusOK, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(leverage))
}

func (handler *userHandler) UpdateAssetMode(c *gin.Context) {
	var req payload.UpdateAssetModeParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	if req.AssetMode != futuresassetpb.AssetMode_ASSET_MODE_SINGLE && req.AssetMode != futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("asset mode err: %+v", req))))

		return
	}

	code, err := handler.userUseCase.UpdateAssetMode(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) UpdatePositionMode(c *gin.Context) {
	var req payload.HoldModeParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	if req.PositionMode != domain.HoldModeHedge && req.PositionMode != domain.HoldModeBoth {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("hold mode err: %+v", req))))

		return
	}

	code, err := handler.userUseCase.UpdatePositionMode(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

func (handler *userHandler) UpdateCrossMarginMode(c *gin.Context) {
	var req payload.AdjustCrossParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	res, err := handler.userUseCase.AdjustCross(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992130, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(res))
}

func (handler *userHandler) UpdateOrderConfirm(c *gin.Context) {
	var req payload.UpdateOrderConfirmParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	code, err := handler.userUseCase.UpdateOrderConfirm(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}

// UpdateMarginMode 调整仓位模式
func (handler *userHandler) UpdateMarginMode(c *gin.Context) {
	var req payload.UpdateMarginModeParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	if req.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED && req.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_CROSS {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("margin mode err: %+v", req))))
		return
	}

	// 混合保证金不允许切换成逐仓
	asset, err := handler.assetRepo.LoadJoinMargin(c.Request.Context(), &repository.CommonParam{
		UID:    req.UID,
		Symbol: req.Symbol,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992410, err))
		return
	}
	if asset.AssetMode == int(futuresassetpb.AssetMode_ASSET_MODE_MULTI) && req.MarginMode == futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code992123, errors.New("asset mode not support isolated")))
		return
	}

	// 爆仓中不能调整杠杆倍数 和仓位模式
	checkBurst := repository.CheckBurstParam{
		UID: req.UID,
	}
	isBursting, err := handler.burstRepo.IsBursting(c.Request.Context(), checkBurst)
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}
	if isBursting {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	pair, err := handler.settingRepo.GetCachePair(c.Request.Context(), req.Symbol)
	if err != nil || pair == nil {
		logrus.Errorf("get contract pair err: %+v", err)
		c.JSON(http.StatusBadRequest, response.NewError(domain.Code250002, err))
		return
	}

	code, err := handler.userUseCase.UpdateMarginMode(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(code, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(""))
}
