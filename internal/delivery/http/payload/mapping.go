package payload

const BillSwapMapping = `
{
    "mappings": {
        "bill_swap": {
            "properties": {
                "id": {
                    "type": "long"
                },
                "user_id": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 50
                        }
                    }
                },
                "bill_id": {
                    "type": "text"
                },
                "symbol": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 80
                        }
                    }
                },
                "currency": {
                    "type": "text",
                    "fields": {
                        "keyword": {
                            "type": "keyword",
                            "ignore_above": 50
                        }
                    }
                },
                "bill_type": {
                    "type": "long"
                },
                "amount": {
                    "type": "double"
                },
                "balance": {
                    "type": "double"
                },
                "margin": {
                    "type": "double"
                },
                "available": {
                    "type": "double"
                },
                "frozen":{
					"type": "double"
				},
                "operate_time": {
                    "type": "long"
                }               
            }
        }
    }
}
`
