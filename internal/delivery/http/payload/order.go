package payload

import (
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"
)

type OrderListReq struct {
	pager.Condition
	UID     string `form:"uid"`      // 用户ID
	Symbol  string `form:"symbol"`   // 交易对，如 BTC-USDT
	OrderID string `form:"order_id"` // 订单ID

	StartTime int64 `form:"start_time" json:"start_time"` // 开始时间
	EndTime   int64 `form:"end_time" json:"end_time"`     // 结束时间
	Side      int   `form:"side" json:"side"`
}

func (req *OrderListReq) ToUseCase() *usecase.OrderListParam {
	return &usecase.OrderListParam{
		Condition: pager.Condition{
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
		},
		UID:       req.UID,
		Symbol:    req.Symbol,
		OrderID:   req.OrderID,
		Side:      req.Side,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
}
