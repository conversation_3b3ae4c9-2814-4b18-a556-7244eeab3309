package payload

import (
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/libs/pager"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type (
	SwapParam struct {
		UID      string `form:"uid" json:"uid" binding:"required"`
		Currency string `form:"currency" json:"currency"`
		Symbol   string `form:"symbol" json:"symbol"` // 合约代码
	}
	SwapReply struct {
		UID           string                     `json:"uid"`            // 账户余额
		Balance       map[string]decimal.Decimal `json:"balance"`        // 账户余额
		Valuation     decimal.Decimal            `json:"valuation"`      // 保证金余额折合 其他
		BtcValuation  decimal.Decimal            `json:"btc_valuation"`  // 保证金余额折合 BTC
		MarginBalance decimal.Decimal            `json:"margin_balance"` // 保证金余额
		Available     decimal.Decimal            `json:"available"`      // 可用 (// 可用 (保证金余额8-已用8-冻结8))
		FrozenList    []Frozen                   `json:"frozen_list"`    // 冻结列表
	}
)

func (p *SwapParam) ToUseCase() *repository.SwapParam {
	return &repository.SwapParam{
		UID:      p.UID,
		Currency: p.Currency,
		Symbol:   p.Symbol,
	}
}

type Frozen struct {
	HaveTrial bool            `json:"have_trial"`
	Symbol    string          `json:"symbol"`
	Side      int             `json:"side"` // 双向持仓： 1多2空 单向持仓 1买2卖
	Frozen    decimal.Decimal `json:"frozen"`
}

// 运营后台用户持仓查询
type (
	UserPosParam struct {
		pager.Condition
		PosId           string `json:"pos_id"` // 持仓ID
		UID             string `json:"uid"`
		Base            string `json:"base"`
		Quote           string `json:"quote"`
		Order           int    `json:"order"`      // 排序 时间排序(1:升序 2:降序(默认))
		PosSide         int32  `json:"pos_side"`   // 仓位类型 1:多仓 2:空仓
		IsHasPos        bool   `json:"is_has_pos"` // 是否只看持有仓位
		AccountType     string `json:"account_type"`
		UserType        []int  `json:"user_type"`
		StartTime       int64  `json:"start_time"`
		EndTime         int64  `json:"end_time"`
		PosStatus       int32  `json:"pos_status"`
		MarginMode      int32  `json:"margin_mode"`
		LiquidationType []int  `json:"liquidation_type"`
		IsExcel         int    `json:"is_excel"` // 是否导出
	}
)

func (p *UserPosParam) ToUseCase() *repository.UserPosParam {
	return &repository.UserPosParam{
		Condition: pager.Condition{
			PageIndex: p.PageIndex,
			PageSize:  p.PageSize,
		},
		Base:            p.Base,
		Quote:           p.Quote,
		PosSide:         p.PosSide,
		IsHasPos:        p.IsHasPos,
		AccountType:     p.AccountType,
		UserType:        p.UserType,
		StartTime:       p.StartTime,
		EndTime:         p.EndTime,
		PosStatus:       p.PosStatus,
		MarginMode:      p.MarginMode,
		LiquidationType: p.LiquidationType,
		IsExcel:         p.IsExcel,
	}
}

type (
	// ContractBurstQueryRes 爆仓查询 列表
	ContractBurstQueryRes struct {
		LastPrice decimal.Decimal     `json:"last_price"` // 最新成交价 单币对时显示
		List      []ContractBurstData `json:"list"`       // 仓位列表
		PageSize  int
		PageNum   int
		Total     int
	}
	ContractBurstData struct {
		UID              string          `json:"uid"`               // 用户ID
		Symbol           string          `json:"symbol"`            // 合约代码
		Currency         string          `json:"currency"`          // 资产币种
		PosSide          int             `json:"pos_side"`          // 仓位类型:domain.LongPos domain.ShortPos
		MarginMode       int             `json:"margin_mode"`       // 全仓逐仓		？额外增加 产品可能会加上
		LeverAge         int             `json:"lever_age"`         // 杠杆倍数
		PosLevel         int             `json:"pos_level"`         // 仓位档位
		LiquidationPrice decimal.Decimal `json:"liquidation_price"` // 预估强平价
		MarginRate       decimal.Decimal `json:"margin_rate"`       // 保证金率
		IsSubsidy        int             `json:"is_subsidy"`        // 是否穿仓补贴 1.是 2.否
		IsOtherSub       int             `json:"is_other_sub"`      // 是否对手方减仓
		OperateTime      int64           `json:"operate_time"`      // 强平时间，如果减仓是减仓时间
		Status           int             `json:"status"`            // 状态： 已爆仓，爆仓中
	}
	// ContractBurstDetail 爆仓查询 详情
	ContractBurstDetail struct {
		UID           string
		ContractLevel int
		BurstData     []BurstDetail `json:"burst_data"`
	}
	BurstDetail struct {
		Symbol           string          `json:"symbol"`            // 合约代码
		Currency         string          `json:"currency"`          // 资产币种
		PosSide          int             `json:"pos_side"`          // 仓位类型:domain.LongPos domain.ShortPos
		MarginMode       int             `json:"margin_mode"`       // 全仓逐仓
		LeverAge         int             `json:"lever_age"`         // 杠杆倍数
		Pos              decimal.Decimal `json:"pos"`               // 仓位
		FlatPos          decimal.Decimal `json:"flat_pos"`          // 已平仓位
		MarginBalance    decimal.Decimal `json:"margin_balance"`    // 保证金
		OpenPrice        decimal.Decimal `json:"open_price"`        // 开仓均价
		LiquidationPrice decimal.Decimal `json:"liquidation_price"` // 预估强平价
		// ？？？？？？结构不全，需要等产品修改完需求才能继续做
	}

	// SubsidyQueryList 穿仓补贴列表 查询
	SubsidyQueryList struct {
		PageSize           int
		PageNum            int
		Total              int
		List               []SubsidyData
		TotalSubsidyAmount decimal.Decimal `json:"total_subsidy_amount"` // 小时穿仓补贴数量合计
	}
	SubsidyData struct {
		OperateTime     int64           `json:"operate_time"`      // 强制减仓时间
		Symbol          string          `json:"symbol"`            // 合约代码
		Currency        string          `json:"currency"`          // 资产币种
		UID             string          `json:"uid"`               // 用户ID
		SubsidyAmount   decimal.Decimal `json:"subsidy_amount"`    // 穿仓补贴数量
		EqSubsidyAmount decimal.Decimal `json:"eq_subsidy_amount"` // 穿仓补贴数量折合 usdt
	}
	ExcelFunding struct {
		OperateTime  string `json:"operate_time"`   // 资金费用收支时间
		UID          string `json:"uid"`            // 用户ID
		UserType     string `json:"user_type"`      // 用户类型
		FundingId    string `json:"funding_id"`     // 资金费用单号
		Direction    string `json:"direction"`      // 用户资金费用方向 1.收入，2.支出
		FundRate     string `json:"funding_rate"`   // 资金费率
		Amount       string `json:"amount"`         // 资金费用金额
		MarkPrice    string `json:"mark_price"`     // 标记价格
		Quote        string `json:"quote"`          // 计价币 结算币种
		FundPosSide  string `json:"fund_pos_side"`  // 净持仓类型
		FundPosNum   string `json:"fund_pos_num"`   // 净持仓
		AccountType  string `json:"account_type"`   // 合约类型
		Symbol       string `json:"symbol"`         // 合约代码
		MarginMode   string `json:"margin_mode"`    // 保证金模式 (1:全仓模式 2:追仓模式)
		PosSide      string `json:"pos_side"`       // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
		Pos          string `json:"pos"`            // 仓位数
		OpenPriceAvg string `json:"open_price_avg"` // 开仓均价
		Liquidation  string `json:"liquidation"`    // 预估强评价
		Margin       string `json:"margin"`         // 仓位保证金
		OpenTime     string `json:"open_time"`      // 开仓时间
	}

	ReqGetFundRate struct {
		Symbol string `form:"symbol" json:"symbol"` // 合约代码
	}
	ReqFundRate struct {
		Symbol    string          `json:"symbol"`
		FundRate  decimal.Decimal `json:"funding_rate"`
		StartTime int64           `json:"start_time"`
		EndTime   int64           `json:"end_time"`
	}

	FundRateAll struct {
		FundList  []FundRate `json:"fundList"`
		StartTime int64      `json:"start_time"`
		EndTime   int64      `json:"end_time"`
	}
	FundRate struct {
		Symbol       string          `json:"symbol"`
		ShowRank     int             `json:"show_rank"`         // 币对展示排位
		FundRate     decimal.Decimal `json:"funding_rate"`      // 实时资金费率
		LastFundRate decimal.Decimal `json:"last_funding_rate"` // 上次传结算资金费率
		LastTime     int64           `json:"last_time"`         // 上次结算时间
	}

	UserHoldPosReq struct {
		Base    string `json:"base"`     // 交易币
		Quote   string `json:"quote"`    // 计价币
		PosSide int32  `json:"pos_side"` // 仓位类型 1:多仓 2:空仓
		UID     string `json:"uid"`      // 用户ID
	}
)

func (p *UserHoldPosReq) ToUseCase() *repository.UserHoldPosReq {
	return &repository.UserHoldPosReq{
		Base:    p.Base,
		Quote:   p.Quote,
		PosSide: p.PosSide,
		UID:     p.UID,
	}
}

type (
	UserIsBurstReq struct {
		UID        string                    `json:"uid"`         // 用户ID
		MarginMode futuresassetpb.MarginMode `json:"margin_mode"` // 全仓逐仓
		PosSide    int32                     `json:"pos_side"`    // 仓位类型 1:多仓 2:空仓 3: 单向持仓
		Symbol     string                    `json:"symbol"`
		IsTrialPos bool                      `json:"is_trial_pos"`
	}
)

type IndexPrice struct {
	Platforms  []string        `json:"platforms"`
	Type       string          `json:"type"`
	IndexPrice decimal.Decimal `json:"index_price"`
}

type (
	ReqUserAssetAndPos struct {
		Asset   AssetData `json:"asset"`
		PosList []PosData `json:"posList"`
	}
	AssetData struct {
		UID           string                     `json:"uid"`           // 用户id
		Balance       map[string]decimal.Decimal `json:"balance"`       // 账户余额
		Available     decimal.Decimal            `json:"available"`     // 可用
		ProfitUnreal  decimal.Decimal            `json:"profitUnreal"`  // 总未实现盈亏
		MarginBalance decimal.Decimal            `json:"marginBalance"` // 保证金余额
		ProfitRate    decimal.Decimal            `json:"profit_rate"`   // 盈亏比例
		CnyBalance    decimal.Decimal            `json:"cny_balance"`   // cny 折合
		BtcBalance    decimal.Decimal            `json:"btc_balance"`   // btc 折合
	}
	PosData struct {
		Symbol       string          `json:"symbol"`         // 合约代码
		Currency     string          `json:"currency"`       // 币种
		UID          string          `json:"uid"`            // 用户ID
		Leverage     int             `json:"leverage"`       // 杠杆倍数
		Pos          decimal.Decimal `json:"pos"`            // 仓位数
		OpenPriceAvg decimal.Decimal `json:"open_price_avg"` // 开仓均价
		MarkPrice    decimal.Decimal `json:"mark_price"`     // 标记价格
		Liquidation  decimal.Decimal `json:"liquidation"`    // 预估强评价
		MarginMode   int32           `json:"margin_mode"`    // 仓位类型 1:全仓 2:逐仓
		Margin       decimal.Decimal `json:"margin"`         // 仓位保证金
		ProfitUnreal decimal.Decimal `json:"profit_unreal"`  // 未实现盈亏(回报率)
		RiskRate     decimal.Decimal `json:"risk_rate"`      // 风险率
		PosSide      int32           `json:"pos_side"`       // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
		AwardIds     []string        `json:"award_ids"`      // 奖励操作id
		// RivalScore   decimal.Decimal `json:"rival_score"`   // 对手方减仓指数
	}
)

// WinRateParam 用户胜率参数
type WinRateParam struct {
	Symbol string `json:"symbol"`
}

type BaseNum struct {
	BBaseNum int `json:"b_base_num"` // 币本位的基差计算周期
	UBaseNum int `json:"u_base_num"` // u本位基差计算周期
}

type UserStatistics struct {
	AccountType string `json:"account_type"` // 合约类型
}

func (p *UserStatistics) ToUseCase() *repository.UserStatistics {
	return &repository.UserStatistics{
		AccountType: p.AccountType,
	}
}

type (
	PlatDataReq struct {
		Symbol string `json:"symbol"`
	}
	PlatDataRes struct {
		Symbol     string          `json:"symbol"`
		FundRate   decimal.Decimal `json:"funding_rate"`
		ServerTime int64           `json:"server_time"` // 服务器时间戳
		SettleTime int64           `json:"settle_time"` // 下次结算时间
		IndexPrice decimal.Decimal `json:"index_price"` // 指数价格
		OpenPos    decimal.Decimal `json:"open_pos"`    // 当前为结算总持仓数
	}
)

type (
	OpenCloseTimesReq struct {
		UID string `json:"uid"`
	}

	OpenCloseTimesRes struct {
		UID        string `json:"uid"`
		OpenTimes  int    `json:"open_times"`
		CloseTimes int    `json:"close_times"`
	}
)

func (p *OpenCloseTimesReq) ToUseCase() *repository.OpenCloseTimesReq {
	return &repository.OpenCloseTimesReq{
		UID: p.UID,
	}
}
