package payload

import (
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/libs/pager"
)

type (
	// FundRateParam 资金费率记录
	FundRateParam struct {
		pager.Condition
		Symbol    string `json:"symbol" binding:"required"` // 币对
		StartTime int64  `json:"start_time"`                // 开始时间
		EndTime   int64  `json:"end_time"`                  // 结束时间
	}
	// FundingListReq 资金费用查询
	FundingListReq struct {
		pager.Condition
		AccountType string `json:"accountType"` // 合约类型
		Base        string `json:"base"`        // 交易币
		Quote       string `json:"quote"`       // 计价币
		MarginMode  int    `json:"marginMode"`  // 全仓逐仓
		PosSide     int32  `json:"posSide"`     // 仓位类型 1:多仓 2:空仓
		UID         string `json:"uid"`         // 用户ID
		UserType    []int  `json:"userType"`    // 用户类型
		FundingId   string `json:"fundingId"`   // 资金费用单号
		Direction   int    `json:"direction"`   // 用户资金费用方向 1.收入，2.支出
		StartTime   int64  `json:"startTime"`   // 资金费用收支时间开始
		EndTime     int64  `json:"endTime"`     // 资金费用收支时间结束
		IsExcel     int    `json:"isExcel"`     // 是否导出
	}
)

func (p *FundRateParam) ToUseCase() *repository.FundRateParam {
	return &repository.FundRateParam{
		Condition: pager.Condition{
			PageIndex: p.PageIndex,
			PageSize:  p.PageSize,
		},
		Symbol:    p.Symbol,
		StartTime: p.StartTime,
		EndTime:   p.EndTime,
	}
}

func (p *FundingListReq) ToUseCase() *repository.FundingFeeListParam {
	return &repository.FundingFeeListParam{
		Condition: pager.Condition{
			PageIndex: p.PageIndex,
			PageSize:  p.PageSize,
		},
		AccountType: p.AccountType,
		Base:        p.Base,
		Quote:       p.Quote,
		MarginMode:  p.MarginMode,
		PosSide:     p.PosSide,
		UID:         p.UID,
		UserType:    p.UserType,
		FundingId:   p.FundingId,
		Direction:   p.Direction,
		StartTime:   p.StartTime,
		EndTime:     p.EndTime,
	}
}
