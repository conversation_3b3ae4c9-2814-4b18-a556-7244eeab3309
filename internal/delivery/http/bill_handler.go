package http

import (
	"net/http"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type BillHandlerParam struct {
	dig.In

	BillUseCase usecase.BillUseCase
	PriceRepo   repository.PriceRepository
}

type billHandler struct {
	billUseCase usecase.BillUseCase
	priceRepo   repository.PriceRepository
}

func newBillHandler(param BillHandlerParam) *billHandler {
	return &billHandler{
		billUseCase: param.BillUseCase,
		priceRepo:   param.PriceRepo,
	}
}

func (handler *billHandler) BillAsset(c *gin.Context) {
	var req payload.BillAssetParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	result, err := handler.billUseCase.BillAsset(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"req": req,
			"err": err,
		}).Error("billUseCase.BillAsset")

		c.JSON(http.StatusOK, response.NewError(domain.InternalError, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(result))
}
