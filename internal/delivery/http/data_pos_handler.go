package http

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

// QueryUserPos 运营后台-用户持仓查询
func (handler *dataHandler) QueryUserPos(c *gin.Context) {
	var req payload.UserPosParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	pos, err := handler.positionUseCase.QueryUserPos(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSO<PERSON>(http.StatusOK, response.NewSuccess(pos))
}

func (handler *dataHandler) PosInfo(c *gin.Context) {
	var req payload.UserPosParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	if len(req.Base) == 0 || len(req.Quote) == 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("base or quote is empty")))

		return
	}

	pos, err := handler.positionUseCase.PosInfo(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(pos))
}

func (handler *dataHandler) FundRateList(c *gin.Context) {
	var req payload.FundRateParam
	if err := request.ShouldBindJSON(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	fundRateRes, err := handler.fundingUseCase.GetFundRateList(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(fundRateRes))
}

func (handler *dataHandler) FundRateListMobile(c *gin.Context) {
	var req payload.FundRateParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	req.Condition.PageIndex = 1
	req.Condition.PageSize = 33
	req.EndTime = time.Unix(req.EndTime, 0).Truncate(time.Hour).Unix()
	if req.StartTime > 0 {
		req.StartTime = time.Now().Truncate(time.Hour*24).AddDate(0, 0, -10).Unix()
	} else {
		req.StartTime = time.Unix(req.EndTime, 0).Truncate(time.Hour*24).AddDate(0, 0, -10).Unix()
	}

	mobileFundRateRes := make([][]entity.LogFundingRate, 0)

	// 如果结束时间在3个月之前直接返回
	limitDate := time.Now().Truncate(time.Hour*24).AddDate(0, -3, 0)
	if time.Unix(req.EndTime, 0).Truncate(time.Hour * 24).Before(limitDate) {
		c.JSON(http.StatusOK, mobileFundRateRes)

		return
	}
	// 如果开始时间在3个月之前直接替换开始时间
	if time.Unix(req.StartTime, 0).Truncate(time.Hour * 24).Before(limitDate) {
		req.StartTime = limitDate.Unix()
	}

	fundRateRes, err := handler.fundingUseCase.GetFundRateList(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	lastDateStr := ""
	cstSh, _ := time.LoadLocation("Asia/Shanghai")
	tempList := make([]entity.LogFundingRate, 0)
	for _, mobileFundRate := range fundRateRes.List {
		tempTimeObj := time.Unix(mobileFundRate.FundingTime, 0).In(cstSh)
		tempDateStr := tempTimeObj.Format("2006-01-02")
		if lastDateStr != tempDateStr {
			if len(lastDateStr) != 0 {
				mobileFundRateRes = append(mobileFundRateRes, tempList)
			}
			lastDateStr = tempDateStr
			tempList = make([]entity.LogFundingRate, 0)
		}
		tempList = append(tempList, mobileFundRate)
	}

	c.JSON(http.StatusOK, response.NewSuccess(mobileFundRateRes))
}

func (handler *dataHandler) PlatPosList(c *gin.Context) {
	data, err := handler.positionUseCase.PlatPosList(c.Request.Context())
	if err != nil {
		logrus.Error(fmt.Sprintf("Plat Pos List err:%+v", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) PlatPosDetail(c *gin.Context) {
	var req repository.PlatPosDetailReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	if req.Base == "" || req.Quote == "" {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("base or quote is empty")))

		return
	}

	data, err := handler.positionUseCase.PlatPosDetail(c.Request.Context(), &req)
	if err != nil {
		logrus.Error(fmt.Sprintf("Plat Pos Detail err:%+v", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) PageFundingFee(c *gin.Context) {
	var req payload.FundingListReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	if req.PageSize*req.PageIndex == 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("page size or page num is empty")))

		return
	}

	data, err := handler.fundingUseCase.FundingFeeList(c.Request.Context(), req.ToUseCase())
	if err != nil {
		logrus.Error(fmt.Sprintf("Plat Pos List err:%+v", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	for i := 0; i < len(data.List); i++ {
		data.List[i].OperateTime = data.List[i].OperateTime / 1e9
		data.List[i].FundingRate = data.List[i].FundingRate.Mul(decimal.NewFromInt(100))
		json.Unmarshal([]byte(data.List[i].StrPos), &data.List[i].PosData)
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) FundRateAll(c *gin.Context) {
	data, err := handler.fundingUseCase.FundRateAll(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.InternalError, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) FetchUserHoldModeAndPos(c *gin.Context) {
	var param payload.UserHoldPosReq
	if err := request.ShouldBindQuery(c, &param); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	holdPos, err := handler.positionUseCase.UserHoldPos(c.Request.Context(), param.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(holdPos))
}
