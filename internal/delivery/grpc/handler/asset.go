package handler

import (
	"context"

	"futures-asset/internal/delivery/grpc/response"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	"yt.com/backend/common.git/transport/grpcinit"
)

type NewAssetHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
	BurstRepo    repository.BurstRepository
}

type AssetHandler struct {
	assetUseCase usecase.AssetUseCase
	burstRepo    repository.BurstRepository

	futuresassetpb.FuturesAssetServiceServer
}

func NewAssetHandler(param NewAssetHandlerParam) *AssetHandler {
	return &AssetHandler{
		assetUseCase: param.AssetUseCase,
		burstRepo:    param.BurstRepo,
	}
}

func (h *Asset<PERSON>andler) LockAsset(ctx context.Context, req *futuresassetpb.BatchLockAssetRequest) (*futuresassetpb.BatchAssetOperationResponse, error) {
	orders := make([]*payload.LockAssetOrder, 0, len(req.Orders))
	for _, order := range req.Orders {
		amount, _ := decimal.NewFromString(order.Amount)
		orders = append(orders, &payload.LockAssetOrder{
			OrderID:   order.OrderId,
			Amount:    amount,
			OrderTime: order.OrderTime,
			AwardIds:  order.AwardIds,
			AwardUsed: order.AwardUsed,
		})
	}

	param := &payload.BatchLockAssetParam{
		UID:          req.Uid,
		UserType:     req.UserType,
		Symbol:       req.Symbol,
		Currency:     req.Currency,
		Leverage:     req.Leverage,
		IsInnerCall:  req.IsInnerCall,
		PosSide:      req.PosSide,
		PositionMode: req.PositionMode,
		MarginMode:   req.MarginMode,
		Orders:       orders,
		IsTrial:      req.IsTrial,
	}

	reply := &futuresassetpb.BatchAssetOperationResponse{
		Uid:        req.Uid,
		Symbol:     req.Symbol,
		ResultCode: futuresassetpb.BatchResultCode_ALL_FAILED,
	}

	items, err := h.assetUseCase.LockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"err": err,
		}).Error("batchLockAsset")

		return reply, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	reply.ResultCode = futuresassetpb.BatchResultCode_ALL_SUCCESS
	reply.Results = items
	for _, item := range reply.Results {
		if !item.Success {
			reply.ResultCode = futuresassetpb.BatchResultCode_PARTIAL_SUCCESS
			break
		}
	}

	return reply, nil
}

func (h *AssetHandler) UnLockAsset(ctx context.Context, req *futuresassetpb.BatchUnLockAssetRequest) (*futuresassetpb.BatchAssetOperationResponse, error) {
	param := &payload.BatchUnlockParam{
		UID:          req.Uid,
		UserType:     req.UserType,
		Symbol:       req.Symbol,
		Currency:     req.Currency,
		MarginMode:   req.MarginMode,
		PositionMode: req.PositionMode,
		Orders:       []*payload.UnLockParam{},
		IsTrial:      req.IsTrial,
	}
	_, err := h.assetUseCase.UnLockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.BatchAssetOperationResponse{}, nil
}

func (h *AssetHandler) UserAsset(ctx context.Context, req *futuresassetpb.BatchAssetRequest) (*futuresassetpb.BatchAssetResponse, error) {
	reply := &futuresassetpb.BatchAssetResponse{
		BatchAsset: make(map[string]*futuresassetpb.BatchAsset),
	}

	for _, uid := range req.Uids {
		asset, err := h.assetUseCase.AssetDetail(ctx, usecase.ReqAssetDetail{
			UID: uid,
		})
		if err != nil {
			continue
		}

		assets := make([]*futuresassetpb.Asset, 0)
		for _, item := range asset.AssetList {
			assets = append(assets, &futuresassetpb.Asset{
				Currency: item.Currency,
				Balance:  item.Balance.String(),
			})
		}

		positions := make([]*futuresassetpb.Position, 0)
		for _, pos := range asset.PosList {
			positions = append(positions, &futuresassetpb.Position{
				Currency:        pos.Currency,
				IsolatedMargin:  pos.IsolatedMargin.String(),
				TrialMargin:     pos.TrialMargin.String(),
				Leverage:        int32(pos.Leverage),
				Liquidation:     pos.Liquidation.String(),
				MarginMode:      futuresassetpb.MarginMode(pos.MarginMode),
				OpenPriceAvg:    pos.OpenPriceAvg.String(),
				OpenTime:        pos.OpenTime,
				Pos:             pos.Pos.String(),
				PosAvailable:    pos.PosAvailable.String(),
				PosSide:         futuresassetpb.PosSide(pos.PosSide),
				PosValue:        pos.PosValue.String(),
				ProfitReal:      pos.ProfitReal.String(),
				ProfitUnreal:    pos.ProfitUnreal.String(),
				Symbol:          pos.Symbol,
				PosId:           pos.PosId,
				Uid:             pos.UID,
				UserType:        futuresassetpb.UserType(pos.UserType),
				PosStatus:       futuresassetpb.PosStatus(pos.PosStatus),
				Subsidy:         pos.Subsidy.String(),
				RebornId:        pos.RebornId,
				LiquidationType: futuresassetpb.LiquidationType(pos.LiquidationType),
				AwardIds:        pos.AwardIds,
				MarkPrice:       pos.MarkPrice.String(),
			})
		}

		frozen := make(map[string]string)
		for key, value := range asset.Frozen {
			frozen[key] = value.String()
		}

		reply.BatchAsset[uid] = &futuresassetpb.BatchAsset{
			AssetMode: asset.AssetMode,
			Assets:    assets,
			Frozen:    frozen,
			Positions: positions,
			Uid:       uid,
		}
	}

	return reply, nil
}

func (h *AssetHandler) LockPosition(ctx context.Context, req *futuresassetpb.LockPositionRequest) (*futuresassetpb.LockPositionResponse, error) {
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	param := &payload.LockPositionParam{
		LockPositionRequest: req,
		Amount:              amount,
	}

	reply := &futuresassetpb.LockPositionResponse{
		Code: int32(domain.CodeOk.ToInt()),
	}

	lockReply, err := h.assetUseCase.LockPosition(ctx, param.ToUseCaseParam())
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"err": err,
		}).Error("assetUseCase.LockPosition")

		return reply, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	reply.Amount = lockReply.Amount.String()
	reply.IsTrial = lockReply.IsTrial

	return reply, nil
}
