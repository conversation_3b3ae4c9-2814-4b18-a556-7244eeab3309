package subscriber

import (
	"encoding/json"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type FundingFeeSubscriberParam struct {
	dig.In

	BillRepository repository.BillRepository
}

type FundingFeeSubscriber struct {
	billRepo repository.BillRepository
}

func NewFundingFeeHandler(param FundingFeeSubscriberParam) *FundingFeeSubscriber {
	handle := &FundingFeeSubscriber{
		billRepo: param.BillRepository,
	}

	return handle
}

func (handle *FundingFeeSubscriber) Handle(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim, msg *sarama.ConsumerMessage) {
	// 提交消費完畢
	defer session.MarkMessage(msg, "")

	var events []*entity.BillAsset
	err := json.Unmarshal(msg.Value, &events)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"key":       msg.Key,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"msg":       string(msg.Value),
			"err":       err,
			"type":      "json.Unmarshal",
		}).Error("FundingFeeSubscriber.Handle")

		return
	}

	// ctx, cancel := context.WithTimeout(context.Background(), handlerTimeout)
	// defer cancel()
	//
	// // TODO 做批量插入
	// for _, event := range events {
	// 	err := handle.billRepo.InsertAsset(ctx, event)
	// 	if err != nil {
	// 		logrus.WithFields(logrus.Fields{
	// 			"err":   err,
	// 			"event": fmt.Sprintf("%+v", event),
	// 		}).Error("billRepo.InsertAsset")
	// 	}
	// }
}
