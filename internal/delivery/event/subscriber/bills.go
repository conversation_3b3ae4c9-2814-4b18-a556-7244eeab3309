package subscriber

import (
	"context"
	"encoding/json"
	"fmt"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
)

type BillSubscriberParam struct {
	dig.In

	BillRepository repository.BillRepository
}

type BillSubscriber struct {
	billRepo repository.BillRepository
}

func NewBillHandler(param BillSubscriberParam) *BillSubscriber {
	handle := &BillSubscriber{
		billRepo: param.BillRepository,
	}

	return handle
}

func (handle *BillSubscriber) Handle(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim, msg *sarama.ConsumerMessage) {
	// 提交消費完畢
	defer session.MarkMessage(msg, "")

	var events []*entity.BillAsset
	err := json.Unmarshal(msg.Value, &events)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"topic":     msg.Topic,
			"key":       msg.Key,
			"partition": msg.Partition,
			"offset":    msg.Offset,
			"msg":       string(msg.Value),
			"err":       err,
			"type":      "json.Unmarshal",
		}).Error("BillSubscriber.Handle")

		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), handlerTimeout)
	defer cancel()

	// TODO 做批量插入
	for _, event := range events {
		err := handle.billRepo.InsertAsset(ctx, event)
		if err != nil {
			logrus.WithFields(logrus.Fields{
				"err":   err,
				"event": fmt.Sprintf("%+v", event),
			}).Error("billRepo.InsertAsset")
		}
	}
}
