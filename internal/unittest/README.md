# PositionUseCase 单元测试框架

本目录包含了 `internal/usecase/position.go` 中 `PositionUseCase` 的单元测试框架。

## 框架特性

- 使用 `go.uber.org/mock/gomock` 进行 mock 对象管理
- 使用 `github.com/stretchr/testify/suite` 进行测试套件管理
- 已经为 PositionUseCase 的所有依赖创建了 mock 对象
- 提供了完整的测试方法框架，覆盖所有 PositionUseCase 接口方法

## 文件结构

```
internal/unittest/
├── position_test.go    # PositionUseCase 的单元测试
└── README.md          # 本说明文档
```

## 已实现的测试方法

### 查询类方法
- `TestUserPos` - 测试用户仓位查询（已实现示例）
- `TestQueryUserPos` - 测试查询用户仓位
- `TestPosInfo` - 测试仓位信息查询
- `TestPosTotal` - 测试仓位总量查询（已实现示例）
- `TestUserHoldPos` - 测试用户持仓查询
- `TestPlatPosList` - 测试平台仓位列表查询
- `TestPlatPosDetail` - 测试平台仓位详情查询

### 开仓操作测试
- `TestOpenLongPos` - 测试开多仓
- `TestOpenShortPos` - 测试开空仓
- `TestOpenBothPos` - 测试开单向仓

### 平仓操作测试
- `TestCloseLongPos` - 测试平多仓
- `TestCloseShortPos` - 测试平空仓
- `TestCloseBothPos` - 测试平单向仓

## Mock 对象

框架已经创建了以下 mock 对象：

- `MockPositionRepository` - 仓位仓库 mock
- `MockCacheRepository` - 缓存仓库 mock
- `MockAssetRepository` - 资产仓库 mock
- `MockPriceRepository` - 价格仓库 mock

## 辅助方法

- `createMockUserAsset()` - 创建模拟用户资产对象
- `createMockAccountSettleParam()` - 创建模拟账户结算参数

## 运行测试

```bash
# 运行所有测试
go test ./internal/unittest -v

# 运行特定测试
go test ./internal/unittest -v -run TestPosTotal

# 运行测试套件
go test ./internal/unittest -v -run TestPositionUseCaseTestSuite
```

## 示例测试用例

### 简单查询测试示例（TestPosTotal）

```go
func (suite *PositionUseCaseTestSuite) TestPosTotal() {
    // 准备测试数据
    contractCode := "BTC-USDT"
    expectedTotal := decimal.NewFromFloat(100.5)

    // 设置 mock 期望
    suite.mockPositionRepo.EXPECT().
        PosTotal(suite.ctx, contractCode).
        Return(expectedTotal).
        Times(1)

    // 执行测试
    result := suite.positionUseCase.PosTotal(suite.ctx, contractCode)

    // 验证结果
    suite.Equal(expectedTotal, result, "仓位总量应该匹配期望值")
}
```

### 复杂查询测试示例（TestUserPos）

```go
func (suite *PositionUseCaseTestSuite) TestUserPos() {
    // 准备测试数据
    req := &repository.SwapParam{
        UID:    "test_user_123",
        Symbol: "BTC-USDT",
    }

    expectedPositions := []repository.PosSwap{
        {
            UID:      "test_user_123",
            Symbol:   "BTC-USDT",
            Pos:      decimal.NewFromFloat(1.5),
            PosSide:  1, // 多仓
            Currency: "USDT",
        },
    }

    // 设置 mock 期望
    suite.mockPositionRepo.EXPECT().
        UserPos(suite.ctx, req).
        Return(expectedPositions, nil).
        Times(1)

    // 执行测试
    result, err := suite.positionUseCase.UserPos(suite.ctx, req)

    // 验证结果
    suite.NoError(err, "不应该有错误")
    suite.Equal(len(expectedPositions), len(result), "返回的仓位数量应该匹配")
}
```

## 下一步工作

1. 为每个 TODO 标记的测试方法实现具体的测试用例
2. 根据业务需求添加边界条件测试
3. 添加错误场景测试
4. 完善 `createMockAccountSettleParam` 方法，支持创建完整的账户结算参数

## 注意事项

- 所有测试方法目前都标记为 TODO，需要根据具体业务逻辑实现
- Mock 对象的期望设置需要根据实际的业务流程来配置
- 测试数据应该覆盖各种边界条件和异常情况
- 建议为复杂的业务逻辑（如开仓、平仓）编写多个测试用例覆盖不同场景
