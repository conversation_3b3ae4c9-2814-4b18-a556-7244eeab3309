package unittest

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"testing"

	"futures-asset/configs"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	mockRepo "futures-asset/internal/mock/repository"
	usecaseImpl "futures-asset/internal/usecase"
	"futures-asset/internal/utils"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
	cfg "yt.com/backend/common.git/config"

	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

// PositionUseCaseTestSuite 仓位用例测试套件
type PositionUseCaseTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller

	// Mock repositories
	mockPositionRepo *mockRepo.MockPositionRepository
	mockCacheRepo    *mockRepo.MockCacheRepository
	mockAssetRepo    *mockRepo.MockAssetRepository
	mockPriceRepo    *mockRepo.MockPriceRepository

	// Use case under test
	positionUseCase usecase.PositionUseCase

	// Test context
	ctx context.Context
}

// SetupSuite 设置测试套件
func (suite *PositionUseCaseTestSuite) SetupSuite() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.ctx = context.Background()

	// 创建 mock repositories
	suite.mockPositionRepo = mockRepo.NewMockPositionRepository(suite.ctrl)
	suite.mockCacheRepo = mockRepo.NewMockCacheRepository(suite.ctrl)
	suite.mockAssetRepo = mockRepo.NewMockAssetRepository(suite.ctrl)
	suite.mockPriceRepo = mockRepo.NewMockPriceRepository(suite.ctrl)

	// 创建配置
	config := &cfg.Config[configs.Config]{}

	// 创建 PositionUseCase 实例
	param := usecaseImpl.PositionUseCaseParam{
		Config:       config,
		PositionRepo: suite.mockPositionRepo,
		CacheRepo:    suite.mockCacheRepo,
		AssetRepo:    suite.mockAssetRepo,
	}

	suite.positionUseCase = usecaseImpl.NewPositionUseCase(param)
}

// TearDownSuite 清理测试套件
func (suite *PositionUseCaseTestSuite) TearDownSuite() {
	suite.ctrl.Finish()
}

// TestPositionUseCaseTestSuite 运行测试套件
func TestPositionUseCaseTestSuite(t *testing.T) {
	suite.Run(t, new(PositionUseCaseTestSuite))
}

// ==================== 查询类方法测试 ====================

// TestUserPos 测试用户仓位查询
func (suite *PositionUseCaseTestSuite) TestUserPos() {
	// 准备测试数据
	req := &repository.SwapParam{
		UID:    "test_user_123",
		Symbol: "BTC-USDT",
	}

	expectedPositions := []repository.PosSwap{
		{
			UID:      "test_user_123",
			Symbol:   "BTC-USDT",
			Pos:      decimal.NewFromFloat(1.5),
			PosSide:  1, // 多仓
			Currency: "USDT",
		},
		{
			UID:      "test_user_123",
			Symbol:   "BTC-USDT",
			Pos:      decimal.NewFromFloat(0.8),
			PosSide:  2, // 空仓
			Currency: "USDT",
		},
	}

	// 设置 mock 期望
	suite.mockPositionRepo.EXPECT().
		UserPos(suite.ctx, req).
		Return(expectedPositions, nil).
		Times(1)

	// 执行测试
	result, err := suite.positionUseCase.UserPos(suite.ctx, req)

	// 验证结果
	suite.NoError(err, "靠,不应该有错误")
	suite.Equal(len(expectedPositions), len(result), "返回的仓位数量应该匹配")
	suite.Equal(expectedPositions[0].UID, result[0].UID, "第一个仓位的用户ID应该匹配")
	suite.Equal(expectedPositions[0].Pos, result[0].Pos, "第一个仓位的数量应该匹配")
}

// TestQueryUserPos 测试查询用户仓位
func (suite *PositionUseCaseTestSuite) TestQueryUserPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestQueryUserPos - 待实现")
}

// TestPosInfo 测试仓位信息查询
func (suite *PositionUseCaseTestSuite) TestPosInfo() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPosInfo - 待实现")
}

// TestPosTotal 测试仓位总量查询
func (suite *PositionUseCaseTestSuite) TestPosTotal() {
	// 准备测试数据
	contractCode := "BTC-USDT"
	expectedTotal := decimal.NewFromFloat(100.5)

	// 设置 mock 期望
	suite.mockPositionRepo.EXPECT().
		PosTotal(suite.ctx, contractCode).
		Return(expectedTotal).
		Times(1)

	// 执行测试
	result := suite.positionUseCase.PosTotal(suite.ctx, contractCode)

	// 验证结果
	suite.Equal(expectedTotal, result, "仓位总量应该匹配期望值")
}

// TestUserHoldPos 测试用户持仓查询
func (suite *PositionUseCaseTestSuite) TestUserHoldPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestUserHoldPos - 待实现")
}

// TestPlatPosList 测试平台仓位列表查询
func (suite *PositionUseCaseTestSuite) TestPlatPosList() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPlatPosList - 待实现")
}

// TestPlatPosDetail 测试平台仓位详情查询
func (suite *PositionUseCaseTestSuite) TestPlatPosDetail() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestPlatPosDetail - 待实现")
}

// ==================== 开仓操作测试 ====================

// TestOpenLongPos 测试开多仓
func (suite *PositionUseCaseTestSuite) TestOpenLongPos() {
	// 创建测试数据
	userAsset, err := suite.createMockUserAsset("/Users/<USER>/GitTop/futures-asset/internal/unittest/user_asset.json")
	suite.NoError(err, "创建用户资产数据应该成功")

	accountSettleParam, err := suite.createMockAccountSettleParam("/Users/<USER>/GitTop/futures-asset/internal/unittest/account_msg.json")
	suite.NoError(err, "创建账户结算参数应该成功")

	// 创建 mock PriceRepository
	mockPriceRepo := mockRepo.NewMockPriceRepository(suite.ctrl)

	// 设置 mock 期望 - SpotURate 方法用于获取币种汇率
	mockPriceRepo.EXPECT().
		SpotURate(gomock.Any(), "USDT").
		Return(decimal.NewFromInt(1)).
		AnyTimes()

	// 设置 mock 期望 - GetMarkPrice 方法用于获取标记价格
	mockPriceRepo.EXPECT().
		GetMarkPrice(gomock.Any(), "BTC-USDT").
		Return(decimal.NewFromFloat(1000)).
		AnyTimes()

	// 设置 AssetRepository mock 期望 - TotalJoinBalance 方法
	suite.mockAssetRepo.EXPECT().
		TotalJoinBalance(gomock.Any(), gomock.Any()).
		Return(decimal.NewFromFloat(10000), nil).
		AnyTimes()

	// 设置 CacheRepository mock 期望 - UpdateLongPos 方法
	suite.mockCacheRepo.EXPECT().
		UpdateLongPos(gomock.Any(), "BTC-USDT", userAsset).
		Return(nil).
		Times(1)

	// 准备变更日志
	changelog := make([]string, 0)

	// 执行开多仓操作
	err = suite.positionUseCase.OpenLongPos(userAsset, mockPriceRepo, accountSettleParam, &changelog)

	// 验证结果
	suite.NoError(err, "开多仓操作应该成功")
	suite.True(len(changelog) > 0, "应该有变更日志记录")

	// 验证仓位数据变更
	expectedAmount, _ := decimal.NewFromString("0.0002")
	suite.Equal(expectedAmount, userAsset.LongPos.Pos, "多仓仓位数量应该正确")
	suite.Equal(expectedAmount, userAsset.LongPos.PosAvailable, "多仓可平仓数量应该正确")

	// 验证开仓均价 - 使用字符串比较避免精度问题
	suite.Equal("1000", userAsset.LongPos.OpenPriceAvg.String(), "开仓均价应该正确")
	suite.Equal("BTC-USDT", userAsset.LongPos.Symbol, "合约代码应该正确")
	suite.Equal("USDT", userAsset.LongPos.Currency, "币种应该正确")
	suite.Equal(int32(1), userAsset.LongPos.PosSide, "仓位方向应该是多仓")

	// 验证日志内容
	suite.Contains(changelog[0], "asset:pos amount:0.0002 from:OpenLongPos()", "应该包含仓位变更日志")
	suite.True(len(changelog) >= 4, "应该有至少4条变更日志：仓位变更、解冻保证金、扣除手续费、逐仓保证金")

	// 验证各种日志类型
	logTypes := []string{"asset:pos", "asset:margin", "asset:balance"}
	for _, logType := range logTypes {
		found := false
		for _, log := range changelog {
			if strings.Contains(log, logType) {
				found = true
				break
			}
		}
		suite.True(found, fmt.Sprintf("应该包含%s类型的日志", logType))
	}

	suite.T().Logf("开多仓测试完成，变更日志: %v", changelog)
}

// TestOpenShortPos 测试开空仓
func (suite *PositionUseCaseTestSuite) TestOpenShortPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestOpenShortPos - 待实现")
}

// TestOpenBothPos 测试开单向仓
func (suite *PositionUseCaseTestSuite) TestOpenBothPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestOpenBothPos - 待实现")
}

// ==================== 平仓操作测试 ====================

// TestCloseLongPos 测试平多仓
func (suite *PositionUseCaseTestSuite) TestCloseLongPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseLongPos - 待实现")
}

// TestCloseShortPos 测试平空仓
func (suite *PositionUseCaseTestSuite) TestCloseShortPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseShortPos - 待实现")
}

// TestCloseBothPos 测试平单向仓
func (suite *PositionUseCaseTestSuite) TestCloseBothPos() {
	// TODO: 实现具体测试用例
	suite.T().Log("TestCloseBothPos - 待实现")
}

// ==================== 辅助方法 ====================

// createMockUserAsset 创建模拟用户资产
func (suite *PositionUseCaseTestSuite) createMockUserAsset(jsonFilePath string) (*repository.AssetSwap, error) {
	content, err := os.ReadFile(jsonFilePath)
	if err != nil {
		return &repository.AssetSwap{}, fmt.Errorf("failed to read file %s: %w", jsonFilePath, err)
	}

	var assetSwap repository.AssetSwap
	err = json.Unmarshal(content, &assetSwap)
	if err != nil {
		return &repository.AssetSwap{}, fmt.Errorf("failed to unmarshal JSON from file %s: %w", jsonFilePath, err)
	}

	// return &repository.AssetSwap{
	// 	UID: "test_user_123",
	// 	LongPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "long_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        1, // 多仓
	// 	},
	// 	ShortPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "short_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        2, // 空仓
	// 	},
	// 	BothPos: repository.PosSwap{
	// 		UID:            "test_user_123",
	// 		PosId:          "both_pos_123",
	// 		Symbol:         "BTC-USDT",
	// 		Currency:       "USDT",
	// 		Pos:            decimal.Zero,
	// 		PosAvailable:   decimal.Zero,
	// 		OpenPriceAvg:   decimal.Zero,
	// 		IsolatedMargin: decimal.Zero,
	// 		Leverage:       10,
	// 		MarginMode:     1, // 逐仓
	// 		PosSide:        3, // 单向仓
	// 	},
	// 	Balance: map[string]decimal.Decimal{
	// 		"USDT": decimal.NewFromFloat(10000), // 发你 10000 USDT
	// 	},
	// 	Frozen: map[string]decimal.Decimal{
	// 		"USDT": decimal.Zero,
	// 	},
	// }

	return &assetSwap, nil
}

// createMockAccountSettleParam 创建模拟账户结算参数
func (suite *PositionUseCaseTestSuite) createMockAccountSettleParam(jsonFilePath string) (utils.AccountSettleParam, error) {
	content, err := os.ReadFile(jsonFilePath)
	if err != nil {
		return utils.AccountSettleParam{}, fmt.Errorf("failed to read file %s: %w", jsonFilePath, err)
	}

	var accountSettleMsg futuresEnginePB.AccountSettleEngine
	err = json.Unmarshal(content, &accountSettleMsg)
	if err != nil {
		return utils.AccountSettleParam{}, fmt.Errorf("failed to unmarshal JSON from file %s: %w", jsonFilePath, err)
	}

	return utils.AccountSettleParam{
		AccountSettle: &accountSettleMsg,
	}, nil
}
