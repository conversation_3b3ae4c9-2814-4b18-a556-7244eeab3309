version: "2"
run:
  issues-exit-code: 2
  tests: false
linters:
  default: none
  enable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - contextcheck
    - decorder
    - dogsled
    - dupword
    - durationcheck
    - errcheck
    - errchkjson
    - errname
    - errorlint
    - exhaustive
    - forbidigo
    - forcetypeassert
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - goheader
    - gomoddirectives
    - goprintffuncname
    - gosec
    - govet
    - grouper
    - importas
    - ineffassign
    - interfacebloat
    - lll
    - loggercheck
    - maintidx
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    - nilnil
    - nlreturn
    - noctx
    - nolintlint
    - nonamedreturns
    - nosprintfhostport
    - paralleltest
    - prealloc
    - predeclared
    - promlinter
    - reassign
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - testableexamples
    - thelper
    - tparallel
    - unconvert
    - unparam
    - unused
    - usestdlibvars
    - wastedassign
    - whitespace
  settings:
    gocognit:
      min-complexity: 40
    lll:
      line-length: 200
      tab-width: 1
    nestif:
      min-complexity: 10
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - contextcheck
          - cyclop
          - dupl
          - err113
          - errcheck
          - funlen
          - gocognit
          - goconst
          - gocritic
          - interfacer
          - lll
          - wrapcheck
        path: _test\.go
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gofmt
    - goimports
  settings:
    gci:
      sections:
        - standard
        - default
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
